VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} repMemberFoodReport 
   Caption         =   "Point_of_Sale_System - repMemberFoodReport (ActiveReport)"
   ClientHeight    =   10815
   ClientLeft      =   0
   ClientTop       =   285
   ClientWidth     =   15360
   _ExtentX        =   27093
   _ExtentY        =   19076
   SectionData     =   "repMemberFoodReport.dsx":0000
End
Attribute VB_Name = "repMemberFoodReport"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Private Sub Detail_Format()
    
    txtTotalBillAmount.DataValue = txtItemsAmount.DataValue + txtTaxAmount.DataValue - txtDiscountAmount.DataValue

End Sub

Private Sub MemberFooter_Format()
    
    txtMemberTotalBillAmount.DataValue = txtMemberItemsAmount.DataValue + txtMemberTaxAmount.DataValue - txtMemberDiscountAmount.DataValue

End Sub

Private Sub MemberHeader_Format()
    
    TOC.Add txtFamilyCode.Text & "\" & txtMemberCode.Text & "  " & txtMemberName.Text

End Sub

Private Sub ReportFooter_Format()
    
    txtGrandTotalBillAmount.DataValue = txtTotalItemsAmount.DataValue + txtTotalTaxAmount.DataValue - txtTotalDiscountAmount.DataValue

End Sub
