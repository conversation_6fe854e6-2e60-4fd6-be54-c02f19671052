Attribute VB_Name = "win98"
Option Explicit
DefBool F: DefLng G-L: DefStr M-Z

'Download Winio from http://www.internals.com/
'Find SMARTVSD.VXD on system folder of Windows 95 OSR2

Private Declare Function GetPortVal Lib "WinIo.dll" (ByVal PortAddr As Integer, ByRef PortVal <PERSON>, ByVal bSize As Byte) As Boolean
Private Declare Function SetPortVal Lib "WinIo.dll" (ByVal PortAddr As Integer, ByVal PortVal As Long, ByVal bSize As Byte) As Boolean
Private Declare Function InitializeWinIo Lib "WinIo.dll" () As Boolean
Private Declare Function ShutdownWinIo Lib "WinIo.dll" () As Boolean

'Work well in Windows 95/98/Me
'Also work on Windows NT/2000/Xp but may clash sometimes

Function Hd98(ByVal j1, s1, s2) As Boolean
Dim i1, i2, i3, f1, k1(1 To 50)
            
    Call InitializeWinIo 'Need for Win NT/2000/Xp

Const HDC_DATA = &H1F0
Const HDC_SDH = &H1F6
Const HDC_STATUS = &H1F7
Const HDC_COMMAND = &H1F7
Const HDC_COMMAND_READPAR = &HEC
Const HDC_STATUS_BUSY = &H80
Const HDC_FIXED_RESET = &H4

    Select Case j1
    Case 0: i2 = &H1F0 'Primary
    Case 1: i2 = &H1F0 'Primary
    Case 2: i2 = &H170 'Secondary
    Case 3: i2 = &H170 'Secondary
    End Select

    If (j1 Mod 2) = 0 Then
        f1 = SetPortVal(i2 + 6, &HA0, 1) 'Master
    Else
        f1 = SetPortVal(i2 + 6, &HB0, 1) 'Slave
    End If
    f1 = SetPortVal(i2 + 7, &HEC, 1) 'Send command to Hd

    'Wait until Hd ready
    Do
        f1 = GetPortVal(i2 + 7, i1, 2)
        If (i1 And &H1) = &H1 Then i1 = 255: Exit Do 'error
        If (i1 Mod 255) = 0 Then i1 = 255: Exit Do 'error
        If (i1 And &H80) = 0 Then Exit Do 'Ok
    Loop
    s1 = "": s2 = ""
    If i1 = 255 Then Hd98 = False: GoTo ooen
   
    'Reading
    'i2 is BaseAddress
    For i1 = 1 To 50
        f1 = GetPortVal(i2, k1(i1), 2)
    Next
    
    For i1 = 1 To 50
        i3 = k1(i1) Mod 256
        s2 = s2 + Chr((k1(i1) - i3) / 256)
        s2 = s2 + Chr(i3)
    Next
    s1 = Mid(s2, 55, 40) 'Model
    s2 = Mid(s2, 21, 20) 'Serial Number
    
    f1 = SetPortVal(i2 + 7, &H4, 1) 'Clear state Hd
    Hd98 = True
ooen:
    Call ShutdownWinIo 'Need for Win NT/2000/Xp
End Function
