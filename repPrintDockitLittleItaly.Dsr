VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} repPrintDockitLittleItaly 
   Caption         =   "Point_of_Sale_System - repPrintDockitLittleItaly (ActiveReport)"
   ClientHeight    =   10620
   ClientLeft      =   -15
   ClientTop       =   480
   ClientWidth     =   15240
   _ExtentX        =   26882
   _ExtentY        =   18733
   SectionData     =   "repPrintDockitLittleItaly.dsx":0000
End
Attribute VB_Name = "repPrintDockitLittleItaly"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
Dim DiscountAmount As String
Dim SalesTaxAmount As String

Private Sub Detail_Format()
    
    If txtItemQuantity.DataValue = 0 Then
        txtItemTotal.Text = "Free"
    End If
    
End Sub

Private Sub DockitFooter_Format()
    
    txtGrandTotal.DataValue = Val(txtSumOfAmount.DataValue) + Val(txtTaxAmount.DataValue) - Val(txtDiscountAmount.DataValue)

End Sub
