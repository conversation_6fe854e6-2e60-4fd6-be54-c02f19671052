VERSION 5.00
Object = "{CDF3B183-D408-11CE-AE2C-0080C786E37D}#3.0#0"; "Edt32x30.ocx"
Object = "{B02F3647-766B-11CE-AF28-C3A2FBE76A13}#3.0#0"; "SPR32X30.OCX"
Begin VB.Form frmRightsBrowser 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "Rights Browser"
   ClientHeight    =   3900
   ClientLeft      =   4065
   ClientTop       =   2925
   ClientWidth     =   5640
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmRightsBrowser.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   3900
   ScaleWidth      =   5640
   ShowInTaskbar   =   0   'False
   Begin EditLib.fpBoolean chkSelectAll 
      Height          =   300
      Left            =   3795
      TabIndex        =   5
      Top             =   3450
      Width           =   1410
      _Version        =   196608
      _ExtentX        =   2487
      _ExtentY        =   529
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   0   'False
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -**********
      ThreeDInsideShadowColor=   -**********
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -**********
      ThreeDOutsideShadowColor=   -**********
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   0
      BorderColor     =   -**********
      BorderWidth     =   1
      AutoToggle      =   -1  'True
      BooleanStyle    =   0
      ToggleFalse     =   ""
      TextFalse       =   "Select All"
      BooleanPicture  =   2
      AlignPictureH   =   2
      AlignPictureV   =   1
      GroupId         =   0
      GroupTag        =   0
      GroupSelect     =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      MultiLine       =   0   'False
      AlignTextH      =   0
      AlignTextV      =   1
      ToggleTrue      =   ""
      TextTrue        =   ""
      Value           =   0
      BooleanMode     =   0
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -**********
      ThreeDTextShadowColor=   -**********
      ThreeDTextOffset=   1
      BorderGrayAreaColor=   -**********
      ToggleGrayed    =   ""
      TextGrayed      =   ""
      AllowMnemonic   =   -1  'True
      BackColor       =   -**********
      ForeColor       =   -**********
      ThreeDOnFocusInvert=   0   'False
      Caption         =   "Select All"
      ThreeDFrameColor=   -**********
      Appearance      =   0
      BorderDropShadow=   0
      BorderDropShadowColor=   -**********
      BorderDropShadowWidth=   3
      BooleanDataType =   0
      OLEDropMode     =   0
   End
   Begin VB.CommandButton cmdCancel 
      BackColor       =   &H00E0E0E0&
      Cancel          =   -1  'True
      Caption         =   "&Cancel"
      Height          =   390
      Left            =   1770
      TabIndex        =   4
      Top             =   3405
      Width           =   1065
   End
   Begin VB.CommandButton cmdOk 
      BackColor       =   &H00E0E0E0&
      Caption         =   "OK"
      Height          =   390
      Left            =   465
      TabIndex        =   3
      Top             =   3405
      Width           =   1065
   End
   Begin VB.Frame Frame1 
      BackColor       =   &H00EAD08E&
      Height          =   585
      Left            =   0
      TabIndex        =   0
      Top             =   -75
      Width           =   5640
      Begin VB.Label lblHeading 
         Alignment       =   2  'Center
         AutoSize        =   -1  'True
         BackColor       =   &H00EFD6C5&
         BackStyle       =   0  'Transparent
         Caption         =   "Rights Browser"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   15.75
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Height          =   375
         Left            =   1755
         TabIndex        =   1
         Top             =   135
         Width           =   2400
      End
   End
   Begin FPSpread.vaSpread vaSpread1 
      Height          =   2760
      Left            =   0
      TabIndex        =   2
      Top             =   525
      Width           =   5640
      _Version        =   196608
      _ExtentX        =   9948
      _ExtentY        =   4868
      _StockProps     =   64
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   3
      MaxRows         =   10
      ScrollBars      =   2
      SpreadDesigner  =   "frmRightsBrowser.frx":000C
      UserResize      =   0
   End
End
Attribute VB_Name = "frmRightsBrowser"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub chkSelectAll_Change()
        '<EhHeader>
        On Error GoTo chkSelectAll_Change_Err
        '</EhHeader>

100     vaSpread1.Row = -1
102     vaSpread1.Col = 3
104     vaSpread1.Value = chkSelectAll.Value

        '<EhFooter>
        Exit Sub

chkSelectAll_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in TimeManager.frmRightsBrowser.chkSelectAll_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdCancel_Click()
        '<EhHeader>
        On Error GoTo cmdCancel_Click_Err
        '</EhHeader>

100     Unload Me

        '<EhFooter>
        Exit Sub

cmdCancel_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmRightsBrowser.cmdCancel_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdOk_Click()
        '<EhHeader>
        On Error GoTo cmdOk_Click_Err
        '</EhHeader>

100     Select Case BrowserType

            Case Is = TransactionFrom
102             Call SaveTransactionFrom

104         Case Is = Settings
106             Call SaveSettings

108         Case Is = SetupReports
110             Call SaveSetupReports

112         Case Is = SummaryReports
114             Call SaveSummaryReports

        End Select

116     Unload Me

        '<EhFooter>
        Exit Sub

cmdOk_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmRightsBrowser.cmdOk_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>

        CenterForm Me
100     vaSpread1.MaxRows = 0
102     vaSpread1.MaxRows = 10

104     Select Case BrowserType

            Case Is = TransactionFrom
106             Call FillTransactionFrom

108         Case Is = Settings
110             Call FillSettings

112         Case Is = SetupReports
114             Call FillSetupReports

116         Case Is = SummaryReports
118             Call FillSummaryReports

        End Select
            
        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmRightsBrowser.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
            
Private Sub FillTransactionFrom()
        '<EhHeader>
        On Error GoTo FillTransactionFrom_Err
        '</EhHeader>

100     sSQL = "SELECT MenuID, MenuName, MenuCaption, MenuType From UserMenu WHERE MenuType='TransactionFrom' Order by MenuID"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
106     vaSpread1.MaxRows = 0
108     vaSpread1.MaxRows = 1
110     vaSpread1.Row = 1

112     Do Until rs.EOF

114         vaSpread1.Col = 1
116         vaSpread1.Text = rs("MenuID")
118         vaSpread1.Col = 2
120         vaSpread1.Text = rs("MenuCaption")
122         vaSpread1.MaxRows = vaSpread1.MaxRows + 1
124         vaSpread1.Row = vaSpread1.Row + 1
126         rs.MoveNext

        Loop

128     vaSpread1.MaxRows = vaSpread1.DataRowCnt

130     For i = 1 To vaSpread1.DataRowCnt

132         vaSpread1.Row = i
134         vaSpread1.Col = 1
        
136         Set rs = New ADODB.Recordset
138         sSQL = "Select * From MenuRights Where UserID='" & UserRightsUser & "' And MenuID='" & vaSpread1.Text & "' And MenuTypeID=" & BrowserType
140         rs.Open sSQL, Conn, 1, 3
        
142         If Not rs.EOF Then

144             vaSpread1.Col = 3
146             vaSpread1.Value = 1

            End If
        
        Next

        '<EhFooter>
        Exit Sub

FillTransactionFrom_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Payroll_System.frmRightsBrowser.FillTransactionFrom " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
          
Private Sub FillSetupReports()
        '<EhHeader>
        On Error GoTo FillSetupReports_Err
        '</EhHeader>

100     sSQL = "SELECT MenuID, MenuName, MenuCaption, MenuType From UserMenu WHERE MenuType='SetupReports'"
    
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
106     vaSpread1.MaxRows = 0
108     vaSpread1.MaxRows = 1
110     vaSpread1.Row = 1

112     Do Until rs.EOF

114         vaSpread1.Col = 1
116         vaSpread1.Text = rs("MenuID")
118         vaSpread1.Col = 2
120         vaSpread1.Text = rs("MenuCaption")
122         vaSpread1.MaxRows = vaSpread1.MaxRows + 1
124         vaSpread1.Row = vaSpread1.Row + 1
126         rs.MoveNext

        Loop

128     vaSpread1.MaxRows = vaSpread1.DataRowCnt

130     For i = 1 To vaSpread1.DataRowCnt

132         vaSpread1.Row = i
134         vaSpread1.Col = 1
        
136         Set rs = New ADODB.Recordset
138         sSQL = "Select * From MenuRights Where UserID='" & UserRightsUser & "' And MenuID='" & vaSpread1.Text & "' And MenuTypeID=" & BrowserType
140         rs.Open sSQL, Conn, 1, 3
        
142         If Not rs.EOF Then

144             vaSpread1.Col = 3
146             vaSpread1.Value = 1

            End If
        
        Next

        '<EhFooter>
        Exit Sub

FillSetupReports_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Payroll_System.frmRightsBrowser.FillSetupReports " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
            
Private Sub FillSettings()
        '<EhHeader>
        On Error GoTo FillSettings_Err
        '</EhHeader>

100     sSQL = "SELECT MenuID, MenuName, MenuCaption, MenuType From UserMenu WHERE MenuType='Settings' Order by MenuID"
    
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
106     vaSpread1.MaxRows = 0
108     vaSpread1.MaxRows = 1
110     vaSpread1.Row = 1

112     Do Until rs.EOF

114         vaSpread1.Col = 1
116         vaSpread1.Text = rs("MenuID")
118         vaSpread1.Col = 2
120         vaSpread1.Text = rs("MenuCaption")
122         vaSpread1.MaxRows = vaSpread1.MaxRows + 1
124         vaSpread1.Row = vaSpread1.Row + 1
126         rs.MoveNext

        Loop

128     vaSpread1.MaxRows = vaSpread1.DataRowCnt

130     For i = 1 To vaSpread1.DataRowCnt

132         vaSpread1.Row = i
134         vaSpread1.Col = 1
        
136         Set rs = New ADODB.Recordset
138         sSQL = "Select * From MenuRights Where UserID='" & UserRightsUser & "' And MenuID='" & vaSpread1.Text & "' And MenuTypeID=" & BrowserType
140         rs.Open sSQL, Conn, 1, 3
        
142         If Not rs.EOF Then

144             vaSpread1.Col = 3
146             vaSpread1.Value = 1

            End If
        
        Next

        '<EhFooter>
        Exit Sub

FillSettings_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmRightsBrowser.FillSettings " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub FillSummaryReports()
        '<EhHeader>
        On Error GoTo FillSummaryReports_Err
        '</EhHeader>

100     sSQL = "SELECT MenuID, MenuName, MenuCaption, MenuType From UserMenu WHERE MenuType='SummaryReports' Order by MenuID"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
106     vaSpread1.MaxRows = 0
108     vaSpread1.MaxRows = 1
110     vaSpread1.Row = 1

112     Do Until rs.EOF

114         vaSpread1.Col = 1
116         vaSpread1.Text = rs("MenuID")
118         vaSpread1.Col = 2
120         vaSpread1.Text = rs("MenuCaption")
122         vaSpread1.MaxRows = vaSpread1.MaxRows + 1
124         vaSpread1.Row = vaSpread1.Row + 1
126         rs.MoveNext

        Loop

128     vaSpread1.MaxRows = vaSpread1.DataRowCnt

130     For i = 1 To vaSpread1.DataRowCnt

132         vaSpread1.Row = i
134         vaSpread1.Col = 1
        
136         Set rs = New ADODB.Recordset
138         sSQL = "Select * From MenuRights Where UserID='" & UserRightsUser & "' And MenuID='" & vaSpread1.Text & "' And MenuTypeID=" & BrowserType
140         rs.Open sSQL, Conn, 1, 3
        
142         If Not rs.EOF Then

144             vaSpread1.Col = 3
146             vaSpread1.Value = 1

            End If
        
        Next

        '<EhFooter>
        Exit Sub

FillSummaryReports_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmRightsBrowser.FillSummaryReports " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub SaveTransactionFrom()
        '<EhHeader>
        On Error GoTo SaveInvoices_Err
        '</EhHeader>

100     sSQL = "Delete From MenuRights Where UserID='" & UserRightsUser & "' And MenuTypeID=" & BrowserType
102     Conn.Execute sSQL
    
104     For i = 1 To vaSpread1.DataRowCnt

106         vaSpread1.Row = i
108         vaSpread1.Col = 3

110         If vaSpread1.Value = 1 Then

112             vaSpread1.Col = 1
114             Set rs = New ADODB.Recordset
116             sSQL = "Select * From MenuRights Where UserID='" & UserRightsUser & "' And MenuID='" & vaSpread1.Text & "' And MenuTypeID=" & BrowserType
118             rs.Open sSQL, Conn, 1, 3
120             rs.AddNew
122             rs("UserID") = UserRightsUser
124             rs("MenuID") = vaSpread1.Text
126             rs("MenuTypeID") = BrowserType
128             rs.Update
130             rs.Close

            End If

        Next

        '<EhFooter>
        Exit Sub

SaveInvoices_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmRightsBrowser.SaveInvoices " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub SaveSetupReports()
        '<EhHeader>
        On Error GoTo SaveSetupReports_Err
        '</EhHeader>

100     sSQL = "Delete From MenuRights Where UserID='" & UserRightsUser & "' And MenuTypeID=" & BrowserType
102     Conn.Execute sSQL
    
104     For i = 1 To vaSpread1.DataRowCnt

106         vaSpread1.Row = i
108         vaSpread1.Col = 3

110         If vaSpread1.Value = 1 Then

112             vaSpread1.Col = 1
114             Set rs = New ADODB.Recordset
116             sSQL = "Select * From MenuRights Where UserID='" & UserRightsUser & "' And MenuID='" & vaSpread1.Text & "' And MenuTypeID=" & BrowserType
118             rs.Open sSQL, Conn, 1, 3
120             rs.AddNew
122             rs("UserID") = UserRightsUser
124             rs("MenuID") = vaSpread1.Text
126             rs("MenuTypeID") = BrowserType
128             rs.Update
130             rs.Close

            End If

        Next

        '<EhFooter>
        Exit Sub

SaveSetupReports_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Payroll_System.frmRightsBrowser.SaveSetupReports " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub SaveSettings()
        '<EhHeader>
        On Error GoTo SaveSettings_Err
        '</EhHeader>

100     sSQL = "Delete From MenuRights Where UserID='" & UserRightsUser & "' And MenuTypeID=" & BrowserType
102     Conn.Execute sSQL
    
104     For i = 1 To vaSpread1.DataRowCnt

106         vaSpread1.Row = i
108         vaSpread1.Col = 3

110         If vaSpread1.Value = 1 Then

112             vaSpread1.Col = 1
114             Set rs = New ADODB.Recordset
116             sSQL = "Select * From MenuRights Where UserID='" & UserRightsUser & "' And MenuID='" & vaSpread1.Text & "' And MenuTypeID=" & BrowserType
118             rs.Open sSQL, Conn, 1, 3
120             rs.AddNew
122             rs("UserID") = UserRightsUser
124             rs("MenuID") = vaSpread1.Text
126             rs("MenuTypeID") = BrowserType
128             rs.Update
130             rs.Close

            End If

        Next

        '<EhFooter>
        Exit Sub

SaveSettings_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmRightsBrowser.SaveSettings " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub SaveSummaryReports()
        '<EhHeader>
        On Error GoTo SaveSummaryReports_Err
        '</EhHeader>

100     sSQL = "Delete From MenuRights Where UserID='" & UserRightsUser & "' And MenuTypeID=" & BrowserType
102     Conn.Execute sSQL
    
104     For i = 1 To vaSpread1.DataRowCnt

106         vaSpread1.Row = i
108         vaSpread1.Col = 3

110         If vaSpread1.Value = 1 Then

112             vaSpread1.Col = 1
114             Set rs = New ADODB.Recordset
116             sSQL = "Select * From MenuRights Where UserID='" & UserRightsUser & "' And MenuID='" & vaSpread1.Text & "' And MenuTypeID=" & BrowserType
118             rs.Open sSQL, Conn, 1, 3
120             rs.AddNew
122             rs("UserID") = UserRightsUser
124             rs("MenuID") = vaSpread1.Text
126             rs("MenuTypeID") = BrowserType
128             rs.Update
130             rs.Close

            End If

        Next

        '<EhFooter>
        Exit Sub

SaveSummaryReports_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmRightsBrowser.SaveSummaryReports " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

