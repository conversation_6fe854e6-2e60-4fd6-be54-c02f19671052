VERSION 5.00
Object = "{CDF3B183-D408-11CE-AE2C-0080C786E37D}#3.0#0"; "Edt32x30.ocx"
Object = "{43135020-B751-4DDD-AB4A-B6D8A342216E}#1.0#0"; "sg20o.ocx"
Begin VB.Form frmBookingSummary 
   Caption         =   "Booking Summary"
   ClientHeight    =   6900
   ClientLeft      =   1545
   ClientTop       =   2445
   ClientWidth     =   10965
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmBookingSummary.frx":0000
   LinkTopic       =   "Form1"
   ScaleHeight     =   6900
   ScaleWidth      =   10965
   Begin VB.CommandButton cmdCalender 
      Caption         =   "Show Calender"
      Height          =   450
      Left            =   4845
      TabIndex        =   8
      Top             =   885
      Width           =   1650
   End
   Begin VB.Frame Frame1 
      Appearance      =   0  'Flat
      BackColor       =   &H00EAD08E&
      ForeColor       =   &H80000008&
      Height          =   615
      Left            =   0
      TabIndex        =   6
      Top             =   -90
      Width           =   19155
      Begin VB.Label Label2 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Lawns Booking Summary"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   18
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Height          =   435
         Left            =   135
         TabIndex        =   7
         Top             =   165
         Width           =   5070
      End
   End
   Begin EditLib.fpDateTime txtFromDate 
      Height          =   300
      Left            =   1455
      TabIndex        =   2
      Top             =   630
      Width           =   1515
      _Version        =   196608
      _ExtentX        =   2672
      _ExtentY        =   529
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   -2147483643
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   1
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   1
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   0
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   1
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "01/10/2009"
      DateCalcMethod  =   0
      DateTimeFormat  =   0
      UserDefinedFormat=   ""
      DateMax         =   "00000000"
      DateMin         =   "00000000"
      TimeMax         =   "000000"
      TimeMin         =   "000000"
      TimeString1159  =   ""
      TimeString2359  =   ""
      DateDefault     =   "00000000"
      TimeDefault     =   "000000"
      TimeStyle       =   0
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   2
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      PopUpType       =   0
      DateCalcY2KSplit=   60
      CaretPosition   =   0
      IncYear         =   1
      IncMonth        =   1
      IncDay          =   1
      IncHour         =   1
      IncMinute       =   1
      IncSecond       =   1
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      StartMonth      =   4
      ButtonAlign     =   0
      BoundDataType   =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin VB.CommandButton cmdRefreshData 
      Caption         =   "Refresh Data"
      Default         =   -1  'True
      Height          =   450
      Left            =   3135
      TabIndex        =   1
      Top             =   885
      Width           =   1650
   End
   Begin DDSharpGridOLEDB2.SGGrid SGGrid1 
      Height          =   5670
      Left            =   60
      TabIndex        =   0
      Top             =   1365
      Width           =   10860
      _cx             =   19156
      _cy             =   10001
      DataMember      =   ""
      DataMode        =   0
      AutoFields      =   -1  'True
      Enabled         =   -1  'True
      GridBorderStyle =   1
      ScrollBars      =   3
      FlatScrollBars  =   1
      ScrollBarTrack  =   0   'False
      DataRowCount    =   2
      BeginProperty HeadingFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DataColCount    =   4
      HeadingRowCount =   1
      HeadingColCount =   1
      TextAlignment   =   0
      WordWrap        =   0   'False
      Ellipsis        =   0
      HeadingBackColor=   -2147483633
      HeadingForeColor=   -2147483630
      HeadingTextAlignment=   0
      HeadingWordWrap =   0   'False
      HeadingEllipsis =   0
      GridLines       =   1
      HeadingGridLines=   2
      GridLinesColor  =   -2147483633
      HeadingGridLinesColor=   -2147483632
      EvenOddStyle    =   0
      ColorEven       =   -2147483628
      ColorOdd        =   -2147483624
      UserResizeAnimate=   1
      UserResizing    =   3
      RowHeightMin    =   0
      RowHeightMax    =   0
      ColWidthMin     =   0
      ColWidthMax     =   0
      UserDragging    =   2
      UserHiding      =   2
      CellPadding     =   15
      CellBkgStyle    =   1
      CellBackColor   =   -2147483643
      CellForeColor   =   -2147483640
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      FocusRect       =   1
      FocusRectColor  =   0
      FocusRectLineWidth=   1
      TabKeyBehavior  =   0
      EnterKeyBehavior=   1
      NavigationWrapMode=   1
      SkipReadOnly    =   0   'False
      DefaultColWidth =   1200
      DefaultRowHeight=   255
      CellsBorderColor=   0
      CellsBorderVisible=   -1  'True
      RowNumbering    =   0   'False
      EqualRowHeight  =   0   'False
      EqualColWidth   =   0   'False
      HScrollHeight   =   0
      VScrollWidth    =   0
      Format          =   "General"
      Appearance      =   1
      FitLastColumn   =   -1  'True
      SelectionMode   =   0
      MultiSelect     =   0
      AllowAddNew     =   0   'False
      AllowDelete     =   0   'False
      AllowEdit       =   0   'False
      ScrollBarTips   =   0
      CellTips        =   0
      CellTipsDelay   =   1000
      SpecialMode     =   0
      OutlineLines    =   1
      CacheAllRecords =   -1  'True
      ColumnClickSort =   -1  'True
      PreviewPaneColumn=   ""
      PreviewPaneType =   0
      PreviewPanePosition=   2
      PreviewPaneSize =   2000
      GroupIndentation=   225
      InactiveSelection=   1
      AutoScroll      =   -1  'True
      AutoResize      =   1
      AutoResizeHeadings=   -1  'True
      OLEDragMode     =   0
      OLEDropMode     =   0
      Caption         =   ""
      ScrollTipColumn =   ""
      MaxRows         =   4194304
      MaxColumns      =   8192
      NewRowPos       =   1
      CustomBkgDraw   =   0
      AutoGroup       =   -1  'True
      GroupByBoxVisible=   -1  'True
      GroupByBoxText  =   "Drag a column header here to group by that column"
      AlphaBlendEnabled=   -1  'True
      DragAlphaLevel  =   206
      AutoSearch      =   0
      AutoSearchDelay =   2000
      StylesCollection=   $"frmBookingSummary.frx":000C
      ColumnsCollection=   $"frmBookingSummary.frx":1D75
      ValueItems      =   $"frmBookingSummary.frx":3384
   End
   Begin EditLib.fpDateTime txtToDate 
      Height          =   300
      Left            =   1455
      TabIndex        =   3
      Top             =   990
      Width           =   1515
      _Version        =   196608
      _ExtentX        =   2672
      _ExtentY        =   529
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   -2147483643
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   1
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   1
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   0
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   1
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "01/10/2009"
      DateCalcMethod  =   0
      DateTimeFormat  =   0
      UserDefinedFormat=   ""
      DateMax         =   "00000000"
      DateMin         =   "00000000"
      TimeMax         =   "000000"
      TimeMin         =   "000000"
      TimeString1159  =   ""
      TimeString2359  =   ""
      DateDefault     =   "00000000"
      TimeDefault     =   "000000"
      TimeStyle       =   0
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   2
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      PopUpType       =   0
      DateCalcY2KSplit=   60
      CaretPosition   =   0
      IncYear         =   1
      IncMonth        =   1
      IncDay          =   1
      IncHour         =   1
      IncMinute       =   1
      IncSecond       =   1
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      StartMonth      =   4
      ButtonAlign     =   0
      BoundDataType   =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin VB.Label Label1 
      AutoSize        =   -1  'True
      Caption         =   "To Date:"
      BeginProperty Font 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   195
      Left            =   180
      TabIndex        =   5
      Top             =   1035
      Width           =   810
   End
   Begin VB.Label lblLabel1 
      AutoSize        =   -1  'True
      Caption         =   "From Date:"
      BeginProperty Font 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   195
      Left            =   180
      TabIndex        =   4
      Top             =   660
      Width           =   1080
   End
End
Attribute VB_Name = "frmBookingSummary"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
Dim BookingID As String

Private Sub cmdCalender_Click()
        '<EhHeader>
        On Error GoTo cmdCalender_Click_Err
        '</EhHeader>
    
100     frmCalender.Show 1
    
        '<EhFooter>
        Exit Sub

cmdCalender_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmBookingSummary.cmdCalender_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdRefreshData_Click()
        '<EhHeader>
        On Error GoTo cmdRefreshData_Click_Err
        '</EhHeader>
    
        Dim FromDate As String
        Dim ToDate As String
                         
100     FromDate = Format(txtFromDate.Text, "dd/mmm/yyyy")
102     ToDate = Format(txtToDate.Text, "dd/mmm/yyyy")
                             
106     sSQL = "SELECT BookingInformation.BookingID, BookingInformation.CustomerName, LawnSetup.LawnName, EventSetup.EventName, dbo.GetWeekDayNameOfDate(BookingInformation.EventDate) AS WeekDay, BookingInformation.EventDate, BookingInformation.BookingAmount, " & _
           "dbo.GetPaidAmount(BookingInformation.BookingID) AS PaidAmount, BookingInformation.BookingAmount - dbo.GetPaidAmount(BookingInformation.BookingID) AS Remaining, dbo.ExtraServices(BookingInformation.BookingID, 'CB') AS Catering, " & _
           "dbo.ExtraServices(BookingInformation.BookingID, 'DB') AS Decoration FROM BookingInformation INNER JOIN LawnSetup ON BookingInformation.LawnID = LawnSetup.LawnID INNER JOIN EventSetup ON BookingInformation.EventID = EventSetup.EventID " & _
           "WHERE (BookingInformation.TransactionType = N'LB') AND (BookingInformation.EventDate BETWEEN '" & FromDate & "' AND '" & ToDate & "') ORDER BY BookingInformation.BookingID"
    
108     Set rs = New ADODB.Recordset
110     rs.Open sSQL, Conn, 1, 3
   
112     SGGrid1.DataMode = sgBound
114     Set SGGrid1.DataSource = rs
116     SGGrid1.ColorEven = &H80000014
118     SGGrid1.ColorOdd = &H80000018

        '<EhFooter>
        Exit Sub

cmdRefreshData_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmBookingSummary.cmdRefreshData_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()

    CenterForm Me
    Me.WindowState = vbMaximized
    
    Call BuildFunctions
    
    txtFromDate.Text = Format(CDate(Date) - 30, "dd/mm/yyyy")
    txtToDate.Text = Format(Date, "dd/mm/yyyy")

'''    txtFromDate.Text = "01/" & Month(Date) & "/" & Year(Date)
'''    If Month(Date) = 12 Then
'''        txtToDate.Text = CDate("01/" & "01" & "/" & Year(Date) + 1) - 1
'''    Else
'''        txtToDate.Text = CDate("01/" & Month(Date) + 1 & "/" & Year(Date)) - 1
'''    End If
    
    Call cmdRefreshData_Click
End Sub

Private Sub Form_Resize()
    '<EhHeader>
    On Error Resume Next
    '</EhHeader>
    
    SGGrid1.Move 0, SGGrid1.Top, Me.ScaleWidth, Me.ScaleHeight
    
End Sub

Sub BuildFunctions()
        '<EhHeader>
        On Error Resume Next
        '</EhHeader>
        
        '''----------------------------------------
        '''--- GetWeekDayNameOfDate
        '''--- Returns Week Day Name in English
        '''--- Takes @@DATEFIRST into consideration
        '''--- You can edit udf for other languages
        '''----------------------------------------
100     sSQL = "CREATE FUNCTION GetWeekDayNameOfDate (@Date datetime) RETURNS nvarchar(50)" & _
           "BEGIN DECLARE @DayName nvarchar(50) " & _
           "SELECT @DayName = " & _
           "CASE (DATEPART(dw, @Date) + @@DATEFIRST) % 7 " & _
           "WHEN 1 THEN 'Sunday' " & _
           "WHEN 2 THEN 'Monday' " & _
           "WHEN 3 THEN 'Tuesday' " & _
           "WHEN 4 THEN 'Wednesday' " & _
           "WHEN 5 THEN 'Thursday' " & _
           "WHEN 6 THEN 'Friday' " & _
           "WHEN 0 THEN 'Saturday' " & _
           "End RETURN @DayName End "

102     Conn.Execute sSQL
        
        '''----------------------------------------
        '''--- ExtraServices
        '''--- Returns The Extra Services Status as True or False
        '''--- Takes @@BookingID and @TransactinType into consideration
        '''--- You can edit udf for other languages
        '''----------------------------------------
104     sSQL = "CREATE FUNCTION ExtraServices(@BookingID char(10),@TransactionType char(2))RETURNS Bit AS " & _
           "BEGIN DECLARE @Avalible bit " & _
           "SELECT @Avalible=BookingAmount From BookingInformation WHERE (BookingID =@BookingID) AND (TransactionType = @TransactionType) " & _
           "RETURN @Avalible End"

106     Conn.Execute sSQL

        '''----------------------------------------
        '''--- GetPaidAmount
        '''--- Returns Paid Amount for any BookingID
        '''--- Takes @@BookingID into consideration
        '''--- You can edit udf for other languages
        '''----------------------------------------
108     sSQL = "CREATE FUNCTION dbo.GetPaidAmount (@BookingID char (6)) RETURNS Money AS BEGIN " & _
           "DECLARE @PaidAmount money " & _
           "SELECT  @PaidAmount =SUM(RecievedAmount) From BookingInformation WHERE (BookingID=@BookingID) AND (TransactionType = 'LP') " & _
           "RETURN @PaidAmount End"

110     Conn.Execute sSQL

End Sub

Private Sub SGGrid1_DblClick()
        '<EhHeader>
        On Error GoTo SGGrid1_DblClick_Err
        '</EhHeader>
       
100     SGGrid1.Col = 1
102     BookingID = SGGrid1.Text
   
104     sSQL = "SELECT PurchaseOrder.*, RequiredQuantity * SellPrice AS SellingAmount, OrderQuantity * CostPrice AS CostingAmount, RequiredQuantity * SellPrice - OrderQuantity * CostPrice AS Profit From PurchaseOrder " & _
           "WHERE (BookingID='" & BookingID & "')"
106     Set rs = New ADODB.Recordset
108     rs.Open sSQL, Conn, 1, 3

110     If rs.EOF = True Then
            Exit Sub
        End If
        
112     With repCostSheet
114         .Restart
116         .documentName = "Cost Sheet Booking ID: " & BookingID
118         .Caption = .documentName
120         .DataControl1.Recordset = rs
122         .Show 1
        End With

        '<EhFooter>
        Exit Sub

SGGrid1_DblClick_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmBookingSummary.SGGrid1_DblClick " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

