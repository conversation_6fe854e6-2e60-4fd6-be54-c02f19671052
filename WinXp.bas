Attribute VB_Name = "WinXp"
Option Explicit
DefBool F: DefLng G-L: DefStr M-Z

'Work well in Windows NT/2000/Xp

'Win95/98/Me must install
'/Windows/System/Iosubsys/Smartvsd.vxd
'and Restart

Private Const DFP_RECEIVE_DRIVE_DATA = &H7C088
Private Const INVALID_HANDLE_VALUE As Long = -1

Private Const FILE_SHARE_READ = &H1
Private Const FILE_SHARE_WRITE = &H2
Private Const GENERIC_READ = &*********
Private Const GENERIC_WRITE = &*********
Private Const OPEN_EXISTING = 3
Private Const CREATE_NEW = 1

Private Type OSVERSIONINFO
    dwOSVersionInfoSize As Long
    dwMajorVersion As Long
    dwMinorVersion As Long
    dwBuildNumber As Long
    dwPlatformId As Long
    szCSDVersion As String * 128
End Type

Private Type IDEREGS
    bFeaturesReg As Byte
    bSectorCountReg As Byte
    bSectorNumberReg As Byte
    bCylLowReg As Byte
    bCylHighReg As Byte
    bDriveHeadReg As Byte
    bCommandReg As Byte
    bReserved As Byte
End Type

Private Type DRIVERSTATUS
    bDriveError As Byte
    bIDEStatus As Byte
    bReserved(1 To 2) As Byte
    dwReserved(1 To 2) As Long
End Type

Private Type SENDIN
    cBufferSize As Long
    irDriveRegs As IDEREGS
    bDriveNumber As Byte
    bReserved(1 To 3) As Byte
    dwReserved(1 To 4) As Long
End Type

Private Type SENDOUT
    cBufferSize As Long
    DStatus As DRIVERSTATUS
    bBuffer(1 To 512) As Byte
End Type

Private Declare Function GetVersionEx Lib "kernel32" Alias "GetVersionExA" (lpVersionInformation As OSVERSIONINFO) As Long
Private Declare Function CreateFile Lib "kernel32" Alias "CreateFileA" (ByVal lpFileName As String, ByVal dwDesiredAccess As Long, ByVal dwShareMode As Long, ByVal lpSecurityAttributes As Long, ByVal dwCreationDisposition As Long, ByVal dwFlagsAndAttributes As Long, ByVal hTemplateFile As Long) As Long
Private Declare Function CloseHandle Lib "kernel32" (ByVal hObject As Long) As Long
Private Declare Function DeviceIoControl Lib "kernel32" (ByVal hDevice As Long, ByVal dwIoControlCode As Long, lpInBuffer As Any, ByVal nInBufferSize As Long, lpOutBuffer As Any, ByVal nOutBufferSize As Long, lpBytesReturned As Long, ByVal lpOverlapped As Long) As Long
Private Declare Sub ZeroMemory Lib "kernel32" Alias "RtlZeroMemory" (dest As Any, ByVal numBytes As Long)
Private Declare Sub CopyMemory Lib "kernel32" Alias "RtlMoveMemory" (Destination As Any, Source As Any, ByVal Length As Long)

Function HdXp(ByVal id, s1, s2) As Boolean
Dim bin As SENDIN
Dim bout As SENDOUT
Dim hdh As Long, i1, u1
        
    If (id < 0) Or (id > 3) Then GoTo ooen
    u1 = Get_Platform
    Select Case u1
        Case "WNT":
            hdh = CreateFile("\\.\PhysicalDrive" & id, _
                GENERIC_READ + GENERIC_WRITE, FILE_SHARE_READ + FILE_SHARE_WRITE, _
                0, OPEN_EXISTING, 0, 0)
        Case "W95", "W98":
            hdh = CreateFile("\\.\Smartvsd", 0, 0, 0, CREATE_NEW, 0, 0)
        Case Else: hdh = -1
    End Select
    If hdh = -1 Then GoTo ooen
    
    ZeroMemory bin, Len(bin)
    ZeroMemory bout, Len(bout)
    
    With bin
        .bDriveNumber = id
        .cBufferSize = 512
        With .irDriveRegs
            If (id And 1) Then .bDriveHeadReg = &HB0 Else .bDriveHeadReg = &HA0
            .bCommandReg = &HEC
            .bSectorCountReg = 1
            .bSectorNumberReg = 1
        End With
    End With
    
    DeviceIoControl hdh, DFP_RECEIVE_DRIVE_DATA, bin, Len(bin), bout, Len(bout), i1, 0
    
    s1 = Get_info(55, 40, bout)
    s2 = Get_info(21, 20, bout)
    CloseHandle hdh
    HdXp = True
ooen:
End Function

Private Function Get_info(ByVal iStart, ByVal iLen, bout As SENDOUT) As String
Dim i1, s
    s = ""
    For i1 = iStart To iStart + iLen - 1 Step 2
        If bout.bBuffer(i1 + 1) = 0 Then Exit For
        s = s & Chr(bout.bBuffer(i1 + 1))
        If bout.bBuffer(i1) = 0 Then Exit For
        s = s & Chr(bout.bBuffer(i1))
    Next i1
    Get_info = Trim(s)
End Function

Function Get_Platform() As String
Dim OS As OSVERSIONINFO, u1
        
    OS.dwOSVersionInfoSize = Len(OS)
    Call GetVersionEx(OS)

    Select Case OS.dwPlatformId
    Case 0: u1 = "32S"
    Case 2: u1 = "WNT"
    Case 1:
        If OS.dwMinorVersion = 0 Then u1 = "W95" Else u1 = "W98"
    End Select
    Get_Platform = u1
End Function
