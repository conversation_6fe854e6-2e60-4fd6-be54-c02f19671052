VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} repZTapeReportStar 
   Caption         =   "Point_of_Sale_System - repZTapeReportStar (ActiveReport)"
   ClientHeight    =   10680
   ClientLeft      =   0
   ClientTop       =   390
   ClientWidth     =   15360
   _ExtentX        =   27093
   _ExtentY        =   18838
   SectionData     =   "repZTapeReportStar.dsx":0000
End
Attribute VB_Name = "repZTapeReportStar"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
Dim NetSalesTotal As String
Dim strZSalesTaxRate As String

Private Sub ActiveReport_ReportStart()
        '<EhHeader>
        On Error GoTo ActiveReport_ReportStart_Err
        '</EhHeader>
        
        On Error Resume Next
        
        sSQL = "SELECT MAX(TaxRate) AS SalesTaxRate From Transactions WHERE (TransactionDate='" & strZTapeDate & "')"
        Set rs = New ADODB.Recordset
        rs.Open sSQL, Conn, 1, 3
        
        strZSalesTaxRate = rs("SalesTaxRate")
    
        Dim rsDailySummary As ADODB.Recordset
    
        'For Net Sales Total
100     sSQL = "SELECT SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions WHERE TransactionDate='" & strZTapeDate & "'"

102     sSQL = "SELECT DISTINCT TransactionNo, TransactionType, SUM(ItemPrice * ItemQuantity) AS TotalAmount, SUM(DISTINCT TaxAmount) AS TaxAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') GROUP BY TransactionNo, TransactionType"

104     Debug.Print sSQL
106     Set rs = New ADODB.Recordset
108     rs.Open sSQL, Conn, 1, 3

110     Do Until rs.EOF
112         txtNetSalesTotal.Text = Val(txtNetSalesTotal.Text) + Val(rs("TotalAmount")) + Val(rs("TaxAmount"))
114         rs.MoveNext
            
        Loop
        
116     txtNetSalesTotal.Text = Format(txtNetSalesTotal.Text * 100 / 116, "##.##")
118     NetSalesTotal = txtNetSalesTotal.Text
        
        'For DineIn Count
120     sSQL = "SELECT DISTINCT TransactionType, COUNT(DISTINCT TransactionNo) AS TransactionType From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionType HAVING TransactionType='Dine In' " & _
           "ORDER BY TransactionType"

122     Set rs = New ADODB.Recordset
124     rs.Open sSQL, Conn, 1, 3

126     If rs.EOF = False Then
128         txtDineInCount.Text = rs("TransactionType")
        End If
    
        'For Deliveries Count
130     sSQL = "SELECT DISTINCT TransactionType, COUNT(DISTINCT TransactionNo) AS TransactionType From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionType HAVING TransactionType='Delivery' " & _
           "ORDER BY TransactionType"

132     Set rs = New ADODB.Recordset
134     rs.Open sSQL, Conn, 1, 3

136     If rs.EOF = False Then
138         txtDeliveriesCount.Text = rs("TransactionType")
        End If
    
        'For DineIn Total Amount
140     sSQL = "SELECT DISTINCT TransactionType, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionType HAVING TransactionType='Dine In' " & _
           "ORDER BY TransactionType"
        
142     sSQL = "SELECT DISTINCT TransactionNo, TransactionType, SUM(ItemPrice * ItemQuantity) AS TotalAmount, SUM(DISTINCT TaxAmount) AS TaxAmount " & _
           "From Transactions WHERE (TransactionDate='" & strZTapeDate & "') GROUP BY TransactionType, TransactionNo HAVING (TransactionType = 'Dine In') ORDER BY TransactionType "
        
144     Debug.Print sSQL
146     Set rs = New ADODB.Recordset
148     rs.Open sSQL, Conn, 1, 3

150     Do Until rs.EOF
152         txtDineIn.Text = Val(txtDineIn.Text) + Val(rs("TotalAmount")) + Val(rs("TaxAmount"))
154         rs.MoveNext
        Loop
        
156     txtDineIn.Text = Format(txtDineIn.Text * 100 / 116, "##.##")
        
        'For Deliveries Total Amount
158     sSQL = "SELECT DISTINCT TransactionType, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionType HAVING TransactionType='Delivery' " & _
           "ORDER BY TransactionType"
        
160     sSQL = "SELECT DISTINCT TransactionNo, TransactionType, SUM(ItemPrice * ItemQuantity) AS TotalAmount, SUM(DISTINCT TaxAmount) AS TaxAmount " & _
           "From Transactions WHERE (TransactionDate='" & strZTapeDate & "') GROUP BY TransactionType, TransactionNo HAVING (TransactionType = 'Delivery') ORDER BY TransactionType "
    
162     Set rs = New ADODB.Recordset
164     rs.Open sSQL, Conn, 1, 3

166     Do Until rs.EOF

168         txtDelivery.Text = Val(txtDelivery.Text) + Val(rs("TotalAmount")) + Val(rs("TaxAmount"))
170         rs.MoveNext
    
        Loop
        
172     txtDelivery.Text = Format(txtDelivery.Text * 100 / 116, "##.##")
        
        'For TaxCollected
174     sSQL = "SELECT TransactionNo, SUM(DISTINCT TaxAmount) AS TaxAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionNo"
    
176     Set rs = New ADODB.Recordset
178     rs.Open sSQL, Conn, 1, 3

180     Do Until rs.EOF
182         txtTaxCollected.Text = Val(txtTaxCollected.Text) + Val(rs("TaxAmount"))
184         rs.MoveNext
        Loop
        
186     txtTaxCollected.Text = Format((NetSalesTotal * 0.16), "##.##")
    
        'For Discounts
188     sSQL = "SELECT TransactionNo, SUM(DISTINCT DiscountAmount) AS DiscountAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionNo"
    
190     sSQL = "SELECT Transactions.TransactionNo, SUM(DISTINCT Transactions.DiscountAmount) AS DiscountAmount FROM Transactions " & _
           "INNER JOIN DiscountSetup ON Transactions.DiscountCode = DiscountSetup.DiscountCode " & _
           "WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND (DiscountSetup.DiscountAmount <> 100) " & _
           "GROUP BY Transactions.TransactionNo"
        
192     Debug.Print sSQL
194     Set rs = New ADODB.Recordset
196     rs.Open sSQL, Conn, 1, 3

198     Do Until rs.EOF
200         txtDiscounts.Text = Val(txtDiscounts.Text) + Val(rs("DiscountAmount"))
202         rs.MoveNext
        Loop

204     txtTotalRevenue.Text = Val(txtNetSalesTotal.Text) + Val(txtTaxCollected.Text) '+ Val(txtDiscounts.Text)
        '174     txtTotalRevenue.Text = Val(txtNetSalesTotal.Text) + Val(txtTaxCollected.Text) + Val(txtDiscounts.Text)
        
        'For Total Cash Revenue
206     sSQL = "SELECT TransactionNo, SUM(ItemPrice * ItemQuantity) AS TotalAmount, TaxAmount, DiscountAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') AND (PaymentType='Cash') GROUP BY TaxAmount, DiscountAmount, TransactionNo " & _
           "HAVING (SUM(ItemPrice * ItemQuantity) > 0)ORDER BY TransactionNo"
208     Set rs = New ADODB.Recordset
210     rs.Open sSQL, Conn, 1, 3
        
212     Do Until rs.EOF
214         txtTotalCashRevenue.Text = Val(txtTotalCashRevenue.Text) + Val(rs("TotalAmount")) + Val(rs("TaxAmount")) - Val(rs("DiscountAmount"))
216         rs.MoveNext
        Loop
    
        'For Total Voids
218     sSQL = "SELECT COUNT(TransactionNo) AS TotalVoids, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' AND TransactionsVoid=1"
220     Set rs = New ADODB.Recordset
222     rs.Open sSQL, Conn, 1, 3

224     If rs.EOF = False Then
226         txtVoidsCount.Text = rs("TotalVoids")
228         txtVoidsTotalAmount.Text = Val(rs("TotalAmount") & "")
        End If
    
        'For Checks Begun
230     sSQL = "SELECT COUNT(DISTINCT TransactionNo) AS TotalChecks, SUM(ItemPrice + ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "'"

232     Set rs = New ADODB.Recordset
234     rs.Open sSQL, Conn, 1, 3
    
236     If rs.EOF = False Then
238         txtBegunCount.Text = rs("TotalChecks")
240         txtBegunAmount.Text = rs("TotalAmount")
        End If
    
        'For Checks Paid
242     sSQL = "SELECT COUNT(DISTINCT TransactionNo) AS TotalChecks, SUM(ItemPrice + ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' AND (TransactionClose = 0)"

244     Set rs = New ADODB.Recordset
246     rs.Open sSQL, Conn, 1, 3
    
248     If rs.EOF = False Then
250         txtPaidCount.Text = Val(txtBegunCount.Text) - Val(rs("TotalChecks") & "")
252         txtPaidAmount.Text = Val(txtBegunAmount.Text) - Val(rs("TotalAmount") & "")
        End If
    
        'For # Guest
254     sSQL = "SELECT TransactionNo, Customers AS Customers From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionNo, Customers"
256     Set rs = New ADODB.Recordset
258     rs.Open sSQL, Conn, 1, 3
    
260     Do Until rs.EOF
262         txtGuestAvgCount.Text = Val(txtGuestAvgCount.Text) + Val(rs("Customers"))
264         txtChecksAvgCount.Text = Val(txtChecksAvgCount.Text) + 1
266         rs.MoveNext
        Loop

268     txtGuestAvgAmount.Text = Round(txtNetSalesTotal.Text / txtGuestAvgCount.Text, 0)
270     txtChecksAvgAmount.Text = Round(txtNetSalesTotal.Text / txtChecksAvgCount.Text, 0)
   
        'For Bverages and Food
272     sSQL = "SELECT DISTINCT SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions INNER JOIN " & _
           "ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE Transactions.TransactionDate='" & strZTapeDate & "' AND ItemSetup.CatagoryCode='0004'"
            
274     Set rs = New ADODB.Recordset
276     rs.Open sSQL, Conn, 1, 3
    
278     If rs.EOF = False Then
280         txtBeverageAmount.Text = rs("TotalAmount")
282         txtFoodAmount.Text = Val(txtNetSalesTotal.Text) - Val(txtBeverageAmount.Text)
284         txtTotalMajorGroups.Text = Val(txtFoodAmount.Text) + Val(txtBeverageAmount.Text)
        End If
    
        'For 6 Inch
286     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' AND ItemName LIKE '%6%'"
288     Set rs = New ADODB.Recordset
290     rs.Open sSQL, Conn, 1, 3

292     If rs.EOF = False Then
294         txt6InchCount.Text = rs("ItemCount")
296         txt6InchAmount.Text = rs("TotalAmount")
        End If
        
        'For 6 Inch Deals Count
298     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount, ItemSetup.Quantity " & _
           "FROM Transactions INNER JOIN ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND " & _
           "(Transactions.ItemName LIKE N'DEAL%') GROUP BY ItemSetup.Quantity"
300     Debug.Print sSQL
302     Set rs = New ADODB.Recordset
304     rs.Open sSQL, Conn, 1, 3

306     Do Until rs.EOF
308         txt6InchCount.Text = Val(txt6InchCount.Text) - rs("ItemCount") + (rs("ItemCount") * rs("Quantity"))
310         rs.MoveNext
        Loop
        
        'For 12 Inch
312     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' AND ItemName LIKE '%12%'"
            
314     Set rs = New ADODB.Recordset
316     rs.Open sSQL, Conn, 1, 3
    
318     If rs.EOF = False Then
320         txt12InchCount.Text = rs("ItemCount")
322         txt12InchAmount.Text = rs("TotalAmount")
        End If
    
        'For Salad Inch
324     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' AND ItemName LIKE '%Salad%'"
            
326     Set rs = New ADODB.Recordset
328     rs.Open sSQL, Conn, 1, 3
    
330     If rs.EOF = False Then
332         txtSaladCount.Text = rs("ItemCount")
334         txtSaladAmount.Text = Val(rs("TotalAmount") & "")
        End If
    
        'For Extra & Add On
336     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions INNER JOIN " & _
           "ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE Transactions.TransactionDate='" & strZTapeDate & "' GROUP BY ItemSetup.CatagoryCode " & _
           "HAVING (ItemSetup.CatagoryCode = N'0007')"
338     Set rs = New ADODB.Recordset
340     rs.Open sSQL, Conn, 1, 3
    
342     If rs.EOF = False Then
344         txtExtraAddonCount.Text = rs("ItemCount")
346         txtExtraAddonAmount.Text = Val(rs("TotalAmount") & "")
        End If
    
        'For SODA
348     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions INNER JOIN " & _
           "ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE Transactions.TransactionDate='" & strZTapeDate & "' AND (Transactions.ItemCode = N'0001' OR " & _
           " Transactions.ItemCode = '0019' OR Transactions.ItemCode = '0055')"
350     Set rs = New ADODB.Recordset
352     rs.Open sSQL, Conn, 1, 3
        
354     If rs.EOF = False Then
356         txtSodaCount.Text = rs("ItemCount")
358         txtSodaAmount.Text = rs("Totalamount")
        End If
    
        'For Other Beverages
360     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions INNER JOIN " & _
           "ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND (Transactions.ItemCode='0002' OR " & _
           "Transactions.ItemCode='0003' OR Transactions.ItemCode='0065')"
362     Set rs = New ADODB.Recordset
364     rs.Open sSQL, Conn, 1, 3
    
366     If rs.EOF = False Then
368         txtOtherBeveragesCount.Text = rs("ItemCount")
370         txtOtherBeveragesAmount.Text = rs("TotalAmount")
        End If
    
        'For Chips and Cookies
372     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') AND (ItemCode='0014' OR ItemCode='0012' OR ItemCode='0013' OR ItemCode='0070')"
374     Set rs = New ADODB.Recordset
376     rs.Open sSQL, Conn, 1, 3
    
378     If rs.EOF = False Then
380         txtMiscellaneousCount.Text = rs("ItemCount")
382         txtMiscellaneousAmount.Text = rs("TotalAmount")
        End If
    
        'For Food Total
384     txtFoodTotalAmount.Text = Val(txt6InchAmount.Text) + Val(txt12InchAmount.Text) + Val(txtSaladAmount.Text) + Val(txtExtraAddonAmount.Text) + Val(txtSodaAmount.Text) + Val(txtOtherBeveragesAmount.Text) + Val(txtMiscellaneousAmount.Text)
    
        'For Free 6 Inch
386     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') AND (ItemName LIKE '%6%') AND (ItemQuantity = 0)"
388     Set rs = New ADODB.Recordset
390     rs.Open sSQL, Conn, 1, 3
    
392     If rs.EOF = False Then
394         txtFree6InchCount.Text = rs("ItemCount")
        End If
    
        'For Free 12 Inch
396     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') AND (ItemName LIKE '%12%') AND (ItemQuantity = 0)"
398     Set rs = New ADODB.Recordset
400     rs.Open sSQL, Conn, 1, 3
    
402     If rs.EOF = False Then
404         txtFree12InchCount.Text = rs("ItemCount")
        End If
    
        'For Free Salad Inch
406     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') AND (ItemName LIKE '%Salad%') AND (ItemQuantity = 0)"
408     Set rs = New ADODB.Recordset
410     rs.Open sSQL, Conn, 1, 3
    
412     If rs.EOF = False Then
414         txtFreeSaladCount.Text = rs("ItemCount")
        End If
    
416     txtTotalFreeAmount.Text = Val(txtFree6InchCount.Text) + Val(txtFree12InchCount.Text) + Val(txtFreeSaladCount.Text)
    
        'For Cash Financials
418     sSQL = "SELECT TransactionNo AS TransactionNo, SUM(ItemPrice * ItemQuantity) AS NetAmount, TaxAmount, DiscountAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') GROUP BY PaymentType, TransactionNo, TaxAmount, DiscountAmount " & _
           "HAVING (PaymentType = 'Cash') AND (SUM(ItemPrice * ItemQuantity) > 0) ORDER BY TransactionNo"
        
420     Debug.Print sSQL
422     Set rs = New ADODB.Recordset
424     rs.Open sSQL, Conn, 1, 3

426     Do Until rs.EOF
428         txtCashCount.Text = Val(txtCashCount.Text) + 1
430         txtCashAmount.Text = Val(txtCashAmount.Text) + (Val(rs("NetAmount") & "") + Val(rs("TaxAmount") & "") - Val(rs("DiscountAmount")))
432         rs.MoveNext
        Loop
            
        'For Credit Card Financials
434     sSQL = "SELECT TransactionNo AS TransactionNo, SUM(ItemPrice * ItemQuantity) AS NetAmount, TaxAmount, DiscountAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') GROUP BY PaymentType, TransactionNo, TaxAmount, DiscountAmount " & _
           "HAVING (PaymentType = 'Credit Card') AND (SUM(ItemPrice * ItemQuantity) > 0) ORDER BY TransactionNo"
436     Set rs = New ADODB.Recordset
438     rs.Open sSQL, Conn, 1, 3
    
440     Do Until rs.EOF
442         txtCreditCardCount.Text = Val(txtCreditCardCount.Text) + 1
444         txtCreditCardAmount.Text = Val(txtCreditCardAmount.Text) + (Val(rs("NetAmount") & "") + Val(rs("TaxAmount") & "") - Val(rs("DiscountAmount") & ""))
446         rs.MoveNext
        Loop
    
        'For Credit A/C Financials
448     sSQL = "SELECT TransactionNo AS TransactionNo, SUM(ItemPrice * ItemQuantity) AS NetAmount, TaxAmount, DiscountAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') GROUP BY PaymentType, TransactionNo, TaxAmount, DiscountAmount " & _
           "HAVING (PaymentType = 'Credit A/C') AND (SUM(ItemPrice * ItemQuantity) > 0) ORDER BY TransactionNo"
450     Set rs = New ADODB.Recordset
452     rs.Open sSQL, Conn, 1, 3
    
454     Do Until rs.EOF
456         txtCreditACCount.Text = Val(txtCreditACCount.Text) + 1
458         txtCreditACAmount.Text = Val(txtCreditACAmount.Text) + (Val(rs("NetAmount") & "") + Val(rs("TaxAmount") & "") - Val(rs("DiscountAmount") & ""))
460         rs.MoveNext
        Loop

462     txtFinancialsTotal.Text = Val(txtCashAmount.Text) + Val(txtCreditCardAmount.Text) + Val(txtCreditACAmount.Text)
        
        'Update the daily total to the new table
464     sSQL = "SELECT * From DailySummary WHERE TransactionDate='" & strZTapeDate & "'"
466     Set rsDailySummary = New ADODB.Recordset
468     rsDailySummary.Open sSQL, Conn, 1, 3
    
470     If rsDailySummary.EOF = True Then
472         rsDailySummary.AddNew
474         rsDailySummary("TransactionDate") = strZTapeDate
        End If

476     rsDailySummary("NetAmount") = txtNetSalesTotal.Text
478     rsDailySummary("DiscountAmount") = txtDiscounts.Text
480     rsDailySummary("TaxesAmount") = txtTaxCollected.Text
482     rsDailySummary.Update
    
484     txtTotalCashRevenue.Text = Val(txtTotalRevenue.Text) - Val(txtDiscounts.Text) - Val(txtCreditACAmount.Text) - Val(txtCreditCardAmount.Text)
        'Changes for the Subway
486     txtCashAmount.Text = Val(txtTotalRevenue.Text) - Val(txtDiscounts.Text) - Val(txtCreditACAmount.Text) - Val(txtCreditCardAmount.Text)
488     txtFinancialsTotal.Text = Val(txtCashAmount.Text) + Val(txtCreditCardAmount.Text) + Val(txtCreditACAmount.Text)

490     sSQL = "SELECT SUM(NetAmount) AS NetAmount, SUM(DiscountAmount) AS DiscountAmount, SUM(TaxesAmount) AS TaxesAmount From DailySummary " & _
           "WHERE (TransactionDate<='" & strZTapeDate & "')"

492     Set rs = New ADODB.Recordset
494     rs.Open sSQL, Conn, 1, 3
    
496     If rs.EOF = False Then
498         txtNRGT.Text = Val(rs("NetAmount")) + Val(rs("TaxesAmount")) - Val(rs("DiscountAmount"))
        End If

        '<EhFooter>
        Exit Sub

ActiveReport_ReportStart_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.repZTapeReportStar.ActiveReport_ReportStart " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
