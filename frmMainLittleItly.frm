VERSION 5.00
Object = "{7888C00A-4808-4D27-9AAE-BD36EC13D16F}#1.0#0"; "LVbuttons.ocx"
Begin VB.Form frmMainLittleItly 
   Appearance      =   0  'Flat
   BackColor       =   &H80000005&
   BorderStyle     =   0  'None
   Caption         =   "Little Itly"
   ClientHeight    =   7605
   ClientLeft      =   3345
   ClientTop       =   2655
   ClientWidth     =   6585
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmMainLittleItly.frx":0000
   LinkTopic       =   "Form1"
   LockControls    =   -1  'True
   MaxButton       =   0   'False
   MinButton       =   0   'False
   Picture         =   "frmMainLittleItly.frx":000C
   ScaleHeight     =   7605
   ScaleWidth      =   6585
   ShowInTaskbar   =   0   'False
   WindowState     =   2  'Maximized
   Begin VB.PictureBox Picture4 
      Appearance      =   0  'Flat
      BackColor       =   &H80000005&
      ForeColor       =   &H80000008&
      Height          =   2550
      Left            =   7635
      Picture         =   "frmMainLittleItly.frx":6EE16
      ScaleHeight     =   2520
      ScaleWidth      =   4500
      TabIndex        =   24
      Top             =   3600
      Width           =   4530
   End
   Begin VB.PictureBox Picture1 
      Appearance      =   0  'Flat
      AutoSize        =   -1  'True
      BackColor       =   &H80000005&
      ForeColor       =   &H80000008&
      Height          =   1785
      Left            =   7635
      Picture         =   "frmMainLittleItly.frx":96C38
      ScaleHeight     =   1755
      ScaleWidth      =   4500
      TabIndex        =   4
      Top             =   1770
      Width           =   4530
   End
   Begin VB.PictureBox Picture3 
      Appearance      =   0  'Flat
      BackColor       =   &H00404000&
      ForeColor       =   &H80000008&
      Height          =   1215
      Left            =   585
      ScaleHeight     =   1185
      ScaleWidth      =   14040
      TabIndex        =   1
      Top             =   9120
      Width           =   14070
      Begin VB.Label lblTransactionDate 
         Alignment       =   2  'Center
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "06-jul-2007"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   27.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         ForeColor       =   &H0000FFFF&
         Height          =   645
         Left            =   165
         TabIndex        =   25
         Top             =   555
         Width           =   13710
      End
      Begin VB.Label lblLabel1 
         Alignment       =   2  'Center
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "POS designed for LITTLE ITALY PIZZA"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   18
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         ForeColor       =   &H0000FFFF&
         Height          =   420
         Left            =   165
         TabIndex        =   2
         Top             =   135
         Width           =   13710
         WordWrap        =   -1  'True
      End
   End
   Begin VB.CommandButton cmdInsertMenu 
      Caption         =   "InsertMenu"
      Height          =   540
      Left            =   705
      TabIndex        =   0
      Top             =   960
      Visible         =   0   'False
      Width           =   1665
   End
   Begin LVbuttons.LaVolpeButton cmdCatagorySetup 
      Height          =   570
      Left            =   540
      TabIndex        =   3
      Top             =   1755
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "Catagory Setup"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B07CE
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdItemsSetup 
      Height          =   570
      Left            =   540
      TabIndex        =   5
      Top             =   5565
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "Items Setup"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B07EA
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdDiscountSetup 
      Height          =   570
      Left            =   540
      TabIndex        =   6
      Top             =   6525
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "Discount Setup"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B0806
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdPOSSale 
      Height          =   570
      Left            =   540
      TabIndex        =   7
      Top             =   8415
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "POS Sale"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B0822
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdDockItPrint 
      Height          =   570
      Left            =   12465
      TabIndex        =   8
      Top             =   1755
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "DockIt Print"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B083E
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdDailyMeal 
      Height          =   570
      Left            =   12465
      TabIndex        =   9
      Top             =   2587
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "Daily Meal"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B085A
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdSalesTax 
      Height          =   570
      Left            =   540
      TabIndex        =   10
      Top             =   7470
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "Sales Tax Setup"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B0876
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdExit 
      Height          =   570
      Left            =   12465
      TabIndex        =   11
      Top             =   8415
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "E&xit"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   65535
      FCOL            =   4210688
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B0892
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdDiscountReport 
      Height          =   570
      Left            =   12465
      TabIndex        =   12
      Top             =   4251
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "Discount Report"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B08AE
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdUserManagement 
      Height          =   570
      Left            =   540
      TabIndex        =   13
      Top             =   2715
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "User Management"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B08CA
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdEndofDay 
      Height          =   570
      Left            =   12465
      TabIndex        =   14
      Top             =   7579
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "End of Day"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B08E6
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdChangePassword 
      Height          =   570
      Left            =   540
      TabIndex        =   15
      Top             =   4620
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "Change Password"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B0902
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdDailyTransaction 
      Height          =   570
      Left            =   12465
      TabIndex        =   16
      Top             =   5915
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "Daily Transaction"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B091E
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdCatagoryWise 
      Height          =   570
      Left            =   12465
      TabIndex        =   17
      Top             =   3419
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "Catagory Wise Daily Meal"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B093A
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdEmployeesSetup 
      Height          =   570
      Left            =   540
      TabIndex        =   18
      Top             =   3660
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "Employees Setup"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B0956
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdMonthlySummary 
      Height          =   570
      Left            =   12465
      TabIndex        =   19
      Top             =   6747
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "Monthly Summary"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B0972
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin LVbuttons.LaVolpeButton cmdDeliveryReport 
      Height          =   570
      Left            =   12465
      TabIndex        =   20
      Top             =   5083
      Width           =   2205
      _ExtentX        =   3889
      _ExtentY        =   1005
      BTYPE           =   3
      TX              =   "Delivery Report"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   2
      BCOL            =   4210688
      FCOL            =   65535
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmMainLittleItly.frx":B098E
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin VB.PictureBox Picture2 
      Appearance      =   0  'Flat
      BackColor       =   &H00FFFFFF&
      ForeColor       =   &H80000008&
      Height          =   1305
      Left            =   540
      ScaleHeight     =   1275
      ScaleWidth      =   14145
      TabIndex        =   26
      Top             =   9075
      Width           =   14175
   End
   Begin VB.Label lblPOS 
      Alignment       =   2  'Center
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Point of Sale"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   36
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H0000FFFF&
      Height          =   840
      Left            =   165
      TabIndex        =   22
      Top             =   420
      Width           =   5085
   End
   Begin VB.Label lblPointOf 
      Alignment       =   2  'Center
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Point of Sale"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   36
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H00404000&
      Height          =   840
      Left            =   135
      TabIndex        =   21
      Top             =   390
      Width           =   5085
   End
   Begin VB.Label Label1 
      Alignment       =   2  'Center
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Point of Sale"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   36
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H00404000&
      Height          =   840
      Left            =   195
      TabIndex        =   23
      Top             =   450
      Width           =   5085
   End
End
Attribute VB_Name = "frmMainLittleItly"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub cmdCatagorySetup_Click()
        '<EhHeader>
        On Error GoTo cmdCatagorySetup_Click_Err
        '</EhHeader>
    
100     frmItemCatagory.Show 1
    
        '<EhFooter>
        Exit Sub

cmdCatagorySetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.frmMainSubWay.cmdCatagorySetup_Click " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdCatagoryWise_Click()
        '<EhHeader>
        On Error GoTo cmdCatagoryWise_Click_Err
        '</EhHeader>

'100     frmCatagoryWiseMealReport.Show 1

        '<EhFooter>
        Exit Sub

cmdCatagoryWise_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.cmdCatagoryWise_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdChangePassword_Click()
        '<EhHeader>
        On Error GoTo cmdChangePassword_Click_Err
        '</EhHeader>

100     frmChangePassword.Show 1

        '<EhFooter>
        Exit Sub

cmdChangePassword_Click_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.frmMainSubWay.cmdChangePassword_Click " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdInsertMenu_Click()
        '<EhHeader>
        On Error GoTo cmdInsertMenu_Click_Err
        '</EhHeader>
    
        Dim obj As Object
        Dim iCounter As String
100     iCounter = 1

102     sSQL = "Delete From MenuRights"
104     Conn.Execute sSQL
        
106     sSQL = "Delete From UserMenu"
108     Conn.Execute sSQL
        
110     For Each obj In Me

112         If TypeOf obj Is LaVolpeButton Then

114             Debug.Print obj.Name & "        " & obj.Caption
                
116             iCounter = Format$(iCounter, "0##")
118             sSQL = "INSERT INTO UserMenu (MenuID, MenuName, MenuCaption, MenuType) " & _
                   "VALUES ('" & iCounter & "', '" & obj.Name & "', '" & obj.Caption & "', '')"
120             Conn.Execute sSQL
122             iCounter = Val(iCounter) + 1

            End If

        Next
    
        '<EhFooter>
        Exit Sub

cmdInsertMenu_Click_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.frmMainSubWay.cmdInsertMenu_Click " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdDailyMeal_Click()
        '<EhHeader>
        On Error GoTo cmdDailyMeal_Click_Err
        '</EhHeader>
    
100     frmDailyMealReport.Show 1

        '<EhFooter>
        Exit Sub

cmdDailyMeal_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.cmdDailyMeal_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdDailyTransaction_Click()
        '<EhHeader>
        On Error GoTo cmdDailyTransaction_Click_Err
        '</EhHeader>

100     frmDailyTransactionDetail.Show 1

        '<EhFooter>
        Exit Sub

cmdDailyTransaction_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.cmdDailyTransaction_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdDeliveryReport_Click()
        '<EhHeader>
        On Error GoTo cmdDeliveryReport_Click_Err
        '</EhHeader>

100     frmDeliveryReport.Show 1

        '<EhFooter>
        Exit Sub

cmdDeliveryReport_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.cmdDeliveryReport_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdDiscountReport_Click()
        '<EhHeader>
        On Error GoTo cmdDiscountReport_Click_Err
        '</EhHeader>

100     frmDiscountReport.Show 1

        '<EhFooter>
        Exit Sub

cmdDiscountReport_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.cmdDiscountReport_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdDiscountSetup_Click()
        '<EhHeader>
        On Error GoTo cmdDiscountSetup_Click_Err
        '</EhHeader>
    
100     frmDiscountSetup.Show 1
    
        '<EhFooter>
        Exit Sub

cmdDiscountSetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.frmMainSubWay.cmdDiscountSetup_Click " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdDockItPrint_Click()
        '<EhHeader>
        On Error GoTo cmdDockItPrint_Click_Err
        '</EhHeader>
    
100     frmDockitPrint.Show 1

        '<EhFooter>
        Exit Sub

cmdDockItPrint_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.cmdDockItPrint_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdEmployeesSetup_Click()
        '<EhHeader>
        On Error GoTo cmdEmployeesSetup_Click_Err
        '</EhHeader>
    
100     frmEmployees.Show 1
    
        '<EhFooter>
        Exit Sub

cmdEmployeesSetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.cmdEmployeesSetup_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdEndofDay_Click()
        '<EhHeader>
        On Error GoTo cmdEndofDay_Click_Err
        '</EhHeader>

100     frmEndOfDay.Show 1

        '<EhFooter>
        Exit Sub

cmdEndofDay_Click_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.frmMainSubWay.cmdEndofDay_Click " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdExit_Click()
        '<EhHeader>
        On Error GoTo cmdExit_Click_Err
        '</EhHeader>
    
100     End
    
        '<EhFooter>
        Exit Sub

cmdExit_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.cmdExit_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdItemsSetup_Click()
        '<EhHeader>
        On Error GoTo cmdItemsSetup_Click_Err
        '</EhHeader>

100     frmItemSetup.Show 1

        '<EhFooter>
        Exit Sub

cmdItemsSetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.frmMainSubWay.cmdItemsSetup_Click " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdMonthlySummary_Click()
        '<EhHeader>
        On Error GoTo cmdMonthlySummary_Click_Err
        '</EhHeader>
    
100     frmDateWiseTransactionSummary.Show 1
    
        '<EhFooter>
        Exit Sub

cmdMonthlySummary_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.cmdMonthlySummary_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdPOSSale_Click()
        '<EhHeader>
        On Error GoTo cmdPOSSale_Click_Err
        '</EhHeader>

100     frmPointOfSaleLittleItaly.Show 1

        '<EhFooter>
        Exit Sub

cmdPOSSale_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.cmdPOSSale_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdSalesTax_Click()
        '<EhHeader>
        On Error GoTo cmdSalesTax_Click_Err
        '</EhHeader>

100     frmTaxesSetup.Show 1

        '<EhFooter>
        Exit Sub

cmdSalesTax_Click_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.frmMainSubWay.cmdSalesTax_Click " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdUserManagement_Click()
        '<EhHeader>
        On Error GoTo cmdUserManagement_Click_Err
        '</EhHeader>
    
100     frmUserRights.Show 1

        '<EhFooter>
        Exit Sub

cmdUserManagement_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.cmdUserManagement_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdZTapePrint_Click()
        '<EhHeader>
        On Error GoTo cmdZTapePrint_Click_Err
        '</EhHeader>

100     frmZTapePrint.Show 1

        '<EhFooter>
        Exit Sub

cmdZTapePrint_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.cmdZTapePrint_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>
    
'''100     If Command = "Management" Then
'''104         cmdCatagorySetup.Enabled = True
'''102         cmdUserManagement.Enabled = True
'''            cmdEmployeesSetup.Enabled = True
'''106         cmdDiscountSetup.Enabled = True
'''108         cmdSalesTax.Enabled = True
'''110         cmdItemsSetup.Enabled = True
'''        End If
        
        MenuRightsImplement
        
112     CenterForm Me
114     lblTransactionDate.Caption = "Current POS Date: " & Format(TransactionDate, "dd/mmm/yyyy")

        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMainSubWay.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub MenuRightsImplement()
        '<EhHeader>
        On Error GoTo MenuRightsImplement_Err
        '</EhHeader>

        'On Error Resume Next

        Dim ObjMenu As Object

100     If UserID = "Shahid" Then

            Exit Sub

        End If
    
102     For Each ObjMenu In Me

104         If TypeOf ObjMenu Is LaVolpeButton Then
            

108                 ObjMenu.Enabled = False

            End If

        Next
   
110     For i = 1 To 4

112         sSQL = "SELECT UserMenu.MenuName, UserMenu.MenuCaption  FROM MenuRights INNER JOIN UserMenu ON MenuRights.MenuID = UserMenu.MenuID " & _
               "WHERE MenuRights.MenuTypeID=" & i & " And MenuRights.UserID='" & UserID & "'"
114         Set rs = New ADODB.Recordset
116         rs.Open sSQL, Conn, 1, 3

118         Do Until rs.EOF

120             For Each ObjMenu In Me

122                 If TypeOf ObjMenu Is LaVolpeButton Then

124                     If ObjMenu.Name = Trim(rs("MenuName")) Then
                            
126                         ObjMenu.Enabled = True

                        End If

                    End If

                Next

128             rs.MoveNext

            Loop

        Next

        cmdExit.Enabled = True
        
        '<EhFooter>
        Exit Sub

MenuRightsImplement_Err:
        MsgBox Err.Description & vbCrLf & _
               "in TimeManager.frmMain.MenuRightsImplement " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub




