VERSION 5.00
Object = "{CDF3B183-D408-11CE-AE2C-0080C786E37D}#3.0#0"; "Edt32x30.ocx"
Object = "{B02F3647-766B-11CE-AF28-C3A2FBE76A13}#3.0#0"; "SPR32X30.ocx"
Object = "{648A5603-2C6E-101B-82B6-000000000014}#1.1#0"; "MSCOMM32.OCX"
Begin VB.Form frmPointOfSaleLittleItaly 
   Appearance      =   0  'Flat
   BackColor       =   &H00EEEEEE&
   Caption         =   "Point of Sale (by Intelysol)"
   ClientHeight    =   8835
   ClientLeft      =   615
   ClientTop       =   1950
   ClientWidth     =   12825
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmPointOfSaleLittleItaly.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   ScaleHeight     =   8835
   ScaleWidth      =   12825
   Begin VB.CommandButton Command1 
      Caption         =   "Command1"
      Height          =   375
      Left            =   7125
      TabIndex        =   55
      Top             =   8430
      Visible         =   0   'False
      Width           =   1935
   End
   Begin VB.TextBox txtDeliveredBy 
      Height          =   330
      Left            =   6510
      TabIndex        =   53
      Text            =   "Delivered by"
      Top             =   7740
      Width           =   2010
   End
   Begin VB.TextBox txtDiscountName 
      Height          =   330
      Left            =   6240
      TabIndex        =   52
      Text            =   "DiscountName"
      Top             =   7365
      Width           =   2280
   End
   Begin VB.ComboBox cmbEmployees 
      Height          =   315
      ItemData        =   "frmPointOfSaleLittleItaly.frx":000C
      Left            =   2775
      List            =   "frmPointOfSaleLittleItaly.frx":000E
      Style           =   2  'Dropdown List
      TabIndex        =   51
      Top             =   8955
      Width           =   2625
   End
   Begin VB.PictureBox Picture3 
      Appearance      =   0  'Flat
      BackColor       =   &H80000005&
      Enabled         =   0   'False
      ForeColor       =   &H80000008&
      Height          =   555
      Left            =   12570
      ScaleHeight     =   525
      ScaleWidth      =   2535
      TabIndex        =   45
      Top             =   7500
      Width           =   2565
      Begin EditLib.fpCurrency txtPayableAmount 
         Height          =   555
         Left            =   -15
         TabIndex        =   46
         Top             =   -15
         Width           =   2565
         _Version        =   196608
         _ExtentX        =   4524
         _ExtentY        =   979
         Enabled         =   -1  'True
         MousePointer    =   0
         Object.TabStop         =   -1  'True
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   20.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         BackColor       =   11856344
         ForeColor       =   -2147483640
         ThreeDInsideStyle=   0
         ThreeDInsideHighlightColor=   -2147483633
         ThreeDInsideShadowColor=   -2147483642
         ThreeDInsideWidth=   1
         ThreeDOutsideStyle=   0
         ThreeDOutsideHighlightColor=   -2147483628
         ThreeDOutsideShadowColor=   -2147483632
         ThreeDOutsideWidth=   1
         ThreeDFrameWidth=   0
         BorderStyle     =   1
         BorderColor     =   -2147483642
         BorderWidth     =   1
         ButtonDisable   =   0   'False
         ButtonHide      =   0   'False
         ButtonIncrement =   1
         ButtonMin       =   0
         ButtonMax       =   100
         ButtonStyle     =   0
         ButtonWidth     =   0
         ButtonWrap      =   -1  'True
         ButtonDefaultAction=   -1  'True
         ThreeDText      =   0
         ThreeDTextHighlightColor=   -2147483633
         ThreeDTextShadowColor=   -2147483632
         ThreeDTextOffset=   1
         AlignTextH      =   2
         AlignTextV      =   0
         AllowNull       =   0   'False
         NoSpecialKeys   =   0
         AutoAdvance     =   0   'False
         AutoBeep        =   0   'False
         CaretInsert     =   0
         CaretOverWrite  =   3
         UserEntry       =   0
         HideSelection   =   -1  'True
         InvalidColor    =   -2147483637
         InvalidOption   =   0
         MarginLeft      =   3
         MarginTop       =   3
         MarginRight     =   3
         MarginBottom    =   3
         NullColor       =   -2147483637
         OnFocusAlignH   =   0
         OnFocusAlignV   =   0
         OnFocusNoSelect =   0   'False
         OnFocusPosition =   0
         ControlType     =   0
         Text            =   "Rs:0.00"
         CurrencyDecimalPlaces=   -1
         CurrencyNegFormat=   0
         CurrencyPlacement=   0
         CurrencySymbol  =   ""
         DecimalPoint    =   ""
         FixedPoint      =   -1  'True
         LeadZero        =   0
         MaxValue        =   "9000000000"
         MinValue        =   "-9000000000"
         NegToggle       =   0   'False
         Separator       =   ""
         UseSeparator    =   0   'False
         IncInt          =   1
         IncDec          =   1
         BorderGrayAreaColor=   -2147483637
         ThreeDOnFocusInvert=   0   'False
         ThreeDFrameColor=   -2147483633
         Appearance      =   1
         BorderDropShadow=   0
         BorderDropShadowColor=   -2147483632
         BorderDropShadowWidth=   3
         ButtonColor     =   -2147483633
         AutoMenu        =   0   'False
         ButtonAlign     =   0
         OLEDropMode     =   0
         OLEDragMode     =   0
      End
   End
   Begin VB.ComboBox cmbPaymentType 
      Height          =   315
      ItemData        =   "frmPointOfSaleLittleItaly.frx":0010
      Left            =   60
      List            =   "frmPointOfSaleLittleItaly.frx":001D
      Style           =   2  'Dropdown List
      TabIndex        =   35
      Top             =   8955
      Width           =   2625
   End
   Begin VB.PictureBox Picture2 
      Appearance      =   0  'Flat
      AutoSize        =   -1  'True
      BackColor       =   &H80000005&
      ForeColor       =   &H80000008&
      Height          =   1065
      Left            =   11850
      Picture         =   "frmPointOfSaleLittleItaly.frx":0040
      ScaleHeight     =   1035
      ScaleWidth      =   3465
      TabIndex        =   34
      Top             =   -15
      Visible         =   0   'False
      Width           =   3495
   End
   Begin FPSpread.vaSpread vaSpread3 
      Height          =   2040
      Left            =   12885
      TabIndex        =   30
      Top             =   1785
      Width           =   2400
      _Version        =   196608
      _ExtentX        =   4233
      _ExtentY        =   3598
      _StockProps     =   64
      Enabled         =   0   'False
      DisplayRowHeaders=   0   'False
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   3
      MaxRows         =   10
      ScrollBars      =   2
      SpreadDesigner  =   "frmPointOfSaleLittleItaly.frx":072C
      UserResize      =   1
   End
   Begin EditLib.fpLongInteger txtCustomers 
      Height          =   480
      Left            =   4275
      TabIndex        =   24
      Top             =   435
      Width           =   1170
      _Version        =   196608
      _ExtentX        =   2064
      _ExtentY        =   847
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   14.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   -2147483643
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   1
      ButtonMax       =   100
      ButtonStyle     =   1
      ButtonWidth     =   0
      ButtonWrap      =   0   'False
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   0
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "1"
      MaxValue        =   "100"
      MinValue        =   "1"
      NegFormat       =   1
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin VB.ComboBox cmbTransactionType 
      Height          =   315
      ItemData        =   "frmPointOfSaleLittleItaly.frx":0AD8
      Left            =   2100
      List            =   "frmPointOfSaleLittleItaly.frx":0AE2
      Style           =   2  'Dropdown List
      TabIndex        =   23
      Top             =   60
      Width           =   3375
   End
   Begin VB.ComboBox cmbDiscount 
      Height          =   315
      Left            =   6240
      Style           =   2  'Dropdown List
      TabIndex        =   18
      Top             =   6998
      Width           =   2280
   End
   Begin EditLib.fpCurrency txtTotalAmount 
      Height          =   300
      Left            =   10575
      TabIndex        =   16
      Top             =   7005
      Width           =   1830
      _Version        =   196608
      _ExtentX        =   3228
      _ExtentY        =   529
      Enabled         =   0   'False
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin VB.TextBox txtCardNumber 
      Appearance      =   0  'Flat
      Height          =   285
      Left            =   7275
      TabIndex        =   12
      Text            =   "Text1"
      Top             =   12855
      Visible         =   0   'False
      Width           =   2400
   End
   Begin MSCommLib.MSComm MSComm1 
      Left            =   9075
      Top             =   12375
      _ExtentX        =   1005
      _ExtentY        =   1005
      _Version        =   393216
      CommPort        =   2
      DTREnable       =   -1  'True
      InputLen        =   14
      RThreshold      =   14
      BaudRate        =   2400
      SThreshold      =   14
   End
   Begin VB.PictureBox picEmployeePicture 
      Appearance      =   0  'Flat
      BackColor       =   &H80000005&
      ForeColor       =   &H80000008&
      Height          =   1800
      Left            =   7275
      ScaleHeight     =   1770
      ScaleWidth      =   2370
      TabIndex        =   11
      TabStop         =   0   'False
      Top             =   11340
      Visible         =   0   'False
      Width           =   2400
   End
   Begin Point_of_Sale_System.lvButtons_H cmdStart 
      Height          =   840
      Left            =   30
      TabIndex        =   8
      Top             =   60
      Width           =   1860
      _ExtentX        =   3281
      _ExtentY        =   1482
      Caption         =   "Start"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPointOfSaleLittleItaly.frx":0AF9
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   1
      Left            =   6855
      TabIndex        =   3
      Tag             =   "0005"
      Top             =   1095
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Cookies Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPointOfSaleLittleItaly.frx":696B
      ImgSize         =   24
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.ucStatusBar UcSStatText 
      Height          =   330
      Left            =   -15
      TabIndex        =   2
      Top             =   10455
      Width           =   15375
      _ExtentX        =   27120
      _ExtentY        =   582
      Text            =   " Point of Sale-={by www.intelysol.com"
   End
   Begin FPSpread.vaSpread vaSpread2 
      Height          =   5190
      Left            =   5070
      TabIndex        =   1
      Top             =   1770
      Width           =   7740
      _Version        =   196608
      _ExtentX        =   13653
      _ExtentY        =   9155
      _StockProps     =   64
      EditEnterAction =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   9
      MaxRows         =   20
      ScrollBars      =   2
      SpreadDesigner  =   "frmPointOfSaleLittleItaly.frx":8CED
   End
   Begin FPSpread.vaSpread vaSpread1 
      Height          =   6750
      Left            =   45
      TabIndex        =   0
      Top             =   1755
      Width           =   4950
      _Version        =   196608
      _ExtentX        =   8731
      _ExtentY        =   11906
      _StockProps     =   64
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   3
      MaxRows         =   20
      ScrollBars      =   2
      SpreadDesigner  =   "frmPointOfSaleLittleItaly.frx":94A7
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   0
      Left            =   1759
      TabIndex        =   4
      Tag             =   "0002"
      Top             =   1110
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Beef    Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPointOfSaleLittleItaly.frx":9A23
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   2
      Left            =   5157
      TabIndex        =   5
      Tag             =   "0004"
      Top             =   1110
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Ice Cream && Drink"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPointOfSaleLittleItaly.frx":24685
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   3
      Left            =   8565
      TabIndex        =   6
      Tag             =   "0006"
      Top             =   1110
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Others && Salats"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPointOfSaleLittleItaly.frx":26A07
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   4
      Left            =   15
      TabIndex        =   7
      Tag             =   "0001"
      Top             =   1110
      Width           =   1710
      _ExtentX        =   3016
      _ExtentY        =   1032
      Caption         =   "Chicken Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      CapStyle        =   1
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPointOfSaleLittleItaly.frx":275D9
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdClose 
      Height          =   840
      Left            =   13470
      TabIndex        =   9
      Top             =   9495
      Width           =   1860
      _ExtentX        =   3281
      _ExtentY        =   1482
      Caption         =   "Close"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      cGradient       =   0
      Mode            =   0
      Value           =   0   'False
      cBack           =   -2147483633
   End
   Begin VB.Frame Frame1 
      BackColor       =   &H00EEEEEE&
      Height          =   120
      Left            =   -75
      TabIndex        =   10
      Top             =   9315
      Width           =   15810
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   5
      Left            =   3458
      TabIndex        =   13
      Tag             =   "0003"
      Top             =   1110
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Rice    Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPointOfSaleLittleItaly.frx":281AB
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   6
      Left            =   10254
      TabIndex        =   14
      Tag             =   "0007"
      Top             =   1110
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Extra Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPointOfSaleLittleItaly.frx":2A52D
      ImgSize         =   24
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   7
      Left            =   11955
      TabIndex        =   15
      Tag             =   "0008"
      Top             =   1110
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Sea Foods"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPointOfSaleLittleItaly.frx":2FD1F
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin EditLib.fpCurrency txtDiscount 
      Height          =   300
      Left            =   10575
      TabIndex        =   20
      Top             =   7375
      Width           =   1830
      _Version        =   196608
      _ExtentX        =   3228
      _ExtentY        =   529
      Enabled         =   0   'False
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin Point_of_Sale_System.lvButtons_H cmdVoid 
      Height          =   840
      Left            =   8295
      TabIndex        =   26
      Top             =   9510
      Visible         =   0   'False
      Width           =   1650
      _ExtentX        =   2910
      _ExtentY        =   1482
      Caption         =   "Void"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPointOfSaleLittleItaly.frx":35511
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdPayments 
      Height          =   840
      Left            =   30
      TabIndex        =   27
      Top             =   9510
      Width           =   2415
      _ExtentX        =   4260
      _ExtentY        =   1482
      Caption         =   "Payments"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPointOfSaleLittleItaly.frx":378F3
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin VB.Frame Frame2 
      BackColor       =   &H00EEEEEE&
      Height          =   120
      Left            =   -45
      TabIndex        =   28
      Top             =   960
      Width           =   15810
   End
   Begin Point_of_Sale_System.lvButtons_H cmdDelivery 
      Height          =   840
      Left            =   2640
      TabIndex        =   31
      Top             =   9510
      Width           =   2415
      _ExtentX        =   4260
      _ExtentY        =   1482
      Caption         =   "Delivery"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPointOfSaleLittleItaly.frx":3D515
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin EditLib.fpCurrency txtSalesTaxAmount 
      Height          =   300
      Left            =   10575
      TabIndex        =   32
      Top             =   7745
      Width           =   1830
      _Version        =   196608
      _ExtentX        =   3228
      _ExtentY        =   529
      Enabled         =   0   'False
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   -1  'True
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin EditLib.fpCurrency txtCustomerAmount 
      Height          =   555
      Left            =   12570
      TabIndex        =   37
      Top             =   8130
      Width           =   2565
      _Version        =   196608
      _ExtentX        =   4524
      _ExtentY        =   979
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin EditLib.fpCurrency txtChangeAmount 
      Height          =   555
      Left            =   12570
      TabIndex        =   39
      Top             =   8760
      Width           =   2565
      _Version        =   196608
      _ExtentX        =   4524
      _ExtentY        =   979
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin Point_of_Sale_System.lvButtons_H cmdDeliveryPayment 
      Height          =   585
      Left            =   12885
      TabIndex        =   41
      Tag             =   "0100"
      Top             =   6375
      Width           =   2370
      _ExtentX        =   4180
      _ExtentY        =   1032
      Caption         =   "Unlock for Payment"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPointOfSaleLittleItaly.frx":42317
      ImgSize         =   24
      cBack           =   -2147483633
   End
   Begin FPSpread.vaSpread vaSpread4 
      Height          =   2040
      Left            =   12885
      TabIndex        =   47
      Top             =   4230
      Width           =   2400
      _Version        =   196608
      _ExtentX        =   4233
      _ExtentY        =   3598
      _StockProps     =   64
      Enabled         =   0   'False
      DisplayRowHeaders=   0   'False
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   3
      MaxRows         =   10
      ScrollBars      =   2
      SpreadDesigner  =   "frmPointOfSaleLittleItaly.frx":47B09
      UserResize      =   1
   End
   Begin Point_of_Sale_System.lvButtons_H cmdBasket 
      Height          =   840
      Left            =   5250
      TabIndex        =   48
      Top             =   9510
      Width           =   2415
      _ExtentX        =   4260
      _ExtentY        =   1482
      Caption         =   "Basket"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPointOfSaleLittleItaly.frx":47EB1
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdFree 
      Height          =   585
      Index           =   8
      Left            =   13665
      TabIndex        =   50
      Tag             =   "0008"
      Top             =   1110
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Free"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin VB.Label lblDeliveredBy 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Delivered by:"
      Height          =   195
      Left            =   5160
      TabIndex        =   54
      Top             =   7815
      Width           =   1305
   End
   Begin VB.Label Label5 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Basket:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   15.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   360
      Left            =   12885
      TabIndex        =   49
      Top             =   3870
      Width           =   1260
   End
   Begin VB.Label lblMemberCode 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Label1"
      Height          =   195
      Left            =   6495
      TabIndex        =   44
      Top             =   11535
      Visible         =   0   'False
      Width           =   645
   End
   Begin VB.Label lblMemberName 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Label1"
      Height          =   195
      Left            =   6495
      TabIndex        =   43
      Top             =   11835
      Visible         =   0   'False
      Width           =   645
   End
   Begin VB.Label lblTransactionDate 
      BackStyle       =   0  'Transparent
      Caption         =   "Date:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   375
      Left            =   10635
      TabIndex        =   42
      Top             =   10005
      Width           =   2535
   End
   Begin VB.Label lblChangeAmount 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Change:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   480
      Left            =   10815
      TabIndex        =   40
      Top             =   8797
      Width           =   1575
   End
   Begin VB.Label lblCustomerAmount 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Customer:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   480
      Left            =   10365
      TabIndex        =   38
      Top             =   8167
      Width           =   2025
   End
   Begin VB.Label lblPaymentType 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Payment Type:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   15.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   360
      Left            =   60
      TabIndex        =   36
      Top             =   8535
      Width           =   2340
   End
   Begin VB.Label lblSalesTaxRate 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Sales Tax @16%:"
      Height          =   195
      Left            =   8790
      TabIndex        =   33
      Top             =   7800
      Width           =   1680
   End
   Begin VB.Label Label4 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Name:"
      Height          =   195
      Left            =   5145
      TabIndex        =   29
      Top             =   7440
      Width           =   615
   End
   Begin VB.Label lblLabel4 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Customers:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   15.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   360
      Left            =   2130
      TabIndex        =   25
      Top             =   495
      Width           =   1800
   End
   Begin VB.Label Label3 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Recieveable:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   480
      Left            =   12555
      TabIndex        =   22
      Top             =   7065
      Width           =   2700
   End
   Begin VB.Label Label2 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Discount:"
      Height          =   195
      Left            =   9570
      TabIndex        =   21
      Top             =   7428
      Width           =   900
   End
   Begin VB.Label lblDiscount 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Discount:"
      Height          =   195
      Left            =   5145
      TabIndex        =   19
      Top             =   7065
      Width           =   900
   End
   Begin VB.Label Label1 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Total Amount:"
      Height          =   195
      Left            =   9105
      TabIndex        =   17
      Top             =   7058
      Width           =   1365
   End
End
Attribute VB_Name = "frmPointOfSaleLittleItaly"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
Dim MemberCode As String
Dim CardNumber As String
Dim DiscountCode As String
Dim DiscountAmount As String
Dim IsPercentage As Boolean
Dim TransactionNo As String
Dim DockItPrints As Integer
Dim EmployeeCode As String

Private Sub cmbDiscount_Click()
        '<EhHeader>
        On Error GoTo cmbDiscount_Click_Err
        '</EhHeader>
    
100     DiscountCode = Format(cmbDiscount.ItemData(cmbDiscount.ListIndex), "0##")
    
102     sSQL = "Select * From DiscountSetup Where DiscountCode='" & DiscountCode & "'"
104     Set rs = New ADODB.Recordset
106     rs.Open sSQL, Conn, 1, 3
    
108     IsPercentage = rs("IsPercentage")
110     DiscountAmount = rs("DiscountAmount")
                
112     CalculateBill
        
        '<EhFooter>
        Exit Sub

cmbDiscount_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.cmbDiscount_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmbEmployees_Click()
        '<EhHeader>
        On Error GoTo cmbEmployees_Click_Err
        '</EhHeader>
    
100     EmployeeCode = Format(cmbEmployees.ItemData(cmbEmployees.ListIndex), "0###")
    
        '<EhFooter>
        Exit Sub

cmbEmployees_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.cmbEmployees_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmbPaymentType_Click()
        '<EhHeader>
        On Error GoTo cmbPaymentType_Click_Err
        '</EhHeader>
    
100     If cmbPaymentType.Text = "Cash" Then

102         DockItPrints = 1
        Else

104         DockItPrints = 3
        End If
  
106     If cmbPaymentType.Text = "Credit A/C" Then
108         cmbEmployees.Visible = True
        Else
110         cmbEmployees.Visible = False
        End If
        
        '<EhFooter>
        Exit Sub

cmbPaymentType_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.cmbPaymentType_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmbTransactionType_Click()
        '<EhHeader>
        On Error GoTo cmbTransactionType_Click_Err
        '</EhHeader>
    
100     txtCustomers.Enabled = cmbTransactionType.ListIndex
    
102     If cmbTransactionType.Text = "Delivery" Then
104         txtCustomers.Text = 1
106         cmdDelivery.Enabled = True
108         txtDeliveredBy.Visible = True
110         lblDeliveredBy.Visible = True
112         cmdPayments.Enabled = False
        Else
114         cmdDelivery.Enabled = False
116         txtDeliveredBy.Visible = False
118         lblDeliveredBy.Visible = False
120         cmdPayments.Enabled = True
        End If
    
        '<EhFooter>
        Exit Sub

cmbTransactionType_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.cmbTransactionType_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdClose_Click()
        '<EhHeader>
        On Error GoTo cmdClose_Click_Err
        '</EhHeader>
    
100     Unload Me
    
        '<EhFooter>
        Exit Sub

cmdClose_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.cmdClose_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdFree_Click(Index As Integer)
        '<EhHeader>
        On Error GoTo cmdFree_Click_Err
        '</EhHeader>
        
100     vaSpread2.Row = vaSpread2.ActiveRow
102     vaSpread2.Col = 4
104     vaSpread2.Text = 0
106     CalculateBill
        
        '<EhFooter>
        Exit Sub

cmdFree_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.cmdFree_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdDelivery_Click()
        '<EhHeader>
        On Error GoTo cmdDelivery_Click_Err
        '</EhHeader>
        
100     sSQL = "Delete From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
102     Conn.Execute sSQL

104     sSQL = "Select * From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
106     Debug.Print sSQL
108     Set rs = New ADODB.Recordset
110     rs.Open sSQL, Conn, 1, 3
        
112     For i = 1 To vaSpread2.DataRowCnt
114         rs.AddNew
116         rs("TransactionNo") = TransactionNo
118         rs("TransactionDate") = TransactionDate
120         rs("TransactionStartTime") = Time
122         rs("TransactionType") = cmbTransactionType.Text

124         If cmbPaymentType.Text = "Credit A/C" Then
126             rs("EmployeeCode") = EmployeeCode
128             rs("EmployeeName") = cmbEmployees.Text
            End If

130         rs("UserID") = UserID
132         vaSpread2.Row = i
134         vaSpread2.Col = 1
136         rs("ItemCode") = vaSpread2.Text
138         vaSpread2.Col = 2
140         rs("ItemName") = vaSpread2.Text
142         vaSpread2.Col = 9
144         rs("ItemPrice") = vaSpread2.Value
146         vaSpread2.Col = 4

148         If DiscountAmount <> 100 Then
150             rs("ItemQuantity") = vaSpread2.Value
            Else
152             rs("ItemQuantity") = 0
            End If

154         rs("DiscountCode") = DiscountCode
156         rs("DiscountAmount") = txtDiscount.Text
158         rs("TaxRate") = SalesTaxRate
160         rs("TaxAmount") = txtSalesTaxAmount.Text
162         rs("TransactionClose") = False
164         rs("TransactionsVoid") = False
166         rs("VoidDescription") = ""
168         rs("DiscountName") = txtDiscountName.Text & ""
170         rs("Deliveredby") = txtDeliveredBy.Text & ""
172         rs.Update
        Next
        
174     For i = 1 To DockItPrints
        
176         sSQL = "SELECT Transactions.*, ItemPrice * ItemQuantity AS ItemTotal From Transactions " & _
               "WHERE TransactionNo='" & TransactionNo & "' AND TransactionDate='" & TransactionDate & "'"
178         Set rs = New ADODB.Recordset
180         rs.Open sSQL, Conn, 1, 3
        
182         With repPrintDockitLittleItaly
184             .Restart
186             .lblCompanyAddress.Caption = CompanyAddress
188             .lblDeilveryNumbers.Caption = DeliveryNumbers
190             .lblDockitFooter.Caption = DockitFooter
192             .documentName = "Dockit Print"
194             .DataControl1.Recordset = rs
196             .PrintReport False
            End With
        
        Next
        
198     Call LoadDeliveries
        
200     POS_Lock True

        '<EhFooter>
        Exit Sub

cmdDelivery_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.cmdDelivery_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdStart_Click()
        '<EhHeader>
        On Error GoTo cmdStart_Click_Err
        '</EhHeader>
                   
100     POS_Lock False

102     sSQL = "Select Max(TransactionNo) as LastNumber  From Transactions Where TransactionDate='" & TransactionDate & "'"
104     Set rs = New ADODB.Recordset
106     rs.Open sSQL, Conn, 1, 3
        
108     If rs.EOF = True Then
110         TransactionNo = 1
        Else
112         TransactionNo = Val(rs("LastNumber") & "") + 1
        End If
        
114     TransactionNo = Format(TransactionNo, "0###")
        
116     IsPercentage = True
118     DiscountAmount = 0
        
120     MsgBox "Transaction No.: " & TransactionNo
        
122     cmdItems_Click 4
        
        '<EhFooter>
        Exit Sub

cmdStart_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.cmdStart_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdPayments_Click()
        '<EhHeader>
        On Error GoTo cmdPayments_Click_Err
        '</EhHeader>
                       
100     sSQL = "Delete From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
102     Conn.Execute sSQL

104     sSQL = "Select * From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
106     Debug.Print sSQL
108     Set rs = New ADODB.Recordset
110     rs.Open sSQL, Conn, 1, 3
        
112     For i = 1 To vaSpread2.DataRowCnt
114         rs.AddNew
116         rs("TransactionNo") = TransactionNo
118         rs("TransactionDate") = TransactionDate
120         rs("Customers") = txtCustomers.Text
122         rs("PaymentType") = cmbPaymentType.Text
124         rs("TransactionStartTime") = Time
126         rs("TransactionEndTime") = Time
128         rs("TransactionType") = cmbTransactionType.Text

130         If cmbPaymentType.Text = "Credit A/C" Then
132             rs("EmployeeCode") = EmployeeCode
134             rs("EmployeeName") = cmbEmployees.Text
            End If

136         rs("UserID") = UserID
138         vaSpread2.Row = i
140         vaSpread2.Col = 1
142         rs("ItemCode") = vaSpread2.Text
144         vaSpread2.Col = 2
146         rs("ItemName") = vaSpread2.Text
148         vaSpread2.Col = 9
150         rs("ItemPrice") = vaSpread2.Value
152         vaSpread2.Col = 4

154         If DiscountAmount <> 100 Then
156             rs("ItemQuantity") = vaSpread2.Value
            Else
158             rs("ItemQuantity") = 0
            End If

160         rs("DiscountCode") = DiscountCode
162         rs("DiscountAmount") = txtDiscount.Text
164         rs("TaxRate") = SalesTaxRate
166         rs("TaxAmount") = txtSalesTaxAmount.Text
168         rs("TransactionClose") = True
170         rs("TransactionsVoid") = False
172         rs("VoidDescription") = ""
174         rs("DiscountName") = txtDiscountName.Text & ""
176         rs("Deliveredby") = txtDeliveredBy.Text & ""
178         rs.Update
        Next
        
180     For i = 1 To DockItPrints
        
182         sSQL = "SELECT Transactions.*, ItemPrice * ItemQuantity AS ItemTotal From Transactions " & _
               "WHERE TransactionNo='" & TransactionNo & "' AND TransactionDate='" & TransactionDate & "'"
184         Set rs = New ADODB.Recordset
186         rs.Open sSQL, Conn, 1, 3
        
188         With repPrintDockitLittleItaly
190             .Restart
192             .lblCompanyAddress.Caption = CompanyAddress
194             .lblDeilveryNumbers.Caption = DeliveryNumbers
196             .lblDockitFooter.Caption = DockitFooter
198             .documentName = "Dockit Print"
200             .DataControl1.Recordset = rs
202             .PrintReport False
            End With

        Next
        
204     Printer.FontName = "FontControl"
206     Printer.Print "A"
208     Printer.EndDoc
        
210     MSComm1.Output = "Open Drawer"
212     MSComm1.Output = "Open Drawer"
214     MSComm1.Output = "Open Drawer"
        
216     POS_Lock True
218     cmdPayments.Enabled = True
        
        '<EhFooter>
        Exit Sub

cmdPayments_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.cmdPayments_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdDeliveryPayment_Click()
        '<EhHeader>
        On Error GoTo cmdDeliveryPayment_Click_Err
        '</EhHeader>
    
100     vaSpread3.Enabled = Not vaSpread3.Enabled
102     vaSpread4.Enabled = Not vaSpread4.Enabled

104     If vaSpread3.Enabled = True Or vaSpread4.Enabled = True Then
106         cmdDeliveryPayment.Caption = "Lock Payments"
        Else
108         cmdDeliveryPayment.Caption = "Unlock for Payments"
        End If
    
        '<EhFooter>
        Exit Sub

cmdDeliveryPayment_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.cmdDeliveryPayment_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Command1_Click()
        '<EhHeader>
        On Error GoTo Command1_Click_Err
        '</EhHeader>
100     MSComm1.Output = "Open Drawer"
102     MSComm1.Output = "Open Drawer"
104     MSComm1.Output = "Open Drawer"

        '<EhFooter>
        Exit Sub

Command1_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.Command1_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>
    
100     CenterForm Me
102     Me.WindowState = vbMaximized
                
104     FontControl = GetSetting(ApplicationName, "Drawer Settings", "FontName", "FontControl")
106     DrawerPort = GetSetting(ApplicationName, "Drawer Settings", "DrawerPort", "1")
        
108     TransactionDate = Format(TransactionDate, "dd/mmm/yyyy")
110     lblTransactionDate.Caption = "Date: " & TransactionDate
                
112     cmbTransactionType.ListIndex = 0
114     cmbPaymentType.ListIndex = 0
        
116     txtDiscountName.Text = ""
118     txtDeliveredBy.Text = ""
        
120     MSComm1.CommPort = DrawerPort
122     MSComm1.PortOpen = True
        
124     POS_Lock True
        
        vaSpread2.Row = -1
        vaSpread2.Col = 7
        vaSpread2.Formula = "C#*100/" & 100 + Val(SalesTaxRate)
        
126     Call FillcmbEmployees
128     Call FillcmbDiscount
130     Call LoadDeliveries
        
        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdItems_Click(Index As Integer)
        '<EhHeader>
        On Error GoTo cmdItems_Click_Err
        '</EhHeader>
    
100     sSQL = "Select * From ItemSetup Where CatagoryCode='" & Format(cmdItems(Index).Tag, "0###") & "' Order by ItemName"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
        
106     vaSpread1.MaxRows = 0
108     vaSpread1.MaxRows = 1
110     vaSpread1.Row = 1
        'vaSpread1.RowHeight(vaSpread1.Row) = 20

112     Do Until rs.EOF
114         vaSpread1.Col = 0
116         vaSpread1.Text = rs("ItemCode")
118         vaSpread1.Col = 1
120         vaSpread1.Text = rs("ItemName")
122         vaSpread1.Col = 2
124         vaSpread1.Text = rs("Price")
126         rs.MoveNext
128         vaSpread1.MaxRows = vaSpread1.MaxRows + 1
130         vaSpread1.Row = vaSpread1.Row + 1
            'vaSpread1.RowHeight(vaSpread1.Row) = 20
        Loop

132     vaSpread1.MaxRows = vaSpread1.DataRowCnt
        
        '<EhFooter>
        Exit Sub

cmdItems_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.cmdItems_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdBasket_Click()
        '<EhHeader>
        On Error GoTo cmdBasket_Click_Err
        '</EhHeader>

100     sSQL = "Delete From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
102     Conn.Execute sSQL

104     sSQL = "Select * From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
106     Debug.Print sSQL
108     Set rs = New ADODB.Recordset
110     rs.Open sSQL, Conn, 1, 3
        
112     For i = 1 To vaSpread2.DataRowCnt
114         rs.AddNew
116         rs("TransactionNo") = TransactionNo
118         rs("TransactionDate") = TransactionDate
120         rs("TransactionStartTime") = Time
122         rs("TransactionType") = cmbTransactionType.Text

124         If cmbPaymentType.Text = "Credit A/C" Then
126             rs("EmployeeCode") = EmployeeCode
128             rs("EmployeeName") = cmbEmployees.Text
            End If

130         rs("UserID") = UserID
132         vaSpread2.Row = i
134         vaSpread2.Col = 1
136         rs("ItemCode") = vaSpread2.Text
138         vaSpread2.Col = 2
140         rs("ItemName") = vaSpread2.Text
142         vaSpread2.Col = 9
144         rs("ItemPrice") = vaSpread2.Value
146         vaSpread2.Col = 4

148         If DiscountAmount <> 100 Then
150             rs("ItemQuantity") = vaSpread2.Value
            Else
152             rs("ItemQuantity") = 0
            End If

154         rs("DiscountCode") = DiscountCode
156         rs("DiscountAmount") = txtDiscount.Text
158         rs("TaxRate") = SalesTaxRate
160         rs("TaxAmount") = txtSalesTaxAmount.Text
162         rs("TransactionClose") = False
164         rs("TransactionsVoid") = False
166         rs("VoidDescription") = ""
168         rs("DiscountName") = txtDiscountName.Text & ""
170         rs("Deliveredby") = txtDeliveredBy.Text & ""
172         rs.Update
        Next

174     Call LoadDeliveries
        
176     POS_Lock True
        
        '<EhFooter>
        Exit Sub

cmdBasket_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.cmdBasket_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub MSComm1_OnComm()
        '<EhHeader>
        On Error GoTo MSComm1_OnComm_Err
        '</EhHeader>
        
        Exit Sub
        
        Do
100         CardNumber = CardNumber & MSComm1.Input

102         DoEvents
104     Loop Until InStr(CardNumber, Chr(13))

106     Debug.Print CardNumber
        
108     txtCardNumber.Text = Left(CardNumber, 14)
        
110     CardNumber = ""
    
        '<EhFooter>
        Exit Sub

MSComm1_OnComm_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.MSComm1_OnComm " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtCardNumber_Change()
        '<EhHeader>
        On Error GoTo txtCardNumber_Change_Err
        '</EhHeader>
    
100     Set rs = New ADODB.Recordset
102     sSQL = "Select * From MemberInformation Where CardNumber='" & txtCardNumber.Text & "'"
104     rs.Open sSQL, Conn, 1, 3
    
106     If rs.EOF = False Then
        
108         lblMemberCode.Caption = "Member Code: " & rs("MemberCode")
110         lblMemberName.Caption = "Member Name: " & rs("MemberName")
            
112         picEmployeePicture.DataField = "Picture"
114         Set picEmployeePicture.DataSource = rs
    
        Else
        
116         lblMemberCode.Caption = ""
118         lblMemberName.Caption = ""
120         picEmployeePicture.Picture = LoadPicture("")
        
        End If
    
        '<EhFooter>
        Exit Sub

txtCardNumber_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.txtCardNumber_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtCustomerAmount_Change()
        '<EhHeader>
        On Error GoTo txtCustomerAmount_Change_Err
        '</EhHeader>

100     txtChangeAmount.Value = txtCustomerAmount.Value - txtPayableAmount.Value
    
        '<EhFooter>
        Exit Sub

txtCustomerAmount_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.txtCustomerAmount_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtCustomerAmount_LostFocus()
        '<EhHeader>
        On Error GoTo txtCustomerAmount_LostFocus_Err
        '</EhHeader>

100     txtChangeAmount.Value = txtCustomerAmount.Value - txtPayableAmount.Value

        '<EhFooter>
        Exit Sub

txtCustomerAmount_LostFocus_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.txtCustomerAmount_LostFocus " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtPayableAmount_Change()
        '<EhHeader>
        On Error GoTo txtPayableAmount_Change_Err
        '</EhHeader>
    
100     Call txtCustomerAmount_Change
    
        '<EhFooter>
        Exit Sub

txtPayableAmount_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.txtPayableAmount_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread1_ButtonClicked(ByVal Col As Long, _
   ByVal Row As Long, _
   ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread1_ButtonClicked_Err
        '</EhHeader>
        
100     vaSpread1.Row = vaSpread1.ActiveRow
102     vaSpread2.Row = vaSpread2.ActiveRow
104     vaSpread1.Col = 0
106     vaSpread2.Col = 1
108     vaSpread2.Text = vaSpread1.Text
110     vaSpread1.Col = 1
112     vaSpread2.Col = 2
114     vaSpread2.Text = vaSpread1.Text
116     vaSpread1.Col = 2
118     vaSpread2.Col = 3
120     vaSpread2.Text = vaSpread1.Text
122     vaSpread2.Col = 4
124     vaSpread2.Text = 1
126     vaSpread2.Row = vaSpread2.Row + 1
128     vaSpread2.Col = 1
130     vaSpread2.Action = ActionActiveCell
    
132     Call CalculateBill
    
        '<EhFooter>
        Exit Sub

vaSpread1_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.vaSpread1_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread2_ButtonClicked(ByVal Col As Long, _
   ByVal Row As Long, _
   ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread2_ButtonClicked_Err
        '</EhHeader>
    
100     vaSpread2.Row = vaSpread2.ActiveRow
102     vaSpread2.Action = ActionDeleteRow
        
        '<EhFooter>
        Exit Sub

vaSpread2_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.vaSpread2_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub CalculateBill()
        '<EhHeader>
        On Error GoTo CalculateBill_Err
        '</EhHeader>
    
        Dim TotalAmount As String
        Dim TotalTaxAmount As String

100     For i = 1 To vaSpread2.MaxRows
102         vaSpread2.Row = i
104         vaSpread2.Col = 8
106         TotalTaxAmount = Val(TotalTaxAmount) + Val(vaSpread2.Value & "")
108         vaSpread2.Col = 9
110         TotalAmount = Val(TotalAmount) + Val(vaSpread2.Value & "")
        Next

112     txtTotalAmount.Text = TotalAmount
         
114     If IsPercentage = True Then
116         txtDiscount.Text = Round((txtTotalAmount.Text * DiscountAmount) / 100, 0)
        Else
118         txtDiscount.Text = DiscountAmount
        End If

120     If DiscountAmount = "100" Then
122         txtSalesTaxAmount.Text = 0
124         txtDiscount.Text = txtTotalAmount.Text
        Else
126         txtSalesTaxAmount.Text = TotalTaxAmount
        End If

128     txtPayableAmount.Text = Val(txtTotalAmount.Value) - Val(txtDiscount.Value) + Val(txtSalesTaxAmount.Value)
    
        '<EhFooter>
        Exit Sub

CalculateBill_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.CalculateBill " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub FillcmbDiscount()
        '<EhHeader>
        On Error GoTo FillcmbDiscount_Err
        '</EhHeader>
    
100     sSQL = "Select * From DiscountSetup"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     cmbDiscount.Clear

108     Do Until rs.EOF
110         cmbDiscount.AddItem rs("DiscountDescription")
112         cmbDiscount.ItemData(cmbDiscount.NewIndex) = rs("DiscountCode")
114         rs.MoveNext
        Loop
    
116     cmbDiscount.Text = "No Discount"

        '<EhFooter>
        Exit Sub

FillcmbDiscount_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.FillcmbDiscount " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub POS_Lock(strStatus As Boolean)
        '<EhHeader>
        On Error GoTo POS_Lock_Err
        '</EhHeader>

100     txtDeliveredBy.Text = ""
102     txtDiscountName.Text = ""

104     strStatus = Not strStatus
        
106     cmdStart.Enabled = Not strStatus
108     cmbTransactionType.Enabled = strStatus
110     txtCustomers.Enabled = strStatus
        
112     cmdItems(0).Enabled = strStatus
114     cmdItems(1).Enabled = strStatus
116     cmdItems(2).Enabled = strStatus
118     cmdItems(3).Enabled = strStatus
120     cmdItems(4).Enabled = strStatus
122     cmdItems(5).Enabled = strStatus
124     cmdItems(6).Enabled = strStatus
126     cmdItems(7).Enabled = strStatus
        
128     vaSpread1.Enabled = strStatus
130     vaSpread2.Enabled = strStatus

132     If strStatus = True Then

134         vaSpread1.MaxRows = 0
136         vaSpread1.MaxRows = 25

138         vaSpread2.MaxRows = 0
140         vaSpread2.MaxRows = 20
        
        End If
        
142     cmdPayments.Enabled = strStatus
144     cmbDiscount.Enabled = strStatus
    
        '<EhFooter>
        Exit Sub

POS_Lock_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.POS_Lock " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread2_LeaveCell(ByVal Col As Long, _
   ByVal Row As Long, _
   ByVal NewCol As Long, _
   ByVal NewRow As Long, _
   Cancel As Boolean)
        '<EhHeader>
        On Error GoTo vaSpread2_LeaveCell_Err
        '</EhHeader>
        
100     CalculateBill

        '<EhFooter>
        Exit Sub

vaSpread2_LeaveCell_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.vaSpread2_LeaveCell " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub LoadDeliveries()
        '<EhHeader>
        On Error GoTo LoadDeliveries_Err
        '</EhHeader>
           
100     sSQL = "SELECT DISTINCT TransactionNo, SUM(ItemPrice * ItemQuantity) AS ItemAmount, TaxAmount From Transactions " & _
           "WHERE TransactionDate='" & TransactionDate & "' AND TransactionType='Delivery' AND TransactionClose = 0 GROUP BY TransactionNo, TaxAmount"
        
102     Debug.Print sSQL
104     Set rs = New ADODB.Recordset
106     rs.Open sSQL, Conn, 1, 3
        
108     vaSpread3.MaxRows = 0
110     vaSpread3.MaxRows = 1
112     vaSpread3.Row = 1

114     Do Until rs.EOF
116         vaSpread3.Col = 1
118         vaSpread3.Text = rs("TransactionNo")
120         vaSpread3.Col = 2
122         vaSpread3.Text = Val(rs("ItemAmount")) + Val(rs("TaxAmount"))
124         vaSpread3.MaxRows = vaSpread3.MaxRows + 1
126         vaSpread3.Row = vaSpread3.Row + 1
128         rs.MoveNext
        Loop

130     vaSpread3.MaxRows = vaSpread3.DataRowCnt

132     sSQL = "SELECT DISTINCT TransactionNo, SUM(ItemPrice * ItemQuantity) AS ItemAmount, TaxAmount From Transactions " & _
           "WHERE TransactionDate='" & TransactionDate & "' AND TransactionType='Dine In' AND TransactionClose = 0 GROUP BY TransactionNo, TaxAmount"
        
134     Debug.Print sSQL
136     Set rs = New ADODB.Recordset
138     rs.Open sSQL, Conn, 1, 3
        
140     vaSpread4.MaxRows = 0
142     vaSpread4.MaxRows = 1
144     vaSpread4.Row = 1

146     Do Until rs.EOF
148         vaSpread4.Col = 1
150         vaSpread4.Text = rs("TransactionNo")
152         vaSpread4.Col = 2
154         vaSpread4.Text = Val(rs("ItemAmount")) + Val(rs("TaxAmount"))
156         vaSpread4.MaxRows = vaSpread4.MaxRows + 1
158         vaSpread4.Row = vaSpread4.Row + 1
160         rs.MoveNext
        Loop

162     vaSpread4.MaxRows = vaSpread4.DataRowCnt

        '<EhFooter>
        Exit Sub

LoadDeliveries_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.LoadDeliveries " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread3_ButtonClicked(ByVal Col As Long, _
   ByVal Row As Long, _
   ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread3_ButtonClicked_Err
        '</EhHeader>
        
        Dim TransactionEnd As String

100     vaSpread3.Row = vaSpread3.ActiveRow
102     vaSpread3.Col = 1
104     TransactionNo = vaSpread3.Text
106     TransactionEnd = Time
        
108     Printer.FontName = "FontControl"
110     Printer.Print "A"
112     Printer.EndDoc
        
114     MSComm1.Output = "Open Drawer"
116     MSComm1.Output = "Open Drawer"
118     MSComm1.Output = "Open Drawer"
        
120     sSQL = "UPDATE Transactions SET TransactionEndTime='" & TransactionEnd & "', TransactionClose=1 WHERE (TransactionNo='" & TransactionNo & "') AND (TransactionDate='" & TransactionDate & "')"
122     Conn.Execute sSQL
        
124     LoadDeliveries

        '<EhFooter>
        Exit Sub

vaSpread3_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.vaSpread3_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread4_ButtonClicked(ByVal Col As Long, _
   ByVal Row As Long, _
   ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread4_ButtonClicked_Err
        '</EhHeader>
        
        Dim TransactionEnd As String

100     vaSpread4.Row = vaSpread4.ActiveRow
102     vaSpread4.Col = 1
104     TransactionNo = vaSpread4.Text
106     TransactionEnd = Time
        
108     For i = 1 To DockItPrints
        
110         sSQL = "SELECT Transactions.*, ItemPrice * ItemQuantity AS ItemTotal From Transactions " & _
               "WHERE TransactionNo='" & TransactionNo & "' AND TransactionDate='" & TransactionDate & "'"
112         Set rs = New ADODB.Recordset
114         rs.Open sSQL, Conn, 1, 3

116         With repPrintDockitLittleItaly
118             .Restart
120             .lblCompanyAddress.Caption = CompanyAddress
122             .lblDeilveryNumbers.Caption = DeliveryNumbers
124             .lblDockitFooter.Caption = DockitFooter
126             .documentName = "Dockit Print"
128             .DataControl1.Recordset = rs
130             .PrintReport False
            End With

        Next
        
132     Printer.FontName = "FontControl"
134     Printer.Print "A"
136     Printer.EndDoc

138     MSComm1.Output = "Open Drawer"
140     MSComm1.Output = "Open Drawer"
142     MSComm1.Output = "Open Drawer"
        
144     sSQL = "UPDATE Transactions SET TransactionEndTime='" & TransactionEnd & "', TransactionClose=1 WHERE (TransactionNo='" & TransactionNo & "') AND (TransactionDate='" & TransactionDate & "')"
146     Conn.Execute sSQL
        
148     LoadDeliveries

        '<EhFooter>
        Exit Sub

vaSpread4_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.vaSpread4_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub FillcmbEmployees()
        '<EhHeader>
        On Error GoTo FillcmbEmployees_Err
        '</EhHeader>

100     sSQL = "SELECT EmployeeCode, EmployeeName From EmployeeInformation"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     cmbEmployees.Clear

108     Do Until rs.EOF
110         cmbEmployees.AddItem rs("EmployeeName")
112         cmbEmployees.ItemData(cmbEmployees.NewIndex) = rs("EmployeeCode")
114         rs.MoveNext
        Loop

116     cmbEmployees.ListIndex = 0
    
        '<EhFooter>
        Exit Sub

FillcmbEmployees_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSaleLittleItaly.FillcmbEmployees " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

