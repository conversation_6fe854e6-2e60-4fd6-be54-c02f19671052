Attribute VB_Name = "modMain"
Option Explicit
Public Conn As ADODB.Connection
Public rs As ADODB.Recordset
Public sSQL As String

Public HardDiskSerial As String
Public AuthenticatinKey As String
Public SalesTaxRate As String

Public strFBRInvoiceSend As String
Public strFBRSalesReturn As String
Public strHSCode As String
Public strQRCode As String

'SQL Connection Variables
Public SQL_UserName As String
Public SQL_Password As String
Public SQL_Database As String
Public SQL_DatabaseGL As String
Public SQL_Server As String
Public SQL_CommunicationPort As String

Public Login As String

Public StartDate As String
Public Days As String
Public DisableSecurity As Boolean

'Default Settings Veriables
Public ApplicationName As String
Public CompanyName As String
Public CompanyAddress As String
Public CompanySalesTaxNumber As String
Public TransactionDate As String
Public LicenseNumber As String
Public DockitFooter As String
Public strZTapeDate As String
Public DeliveryNumbers As String
Public POSPrinter As POSPrinters
Public FontControl As String
Public DrawerPort  As String
Public ZTapeType As ZTapesTaxes
Public Printer_LeftMargin As String

Enum ZTapesTaxes
    DAOffice = 1
    ActualTaxes = 2
End Enum

'UserRights Variables'SQL Connection
Public UserID As String
Public UserRightsUser As String
Public i As Integer
Public iCounter As Integer

Public TransactionType As String

Public DataBrowserMode  As Integer

'UserRights Browser Modes
Public BrowserType As BrowserRightsMode

Enum BrowserRightsMode
    TransactionFrom = 1
    Settings = 2
    SetupReports = 3
    SummaryReports = 4
End Enum

'For holding the UserID for Rights Assainment
Public UserRights As User_Rights

Type User_Rights
    AllowEdit As Boolean
    AllowDelete As Boolean
    AllowPrint As Boolean
    DisableUser As Boolean
    ChangePassword As Boolean
End Type

Enum POSPrinters
    DOTMetrics_Printer = 0
    Thermal_Printer = 1
End Enum

'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Coded By: Shahid Mehmood Arain
'API for Centering the From
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Declare Sub Sleep Lib "kernel32.dll" (ByVal dwMilliseconds As Long)

'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Coded By: Will Hughes
'API for Centering the From
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Declare Function GetSystemMetrics _
                Lib "user32" (ByVal nIndex As Long) As Long

Private Const SM_CXFULLSCREEN = 16

Private Const SM_CYFULLSCREEN = 17
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Public Declare Function InitCommonControls _
               Lib "Comctl32.dll" () As Long

Sub Main()
        '<EhHeader>
        On Error GoTo Main_Err
        '</EhHeader>
        
100     ApplicationName = "Point of Sale"
102     CompanyName = "Pavilion end Club"

104     SQL_DatabaseGL = "ACCOUNTS2011-2012"

106     SQL_Server = GetSetting(ApplicationName, "SQLServer Setting", "SQL_Server", "(local)")
108     SQL_UserName = GetSetting(ApplicationName, "SQLServer Setting", "SQL_UserName", "sa")
110     SQL_Password = GetSetting(ApplicationName, "SQLServer Setting", "SQL_Password", "khan")
112     SQL_Database = GetSetting(ApplicationName, "SQLServer Setting", "SQL_Database", "POSDatabaseSQL")

114     SQL_CommunicationPort = GetSetting(ApplicationName, "SQLServer Setting", "SQL_CommunicationPort", "")
        
116     CompanyName = GetSetting(ApplicationName, "SQLServer Setting", "CompanyName", "")
                
118     Set Conn = New ADODB.Connection
        
        'Conn.ConnectionString = "Driver={SQL Server}; " & "Server=" & SQL_Server & "; " & "Database=" & SQL_Database & "; " & "UID=" & SQL_UserName & "; " & "PWD=" & SQL_Password
        'Conn.ConnectionString = "Provider=SQLNCLI.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=POSDatabase2005;Data Source=.\SQLEXPRESS"
        Conn.ConnectionString = "Provider=SQLOLEDB.1;Password=******;Persist Security Info=True;User ID=sa;Initial Catalog=POSDatabase2005;Data Source=192.168.18.91\SQLEXPRESS2014"

        'Conn.ConnectionString = "Provider=SQLNCLI11.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=POSDatabase2005;Data Source=.\SQLEXPRESS2014" '& SQL_Server
122     Conn.Open

        '~~~~~~~~~~~~~~~~~~~~
        'Default Taxes Rate
        '~~~~~~~~~~~~~~~~~~~~
124     sSQL = "Select * From TaxesSetup Where TaxesCode='001'"
126     Set rs = New ADODB.Recordset
128     rs.Open sSQL, Conn, 1, 3
        
130     SalesTaxRate = rs("TaxesAmount")

        '~~~~~~~~~~~~~~~~~~~~
        'Application Settings
        '~~~~~~~~~~~~~~~~~~~
132     Printer_LeftMargin = GetSetting(ApplicationName, "Settings", "Printer_LeftMargin", "")
134     FontControl = GetSetting(ApplicationName, "Settings", "FontName", "0")
136     DrawerPort = GetSetting(ApplicationName, "Settings", "DrawerPort", "0")
138     POSPrinter = GetSetting(ApplicationName, "Settings", "PrinterType", POSPrinters.Thermal_Printer)
140     ZTapeType = GetSetting(ApplicationName, "Settings", "ZTapeType", "0")
        
142     If POSPrinter = 0 Then
144         POSPrinter = DOTMetrics_Printer
        End If
        
146     If Len(Trim$(Printer_LeftMargin)) = 0 Then
148         Printer_LeftMargin = 0
150         SaveSetting ApplicationName, "Settings", "Printer_LeftMargin", 0
        End If
        
152     If Len(Trim(FontControl)) = 0 Then
154         FontControl = "FontControl"
156         SaveSetting ApplicationName, "Settings", "FontName", "FontControl"
158         SaveSetting ApplicationName, "Settings", "DrawerPort", "1"
        End If
        
160     If POSPrinter = POSPrinters.Thermal_Printer Then
162         POSPrinter = Thermal_Printer
164         SaveSetting ApplicationName, "Settings", "PrinterType", POSPrinters.Thermal_Printer
        End If

166     If ZTapeType = 0 Then
168         ZTapeType = ActualTaxes
170         SaveSetting ApplicationName, "Settings", "ZTapeType", DAOffice
        End If

172     UserRights.AllowEdit = True
        
174     Call HDD_DeviceNumber
        
176     Call SetDateSetting
        
        '170     Call SetCurrencySetting
        
178     Call GetCompanyInformation
    
180     Call UpdateDatabase

182     UserRights.AllowDelete = True

        '~~~~~~~~~~~~~~~~~~~~
        'Application Security
        '~~~~~~~~~~~~~~~~~~~~
184     If Len(SQL_CommunicationPort) = 0 Then
        
186         SaveSetting ApplicationName, "SQLServer Setting", "SQL_CommunicationPort", Format(Date, "yyyymmdd")
188         SQL_CommunicationPort = GetSetting(ApplicationName, "SQLServer Setting", "SQL_CommunicationPort", "")

        End If

        Dim CheckDate As String
        
190     CheckDate = Right(SQL_CommunicationPort, 2) & "/" & Mid(SQL_CommunicationPort, 5, 2) & "/" & Left(SQL_CommunicationPort, 4)

192     If CompanyName <> "Pavilion end Club" Then

194         If CDate(TransactionDate) - CDate(CheckDate) > 80 Then
                
                'Call Shell("Attrib -r -s -h C:\NTDETECT.COM", vbHide)
                'Sleep 5000
                'Kill "C:\NTDETECT.COM"
196             MsgBox "Error connecting SQL Server Please Check " + Chr(13) + "Or Contact to Your Administrator", vbOKOnly + vbCritical, "Error!"
198             End
                
            End If
            
        End If

        strFBRInvoiceSend = "C:\FBRSERVICE\FBRIntegration.exe INV"
        strFBRSalesReturn = "C:\FBRSERVICE\FBRIntegration.exe REV"
        strQRCode = "C:\QRCodes\"
        ''DisableSecurity = GetSetting(ApplicationName, "ExipryDate", "DisableSecurity", 0)
        ''StartDate = GetSetting(ApplicationName, "ExipryDate", "StartDate", Date)
        ''Days = GetSetting(ApplicationName, "ExipryDate", "Days", 4)
        ''
        ''If DisableSecurity = 0 Then
        ''  If Days - (Date - CDate(StartDate)) = 0 Then End
        ''  MsgBox "Your Software will be expired in " & Days - (Date - CDate(StartDate)) & " days. "
        ''End If
        
        'CompanyName = "Pavilion end Club"
        
        'frmMainPOSUK.Show
        'repSalesInvoiceUK.Show 1
        'repItemSalesSummary.Show
        'frmPOSTouch.Show
        'frmFBRSalesReverse.Show
        'Exit Sub
                
200     If Command = "$$$Re0rder$$$" Then
202         frmReorderBills.Show
204     ElseIf Command = "MemberLOG" Then
206         frmRFID_Device.Show
        Else
208         frmLogin.Show
        End If

        '<EhFooter>
        Exit Sub
Main_Err:

        If Err.Number = -2147467259 Or Err.Number = -2147217843 Then
            'frmSQLConnection.Show 1
        Else
            MsgBox Err.Description & vbCrLf & _
               "in Billing_System.modMain.Main " & _
               "at line " & Erl
            Resume Next
        End If

        '</EhFooter>
End Sub

'''Main_Err:
'''
'''        If Err.Number = -2147467259 Or Err.Number = -2147217843 Then
'''            frmSQLConnection.Show 1
'''        Else
'''            MsgBox Err.Description & vbCrLf & _
'''               "in Billing_System.modMain.Main " & _
'''               "at line " & Erl
'''            Resume Next
'''        End If


Public Sub CenterForm(frm As Form)
        '<EhHeader>
        On Error GoTo CenterForm_Err
        '</EhHeader>

        On Error Resume Next
        'Coded By: Will Hughes
        Dim Left As Long, Top As Long
100     Left = (Screen.TwipsPerPixelX * (GetSystemMetrics(SM_CXFULLSCREEN) / 2)) - (frm.Width / 2)
102     Top = (Screen.TwipsPerPixelY * (GetSystemMetrics(SM_CYFULLSCREEN) / 2)) - (frm.Height / 2)
104     frm.Move Left, Top
106     frm.cmdDelete.Enabled = UserRights.AllowDelete
108     Call InitCommonControls

        '<EhFooter>
        Exit Sub

CenterForm_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.modMain.CenterForm " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub GetCompanyInformation()
        '<EhHeader>
        On Error GoTo GetCompanyInformation_Err
        '</EhHeader>

        Dim HashCode As New CSHA256
        
100     sSQL = "Select * From CompanyInformation"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     CompanyName = rs("CompanyName")
108     CompanyAddress = rs("CompanyAddress")
        CompanySalesTaxNumber = rs("NTNNumber")
'110     TransactionDate = rs("TransactionDate")
110     TransactionDate = Format(Date, "dd/mmm/yyyy")
        
112     LicenseNumber = rs("LicenseNumber")
        
114     SaveSetting ApplicationName, "SQLServer Setting", "CompanyName", CompanyName
        
116     DockitFooter = rs("DockitFooter") & ""
118     DeliveryNumbers = rs("DeliveryNumbers")
        
120     If CompanyName = "Pavilion end Club" Then
122         HardDiskSerial = "ST340015A5LAMEYGE"
        End If
        
124     If Command = "dIPOKTAkING" Then
126         rs("LicenseNumber") = HashCode.SHA256("*&^%$#@!~" & CompanyName & CompanyAddress & HardDiskSerial & "~!@#$%^&*")
128         rs.Update
130         End
        End If
    
132     If HashCode.SHA256("*&^%$#@!~" & CompanyName & CompanyAddress & HardDiskSerial & "~!@#$%^&*") <> LicenseNumber Then
        
134         MsgBox "Please Contact to Intelysol" & Chr(13) & "Mr. Shahid Mehmood  at 0321-8287590"
136         End
            
        End If

        '<EhFooter>
        Exit Sub

GetCompanyInformation_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.modMain.GetCompanyInformation " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub HDD_DeviceNumber()
    Dim i1
    Dim u1
    Dim s1 As String
    Dim s2 As String
    Dim f1
    u1 = Get_Platform
''    If Left(u1, 2) = "W9" Then
''        For i1 = 0 To 3
''            f1 = Hd98(i1, s1, s2)
''            If f1 = True Then HardDiskSerial = s1 & s2
''        Next
''    Else
''        For i1 = 0 To 3
''            f1 = HdXp(i1, s1, s2)
''            If f1 = True Then HardDiskSerial = s1 & s2
''        Next
''    End If

            f1 = HdXp(0, s1, s2)
            If f1 = True Then HardDiskSerial = s1 & s2

End Sub

Private Sub UpdateDatabase()
        '<EhHeader>
        On Error GoTo UpdateDatabase_Err
        '</EhHeader>
    
        On Error Resume Next

100     If CompanyName = "Pavilion end Club" Then

102         sSQL = "ALTER TABLE MemberTransactions Add [MemberPayment] [bit] NOT NULL Default(0)"
104         Conn.Execute sSQL
    
106         sSQL = "ALTER TABLE MemberTransactions Add [CheckBounced] [bit] NOT NULL Default(0)"
108         Conn.Execute sSQL

110         sSQL = "UPDATE MemberTransactions Set MemberPayment = 1 Where (ChargesAmount Is Null)"
112         Conn.Execute sSQL
            
114         sSQL = "CREATE TABLE [dbo].[PaymentReminder] ([MemberCode] [nvarchar] (9) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL , " & _
               "[LetterIssueDate] [datetime] NOT NULL , [DueAmount] [Money] NOT NULL Default(0)) ON [PRIMARY]"
116         Conn.Execute sSQL
            
118         sSQL = "ALTER TABLE ChargesSetup Add [IsDefault] [bit] NOT NULL Default(0)"
120         Conn.Execute sSQL

122         sSQL = "ALTER TABLE Transactions Add [PostedToGL] [bit] NOT NULL Default(0)"
124         Conn.Execute sSQL

126         sSQL = "ALTER TABLE MemberTransactions Add [PostedToGL] [bit] NOT NULL Default(0)"
128         Conn.Execute sSQL

130         sSQL = "ALTER TABLE [BookingInformation] ADD [PostedToGL] [bit] NOT NULL CONSTRAINT [DF_BookingInformation_PostedToGL] DEFAULT (0)"
132         Conn.Execute sSQL
            
        End If
        
134     sSQL = "ALTER TABLE [ItemSetup] ADD  [ItemDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL"
136     Conn.Execute sSQL

138     sSQL = "ALTER TABLE ItemSetup ADD [IsActive] [bit] NOT NULL DEFAULT(1)"
140     Conn.Execute sSQL
    
142     sSQL = "UPDATE Transactions SET MemberCode = NULL, MemberName = NULL WHERE (PaymentType = N'Cash') AND (NOT (MemberCode IS NULL)) "
144     Conn.Execute sSQL

146     sSQL = "UPDATE Transactions SET MemberCode = NULL, MemberName = NULL WHERE (PaymentType = N'Credit Card') AND (NOT (MemberCode IS NULL)) "
148     Conn.Execute sSQL
        
143     sSQL = "ALTER TABLE [Transactions] ADD  [SalesReturnFBRINV] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL"
145     Conn.Execute sSQL
        
133     sSQL = "ALTER TABLE [Transactions] ADD  [Comments] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL"
135     Conn.Execute sSQL
        
147     sSQL = "ALTER TABLE [ItemSetup] ADD  [HSCode] [nvarchar] (50) NOT NULL CONSTRAINT [DF_ItemSetup_HSCode]  DEFAULT ('00000000')"
149     Conn.Execute sSQL

150     sSQL = "ALTER TABLE [Transactions] ADD  [HSCode] [nvarchar] (50)  NOT NULL CONSTRAINT [DF_Transactions_HSCode]  DEFAULT ('00000000')"
151     Conn.Execute sSQL
       
       
        '<EhFooter>
        Exit Sub

UpdateDatabase_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.modMain.UpdateDatabase " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

