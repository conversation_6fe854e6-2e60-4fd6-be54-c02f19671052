VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} repZTapeReport 
   Caption         =   "repZTapeReport (ActiveReport)"
   ClientHeight    =   11670
   ClientLeft      =   0
   ClientTop       =   285
   ClientWidth     =   19200
   _ExtentX        =   33867
   _ExtentY        =   20585
   SectionData     =   "repZTapeReport.dsx":0000
End
Attribute VB_Name = "repZTapeReport"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
Dim NetSalesTotal As String
Dim strZSalesTaxRate As String

Private Sub ActiveReport_ReportStart()
        '<EhHeader>
        On Error GoTo ActiveReport_ReportStart_Err
        '</EhHeader>
        
        On Error Resume Next
100     ZTapeType = DAOffice
        
102     sSQL = "SELECT MAX(TaxRate) AS SalesTaxRate From Transactions WHERE (TransactionDate='" & strZTapeDate & "')"
104     Set rs = New ADODB.Recordset
106     rs.Open sSQL, Conn, 1, 3
        
108     strZSalesTaxRate = rs("SalesTaxRate")
        
        Dim rsDailySummary As ADODB.Recordset
            
        'For Net Sales Total
110     If ZTapeType = ActualTaxes Then

112         sSQL = "SELECT SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions WHERE TransactionDate='" & strZTapeDate & "'"
114         Set rs = New ADODB.Recordset
116         rs.Open sSQL, Conn, 1, 3
            
118         txtNetSalesTotal.Text = Val(rs("TotalAmount") & "")
        Else

120         sSQL = "SELECT  TransactionNo, TransactionType, SUM(ItemPrice * ItemQuantity) AS TotalAmount, SUM(DISTINCT TaxAmount) AS TaxAmount From Transactions " & _
               "WHERE (TransactionDate='" & strZTapeDate & "') GROUP BY TransactionNo, TransactionType"

122         Debug.Print sSQL
124         Set rs = New ADODB.Recordset
126         rs.Open sSQL, Conn, 1, 3

128         Do Until rs.EOF
130             txtNetSalesTotal.Text = Val(txtNetSalesTotal.Text) + Val(rs("TotalAmount")) + Val(rs("TaxAmount"))
132             rs.MoveNext
            
            Loop

134         txtNetSalesTotal.Text = Format(txtNetSalesTotal.Text * 100 / (Val(strZSalesTaxRate) + 100), "##.##")
        End If
 
136     NetSalesTotal = txtNetSalesTotal.Text
        
        'For DineIn Count
138     sSQL = "SELECT  TransactionType, COUNT(DISTINCT TransactionNo) AS TransactionType From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionType HAVING TransactionType='Dine In' " & _
           "ORDER BY TransactionType"

        Debug.Print sSQL
140     Set rs = New ADODB.Recordset
142     rs.Open sSQL, Conn, 1, 3
    
144     If rs.EOF = False Then
146         txtDineInCount.Text = rs("TransactionType")
        End If
    
        'For Deliveries Count
148     sSQL = "SELECT TransactionType, COUNT(DISTINCT TransactionNo) AS TransactionType From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionType HAVING TransactionType='Delivery' " & _
           "ORDER BY TransactionType"

150     Set rs = New ADODB.Recordset
152     rs.Open sSQL, Conn, 1, 3

154     If rs.EOF = False Then
156         txtDeliveriesCount.Text = rs("TransactionType")
        End If

158     If ZTapeType = ActualTaxes Then
            'For DineIn Total Amount
160         sSQL = "SELECT TransactionType, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
               "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionType HAVING TransactionType='Dine In' " & _
               "ORDER BY TransactionType"
            
162         Debug.Print sSQL
164         Set rs = New ADODB.Recordset
166         rs.Open sSQL, Conn, 1, 3
            
168         Do Until rs.EOF
170             txtDineIn.Text = Val(txtDineIn.Text) + Val(rs("TotalAmount"))
172             rs.MoveNext
            Loop
        
        Else
        
174         sSQL = "SELECT TransactionNo, TransactionType, SUM(ItemPrice * ItemQuantity) AS TotalAmount, SUM(DISTINCT TaxAmount) AS TaxAmount " & _
               "From Transactions WHERE (TransactionDate='" & strZTapeDate & "') GROUP BY TransactionType, TransactionNo HAVING (TransactionType = 'Dine In') ORDER BY TransactionType "
        
176         Debug.Print sSQL
178         Set rs = New ADODB.Recordset
180         rs.Open sSQL, Conn, 1, 3

182         Do Until rs.EOF
184             txtDineIn.Text = Val(txtDineIn.Text) + Val(rs("TotalAmount")) + Val(rs("TaxAmount"))
186             rs.MoveNext
            Loop
        
188         txtDineIn.Text = Format(txtDineIn.Text * 100 / (Val(strZSalesTaxRate) + 100), "##.##")
        End If
        
        'For Deliveries Total Amount
190     If ZTapeType = ActualTaxes Then
192         sSQL = "SELECT TransactionType, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
               "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionType HAVING TransactionType='Delivery' " & _
               "ORDER BY TransactionType"
               
194         Set rs = New ADODB.Recordset
196         rs.Open sSQL, Conn, 1, 3

198         Do Until rs.EOF

200             txtDelivery.Text = Val(txtDelivery.Text) + Val(rs("TotalAmount")) + Val(rs("TaxAmount"))
202             rs.MoveNext
    
            Loop
        
        Else
        
204         sSQL = "SELECT TransactionNo, TransactionType, SUM(ItemPrice * ItemQuantity) AS TotalAmount, SUM(DISTINCT TaxAmount) AS TaxAmount " & _
               "From Transactions WHERE (TransactionDate='" & strZTapeDate & "') GROUP BY TransactionType, TransactionNo HAVING (TransactionType = 'Delivery') ORDER BY TransactionType "
    
206         Set rs = New ADODB.Recordset
208         rs.Open sSQL, Conn, 1, 3

210         Do Until rs.EOF

212             txtDelivery.Text = Val(txtDelivery.Text) + Val(rs("TotalAmount")) + Val(rs("TaxAmount"))
214             rs.MoveNext
    
            Loop
        
216         txtDelivery.Text = Format(txtDelivery.Text * 100 / (Val(strZSalesTaxRate) + 100), "##.##")
        End If
        
        'For TaxCollected
218     sSQL = "SELECT TransactionNo, SUM(TaxAmount) AS TaxAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionNo"
    
220     Set rs = New ADODB.Recordset
222     rs.Open sSQL, Conn, 1, 3

224     Do Until rs.EOF
226         txtTaxCollected.Text = Val(txtTaxCollected.Text) + Val(rs("TaxAmount"))
228         rs.MoveNext
        Loop
        
230     txtTaxCollected.Text = Format((NetSalesTotal * Val(strZSalesTaxRate) / 100), "##.##")
            
        'For Discounts
232     If ZTapeType = ActualTaxes Then

234         sSQL = "SELECT Distinct TransactionNo, DiscountAmount From Transactions " & _
               "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionNo"
           
236         Debug.Print sSQL
238         Set rs = New ADODB.Recordset
240         rs.Open sSQL, Conn, 1, 3

242         Do Until rs.EOF
244             txtDiscounts.Text = Val(txtDiscounts.Text) + Val(rs("DiscountAmount"))
246             rs.MoveNext
            Loop

248         txtTotalRevenue.Text = Val(txtNetSalesTotal.Text) + Val(txtTaxCollected.Text) + Val(txtDiscounts.Text)

        Else
                          
            sSQL = "SELECT  DISTINCT Transactions.TransactionNo, Transactions.DiscountAmount FROM Transactions " & _
                "INNER JOIN DiscountSetup ON Transactions.DiscountCode = DiscountSetup.DiscountCode " & _
                "WHERE ( Transactions.TransactionDate='" & strZTapeDate & "') AND (DiscountSetup.DiscountAmount <> 0) AND (DiscountSetup.DiscountCode <> N'001') " & _
                "GROUP BY Transactions.TransactionNo"
                
            sSQL = "SELECT DISTINCT TransactionNo, DiscountAmount From Transactions WHERE TransactionDate='" & strZTapeDate & "' AND (DiscountCode <> N'001')"
        
252         Debug.Print sSQL
254         Set rs = New ADODB.Recordset
256         rs.Open sSQL, Conn, 1, 3

258         Do Until rs.EOF
260             txtDiscounts.Text = Val(txtDiscounts.Text) + Val(rs("DiscountAmount"))
262             rs.MoveNext
            Loop

264         txtTotalRevenue.Text = Val(txtNetSalesTotal.Text) + Val(txtTaxCollected.Text)  '+ Val(txtDiscounts.Text)
        End If
        
        'If ZTapeType = ActualTaxes Then
        
        'For Total Cash Revenue
266     sSQL = "SELECT TransactionNo, SUM(ItemPrice * ItemQuantity) AS TotalAmount, TaxAmount, DiscountAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') AND (PaymentType='Cash') GROUP BY TaxAmount, DiscountAmount, TransactionNo " & _
           " ORDER BY TransactionNo"

268     Debug.Print sSQL
270     Set rs = New ADODB.Recordset
272     rs.Open sSQL, Conn, 1, 3

274     Do Until rs.EOF
276         txtTotalCashRevenue.Text = Val(txtTotalCashRevenue.Text) + Val(rs("TotalAmount")) + Val(rs("TaxAmount")) - Val(rs("DiscountAmount"))
278         rs.MoveNext
        Loop
    
        'For Total Voids
280     sSQL = "SELECT COUNT(TransactionNo) AS TotalVoids, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' AND TransactionsVoid=1"
282     Set rs = New ADODB.Recordset
284     rs.Open sSQL, Conn, 1, 3

286     If rs.EOF = False Then
288         txtVoidsCount.Text = rs("TotalVoids")
290         txtVoidsTotalAmount.Text = Val(rs("TotalAmount") & "")
        End If
    
        'For Checks Begun
292     sSQL = "SELECT COUNT(DISTINCT TransactionNo) AS TotalChecks, SUM(ItemPrice + ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "'"

294     Set rs = New ADODB.Recordset
296     rs.Open sSQL, Conn, 1, 3
    
298     If rs.EOF = False Then
300         txtBegunCount.Text = rs("TotalChecks")
302         txtBegunAmount.Text = rs("TotalAmount")
        End If
    
        'For Checks Paid
304     sSQL = "SELECT COUNT(DISTINCT TransactionNo) AS TotalChecks, SUM(ItemPrice + ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' AND (TransactionClose = 0)"

306     Set rs = New ADODB.Recordset
308     rs.Open sSQL, Conn, 1, 3
    
310     If rs.EOF = False Then
312         txtPaidCount.Text = Val(txtBegunCount.Text) - Val(rs("TotalChecks") & "")
314         txtPaidAmount.Text = Val(txtBegunAmount.Text) - Val(rs("TotalAmount") & "")
        End If
    
        'For # Guest
316     sSQL = "SELECT TransactionNo, Customers AS Customers From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' GROUP BY TransactionNo, Customers"
318     Set rs = New ADODB.Recordset
320     rs.Open sSQL, Conn, 1, 3
    
322     Do Until rs.EOF
324         txtGuestAvgCount.Text = Val(txtGuestAvgCount.Text) + Val(rs("Customers"))
326         txtChecksAvgCount.Text = Val(txtChecksAvgCount.Text) + 1
328         rs.MoveNext
        Loop

330     txtGuestAvgAmount.Text = Round(txtNetSalesTotal.Text / txtGuestAvgCount.Text, 0)
332     txtChecksAvgAmount.Text = Round(txtNetSalesTotal.Text / txtChecksAvgCount.Text, 0)
   
        'For Bverages and Food
334     sSQL = "SELECT DISTINCT SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions INNER JOIN " & _
           "ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE Transactions.TransactionDate='" & strZTapeDate & "' AND ItemSetup.CatagoryCode='0004'"
            
336     Set rs = New ADODB.Recordset
338     rs.Open sSQL, Conn, 1, 3
    
340     If rs.EOF = False Then
342         txtBeverageAmount.Text = rs("TotalAmount")
344         txtFoodAmount.Text = Val(txtNetSalesTotal.Text) - Val(txtBeverageAmount.Text)
346         txtTotalMajorGroups.Text = Val(txtFoodAmount.Text) + Val(txtBeverageAmount.Text)
        End If
    
        'For 4 Inch
348     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' AND ItemName LIKE '%4%'"
350     Set rs = New ADODB.Recordset
352     rs.Open sSQL, Conn, 1, 3

354     If rs.EOF = False Then
356         txt4InchCount.Text = rs("ItemCount")
358         txt4InchAmount.Text = rs("TotalAmount")
        End If
        
        'For 4 Inch Deals Count
360     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount, ItemSetup.Quantity " & _
           "FROM Transactions INNER JOIN ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND " & _
           "(Transactions.ItemName LIKE N'%DEAL%' AND Transactions.ItemName LIKE N'%4%') GROUP BY ItemSetup.Quantity"
362     Debug.Print sSQL
364     Set rs = New ADODB.Recordset
366     rs.Open sSQL, Conn, 1, 3

368     Do Until rs.EOF
370         txt4InchCount.Text = Val(txt4InchCount.Text) - rs("ItemCount") + (rs("ItemCount") * rs("Quantity"))
372         rs.MoveNext
        Loop
    
        'For 6 Inch
374     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' AND ItemName LIKE '%6%'"
376     Set rs = New ADODB.Recordset
378     rs.Open sSQL, Conn, 1, 3

380     If rs.EOF = False Then
382         txt6InchCount.Text = rs("ItemCount")
384         txt6InchAmount.Text = rs("TotalAmount")
        End If
        
        'For 6 Inch Deals Count
386     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount, ItemSetup.Quantity " & _
           "FROM Transactions INNER JOIN ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND " & _
           "(Transactions.ItemName LIKE N'%DEAL%' AND Transactions.ItemName LIKE N'%6%') GROUP BY ItemSetup.Quantity"
388     Debug.Print sSQL
390     Set rs = New ADODB.Recordset
392     rs.Open sSQL, Conn, 1, 3

394     Do Until rs.EOF
396         txt6InchCount.Text = Val(txt6InchCount.Text) - rs("ItemCount") + (rs("ItemCount") * rs("Quantity"))
398         rs.MoveNext
        Loop
        
        'For 12 Inch
        'sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
         "WHERE TransactionDate='" & strZTapeDate & "' AND ItemName LIKE '%12%'"
400     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, Transactions.ItemQuantity, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount, " & _
           "ItemSetup.Quantity FROM Transactions INNER JOIN ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND " & _
           "(Transactions.ItemName LIKE '%12%') GROUP BY Transactions.ItemQuantity, ItemSetup.Quantity"
402     Debug.Print sSQL
404     Set rs = New ADODB.Recordset
406     rs.Open sSQL, Conn, 1, 3
        
408     txtFree12InchCount.Text = 0

410     Do Until rs.EOF
412         txt12InchCount.Text = Val(txt12InchCount.Text) + Val(rs("ItemCount") * rs("Quantity"))
414         txt12InchAmount.Text = Val(txt12InchAmount.Text) + Val(rs("TotalAmount"))
416         rs.MoveNext
        Loop
    
        'For 12 Inch Deals Count
418     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount, ItemSetup.Quantity " & _
           "FROM Transactions INNER JOIN ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND " & _
           "(Transactions.ItemName LIKE N'%DEAL%' AND Transactions.ItemName LIKE N'%12%') GROUP BY ItemSetup.Quantity"
420     Debug.Print sSQL
422     Set rs = New ADODB.Recordset
424     rs.Open sSQL, Conn, 1, 3

426     Do Until rs.EOF
428         txt6InchCount.Text = Val(txt6InchCount.Text) - rs("ItemCount") + (rs("ItemCount") * rs("Quantity"))
430         rs.MoveNext
        Loop
    
        'For Salad Inch
432     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE TransactionDate='" & strZTapeDate & "' AND ItemName LIKE '%Salad%'"
        'Change for adnan
434     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions " & _
           "INNER JOIN ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND (ItemSetup.CatagoryCode='0006')"
                
436     Set rs = New ADODB.Recordset
438     rs.Open sSQL, Conn, 1, 3
    
440     If rs.EOF = False Then
442         txtSaladCount.Text = rs("ItemCount")
444         txtSaladAmount.Text = Val(rs("TotalAmount") & "")
        End If
    
        'For Extra & Add On
446     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions " & _
           "INNER JOIN ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND (ItemSetup.CatagoryCode='0007')"
        
448     Set rs = New ADODB.Recordset
450     rs.Open sSQL, Conn, 1, 3
    
452     If rs.EOF = False Then
454         txtExtraAddonCount.Text = rs("ItemCount")
456         txtExtraAddonAmount.Text = Val(rs("TotalAmount") & "")
        End If
    
        'For SODA
458     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions INNER JOIN " & _
           "ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE Transactions.TransactionDate='" & strZTapeDate & "' AND (Transactions.ItemCode = N'0001' OR " & _
           " Transactions.ItemCode = '0019' OR Transactions.ItemCode = '0055')"

        'Changes for Adnan
460     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions INNER JOIN " & _
           "ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND (ItemSetup.CatagoryCode=N'0004')"

462     Set rs = New ADODB.Recordset
464     rs.Open sSQL, Conn, 1, 3
        
466     If rs.EOF = False Then
468         txtSodaCount.Text = rs("ItemCount")
470         txtSodaAmount.Text = rs("Totalamount")
        End If
    
        'For Other Beverages
472     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions INNER JOIN " & _
           "ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND (Transactions.ItemCode='0002' OR " & _
           "Transactions.ItemCode='0003' OR Transactions.ItemCode='0065')"
        
        'Changes for Adnan
474     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions INNER JOIN " & _
           "ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND (ItemSetup.CatagoryCode=N'0003')"

476     Debug.Print sSQL
478     Set rs = New ADODB.Recordset
480     rs.Open sSQL, Conn, 1, 3
    
482     If rs.EOF = False Then
484         txtOtherBeveragesCount.Text = rs("ItemCount")
486         txtOtherBeveragesAmount.Text = rs("TotalAmount")
        End If
    
        'For Chips and Cookies
488     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') AND (ItemCode='0014' OR ItemCode='0012' OR ItemCode='0013' OR ItemCode='0070')"
        
        'Changes for Adnan
490     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions INNER JOIN " & _
           "ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND (ItemSetup.CatagoryCode = N'0005')"
        
492     Set rs = New ADODB.Recordset
494     rs.Open sSQL, Conn, 1, 3
    
496     If rs.EOF = False Then
498         txtMiscellaneousCount.Text = rs("ItemCount")
500         txtMiscellaneousAmount.Text = rs("TotalAmount")
        End If
    
        'For Food Total
502     txtFoodTotalAmount.Text = Val(txt4InchAmount.Text) + Val(txt6InchAmount.Text) + Val(txt12InchAmount.Text) + Val(txtSaladAmount.Text) + Val(txtExtraAddonAmount.Text) + Val(txtSodaAmount.Text) + Val(txtOtherBeveragesAmount.Text) + Val(txtMiscellaneousAmount.Text)
    
        'For Free 6 Inch
504     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') AND (ItemName LIKE '%6%') AND (ItemQuantity = 0)"
506     Set rs = New ADODB.Recordset
508     rs.Open sSQL, Conn, 1, 3
    
510     If rs.EOF = False Then
512         txtFree6InchCount.Text = rs("ItemCount")
        End If
    
        'For Free 12 Inch
514     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') AND (ItemName LIKE '%12%') AND (ItemQuantity = 0)"
516     Set rs = New ADODB.Recordset
518     rs.Open sSQL, Conn, 1, 3
        
520     If rs.EOF = False Then
522         txtFree12InchCount.Text = rs("ItemCount")
        End If
    
        'For Free Salad Inch
524     sSQL = "SELECT DISTINCT COUNT(ItemCode) AS ItemCount, SUM(ItemPrice * ItemQuantity) AS TotalAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') AND (ItemName LIKE '%Salad%') AND (ItemQuantity = 0)"
        
        'Changes for Adnan
526     sSQL = "SELECT DISTINCT COUNT(Transactions.ItemCode) AS ItemCount, SUM(Transactions.ItemPrice * Transactions.ItemQuantity) AS TotalAmount FROM Transactions INNER JOIN " & _
           "ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode WHERE (Transactions.TransactionDate='" & strZTapeDate & "') AND (Transactions.ItemQuantity = 0) AND (ItemSetup.CatagoryCode = N'0006')"
        
528     Debug.Print sSQL
530     Set rs = New ADODB.Recordset
532     rs.Open sSQL, Conn, 1, 3
    
534     If rs.EOF = False Then
536         txtFreeSaladCount.Text = rs("ItemCount")
        End If
    
538     txtTotalFreeAmount.Text = Val(txtFree6InchCount.Text) + Val(txtFree12InchCount.Text) + Val(txtFreeSaladCount.Text)
    
        'For Cash Financials
540     sSQL = "SELECT TransactionNo AS TransactionNo, SUM(ItemPrice * ItemQuantity) AS NetAmount, TaxAmount, DiscountAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') GROUP BY PaymentType, TransactionNo, TaxAmount, DiscountAmount " & _
           "HAVING (PaymentType = 'Cash') ORDER BY TransactionNo"
        
542     Debug.Print sSQL
544     Set rs = New ADODB.Recordset
546     rs.Open sSQL, Conn, 1, 3

548     Do Until rs.EOF
550         txtCashCount.Text = Val(txtCashCount.Text) + 1
552         txtCashAmount.Text = Val(txtCashAmount.Text) + (Val(rs("NetAmount") & "") + Val(rs("TaxAmount") & "") - Val(rs("DiscountAmount")))
554         rs.MoveNext
        Loop
            
        'For Credit Card Financials
        'AND (SUM(ItemPrice * ItemQuantity) > 0)
556     sSQL = "SELECT TransactionNo AS TransactionNo, SUM(ItemPrice * ItemQuantity) AS NetAmount, TaxAmount, DiscountAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') GROUP BY PaymentType, TransactionNo, TaxAmount, DiscountAmount " & _
           "HAVING (PaymentType = 'Credit Card') ORDER BY TransactionNo"
558     Set rs = New ADODB.Recordset
560     rs.Open sSQL, Conn, 1, 3
    
562     Do Until rs.EOF
564         txtCreditCardCount.Text = Val(txtCreditCardCount.Text) + 1
566         txtCreditCardAmount.Text = Val(txtCreditCardAmount.Text) + (Val(rs("NetAmount") & "") + Val(rs("TaxAmount") & "") - Val(rs("DiscountAmount") & ""))
568         rs.MoveNext
        Loop
    
        'For Credit A/C Financials
570     sSQL = "SELECT TransactionNo AS TransactionNo, SUM(ItemPrice * ItemQuantity) AS NetAmount, TaxAmount, DiscountAmount From Transactions " & _
           "WHERE (TransactionDate='" & strZTapeDate & "') GROUP BY PaymentType, TransactionNo, TaxAmount, DiscountAmount " & _
           "HAVING (PaymentType = 'Credit A/C') ORDER BY TransactionNo"
572     Set rs = New ADODB.Recordset
574     rs.Open sSQL, Conn, 1, 3
    
576     Do Until rs.EOF
578         txtCreditACCount.Text = Val(txtCreditACCount.Text) + 1
580         txtCreditACAmount.Text = Val(txtCreditACAmount.Text) + (Val(rs("NetAmount") & "") + Val(rs("TaxAmount") & "") - Val(rs("DiscountAmount") & ""))
582         rs.MoveNext
        Loop

584     txtFinancialsTotal.Text = Val(txtCashAmount.Text) + Val(txtCreditCardAmount.Text) + Val(txtCreditACAmount.Text)
        
        'Update the daily total to the new table
586     sSQL = "SELECT * From DailySummary WHERE TransactionDate='" & strZTapeDate & "'"
588     Set rsDailySummary = New ADODB.Recordset
590     rsDailySummary.Open sSQL, Conn, 1, 3
    
592     If rsDailySummary.EOF = True Then
594         rsDailySummary.AddNew
596         rsDailySummary("TransactionDate") = strZTapeDate
        End If

598     rsDailySummary("NetAmount") = txtNetSalesTotal.Text
600     rsDailySummary("DiscountAmount") = txtDiscounts.Text
602     rsDailySummary("TaxesAmount") = txtTaxCollected.Text
604     rsDailySummary.Update
    
606     txtTotalCashRevenue.Text = Val(txtTotalRevenue.Text) - Val(txtDiscounts.Text) - Val(txtCreditACAmount.Text) - Val(txtCreditCardAmount.Text)
        '554     txtTotalCashRevenue.Text = txtFinancialsTotal.Text
        'Changes for the Subway
608     txtCashAmount.Text = Val(txtTotalRevenue.Text) - Val(txtDiscounts.Text) - Val(txtCreditACAmount.Text) - Val(txtCreditCardAmount.Text)
610     txtFinancialsTotal.Text = Val(txtCashAmount.Text) + Val(txtCreditCardAmount.Text) + Val(txtCreditACAmount.Text)
        
612     sSQL = "SELECT SUM(NetAmount) AS NetAmount, SUM(DiscountAmount) AS DiscountAmount, SUM(TaxesAmount) AS TaxesAmount From DailySummary " & _
           "WHERE (TransactionDate<='" & strZTapeDate & "')"
        
614     Set rs = New ADODB.Recordset
616     rs.Open sSQL, Conn, 1, 3
        
618     If rs.EOF = False Then
620         txtNRGT.Text = Val(rs("NetAmount")) + Val(rs("TaxesAmount")) - Val(rs("DiscountAmount"))
        End If

        '<EhFooter>
        Exit Sub

ActiveReport_ReportStart_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.repZTapeReport.ActiveReport_ReportStart " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

