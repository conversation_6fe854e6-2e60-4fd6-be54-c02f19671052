VERSION 5.00
Object = "{CDF3B183-D408-11CE-AE2C-0080C786E37D}#3.0#0"; "Edt32x30.ocx"
Object = "{7888C00A-4808-4D27-9AAE-BD36EC13D16F}#1.0#0"; "LVbutton.ocx"
Object = "{831FDD16-0C5C-11D2-A9FC-0000F8754DA1}#2.0#0"; "mscomctl.ocx"
Begin VB.Form frmDetailOfMemebersBillingPrint 
   BorderStyle     =   1  'Fixed Single
   Caption         =   "Memebers Billing Report Print"
   ClientHeight    =   3975
   ClientLeft      =   3150
   ClientTop       =   3300
   ClientWidth     =   7020
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   9.75
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   3975
   ScaleWidth      =   7020
   Begin MSComctlLib.ProgressBar ProgressBar1 
      Height          =   300
      Left            =   420
      TabIndex        =   9
      Top             =   2220
      Visible         =   0   'False
      Width           =   6315
      _ExtentX        =   11139
      _ExtentY        =   529
      _Version        =   393216
      Appearance      =   1
   End
   Begin VB.ComboBox cmbMembersName 
      Height          =   360
      Left            =   2265
      Style           =   2  'Dropdown List
      TabIndex        =   6
      Top             =   555
      Width           =   3180
   End
   Begin LVbuttons.LaVolpeButton cmdShowReport 
      Height          =   540
      Left            =   1350
      TabIndex        =   4
      Top             =   3030
      Width           =   1650
      _ExtentX        =   2910
      _ExtentY        =   953
      BTYPE           =   3
      TX              =   "ShowReport"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   9.75
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   1
      BCOL            =   14215660
      FCOL            =   0
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "repDetailOfMemebersBilling.frx":0000
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin EditLib.fpDateTime txtFromDate 
      Height          =   330
      Left            =   2265
      TabIndex        =   2
      Top             =   1035
      Width           =   1365
      _Version        =   196608
      _ExtentX        =   2408
      _ExtentY        =   582
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   9.75
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   -2147483643
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   1
      AlignTextV      =   1
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "06/05/2011"
      DateCalcMethod  =   0
      DateTimeFormat  =   0
      UserDefinedFormat=   ""
      DateMax         =   "00000000"
      DateMin         =   "00000000"
      TimeMax         =   "000000"
      TimeMin         =   "000000"
      TimeString1159  =   ""
      TimeString2359  =   ""
      DateDefault     =   "00000000"
      TimeDefault     =   "000000"
      TimeStyle       =   0
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      PopUpType       =   0
      DateCalcY2KSplit=   60
      CaretPosition   =   0
      IncYear         =   1
      IncMonth        =   1
      IncDay          =   1
      IncHour         =   1
      IncMinute       =   1
      IncSecond       =   1
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      StartMonth      =   4
      ButtonAlign     =   0
      BoundDataType   =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin EditLib.fpDateTime txtToDate 
      Height          =   330
      Left            =   2265
      TabIndex        =   3
      Top             =   1515
      Width           =   1365
      _Version        =   196608
      _ExtentX        =   2408
      _ExtentY        =   582
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   9.75
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   -2147483643
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   1
      AlignTextV      =   1
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "06/05/2011"
      DateCalcMethod  =   0
      DateTimeFormat  =   0
      UserDefinedFormat=   ""
      DateMax         =   "00000000"
      DateMin         =   "00000000"
      TimeMax         =   "000000"
      TimeMin         =   "000000"
      TimeString1159  =   ""
      TimeString2359  =   ""
      DateDefault     =   "00000000"
      TimeDefault     =   "000000"
      TimeStyle       =   0
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      PopUpType       =   0
      DateCalcY2KSplit=   60
      CaretPosition   =   0
      IncYear         =   1
      IncMonth        =   1
      IncDay          =   1
      IncHour         =   1
      IncMinute       =   1
      IncSecond       =   1
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      StartMonth      =   4
      ButtonAlign     =   0
      BoundDataType   =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin LVbuttons.LaVolpeButton cmdExit 
      Cancel          =   -1  'True
      Height          =   540
      Left            =   3375
      TabIndex        =   5
      Top             =   3030
      Width           =   1650
      _ExtentX        =   2910
      _ExtentY        =   953
      BTYPE           =   3
      TX              =   "Exit"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   9.75
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   1
      BCOL            =   14215660
      FCOL            =   0
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "repDetailOfMemebersBilling.frx":001C
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin VB.Label lblMemberCode 
      AutoSize        =   -1  'True
      Height          =   240
      Left            =   5640
      TabIndex        =   8
      Top             =   615
      Width           =   1170
   End
   Begin VB.Label lblMembersName 
      AutoSize        =   -1  'True
      Caption         =   "Members Name:"
      Height          =   240
      Left            =   435
      TabIndex        =   7
      Top             =   600
      Width           =   1575
   End
   Begin VB.Label lblToDate 
      AutoSize        =   -1  'True
      Caption         =   "To Date:"
      Height          =   240
      Left            =   435
      TabIndex        =   1
      Top             =   1560
      Width           =   1110
   End
   Begin VB.Label lblFromDate 
      AutoSize        =   -1  'True
      Caption         =   "From Date:"
      Height          =   240
      Left            =   435
      TabIndex        =   0
      Top             =   1080
      Width           =   1110
   End
End
Attribute VB_Name = "frmDetailOfMemebersBillingPrint"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub cmdExit_Click()
        '<EhHeader>
        On Error GoTo cmdExit_Click_Err
        '</EhHeader>
    
100     Unload Me
    
        '<EhFooter>
        Exit Sub

cmdExit_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Project1.repDetailOfMemebersBilling.cmdExit_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdShowReport_Click()
        '<EhHeader>
        On Error GoTo cmdShowReport_Click_Err
        '</EhHeader>
    
        Dim FromDate As String
        Dim ToDate As String
        
100     sSQL = "Delete from YearlyBillingSummary"
102     Conn.Execute sSQL
        
        Dim MemberCode As String
        Dim MemberName As String
        Dim JAN, FEB, MAR, APR, MAY, JUN, JUL, AUG, SEP, OCT, NOV, DEC As Integer
        Dim rsMembers As New ADODB.Recordset
        Dim rsBilling As New ADODB.Recordset
        
104     FromDate = Format(txtFromDate.Text, "dd/mmm/yyyy")
106     ToDate = Format(txtToDate.Text, "dd/mmm/yyyy")

108     sSQL = "SELECT MemberCode, MemberName From MemberInformation WHERE (RIGHT(MemberCode, 3) = '001') ORDER BY MemberCode"
110     Set rsMembers = New ADODB.Recordset
112     rsMembers.Open sSQL, Conn, 1, 3

114     ProgressBar1.Visible = True
116     ProgressBar1.Min = 0
118     ProgressBar1.Max = 90
120     ProgressBar1.Value = 0

122     Do Until rsMembers.EOF
124         MemberCode = rsMembers("MemberCode")
126         MemberName = rsMembers("MemberName")
            
128         sSQL = "SELECT DISTINCT BillIssueDate, CurrentBillAmount From MemberTransactions " & _
               "WHERE (MemberPayment = 0) AND (BillIssueDate BETWEEN '" & FromDate & "' AND '" & ToDate & "') " & _
               "AND (MemberCode = '" & MemberCode & "') ORDER BY BillIssueDate"
130         Set rsBilling = New ADODB.Recordset
132         rsBilling.Open sSQL, Conn, 1, 3
                
134         Do Until rsBilling.EOF

136             If UCase(Left(MonthName(Month(rsBilling("BillIssueDate"))), 3)) = "JAN" Then
138                 JAN = rsBilling("CurrentBillAmount")
140             ElseIf UCase(Left(MonthName(Month(rsBilling("BillIssueDate"))), 3)) = "FEB" Then
142                 FEB = rsBilling("CurrentBillAmount")
144             ElseIf UCase(Left(MonthName(Month(rsBilling("BillIssueDate"))), 3)) = "MAR" Then
146                 MAR = rsBilling("CurrentBillAmount")
148             ElseIf UCase(Left(MonthName(Month(rsBilling("BillIssueDate"))), 3)) = "APR" Then
150                 APR = rsBilling("CurrentBillAmount")
152             ElseIf UCase(Left(MonthName(Month(rsBilling("BillIssueDate"))), 3)) = "MAY" Then
154                 MAY = rsBilling("CurrentBillAmount")
156             ElseIf UCase(Left(MonthName(Month(rsBilling("BillIssueDate"))), 3)) = "JUN" Then
158                 JUN = rsBilling("CurrentBillAmount")
160             ElseIf UCase(Left(MonthName(Month(rsBilling("BillIssueDate"))), 3)) = "JUL" Then
162                 JUL = rsBilling("CurrentBillAmount")
164             ElseIf UCase(Left(MonthName(Month(rsBilling("BillIssueDate"))), 3)) = "AUG" Then
166                 AUG = rsBilling("CurrentBillAmount")
168             ElseIf UCase(Left(MonthName(Month(rsBilling("BillIssueDate"))), 3)) = "SEP" Then
170                 SEP = rsBilling("CurrentBillAmount")
172             ElseIf UCase(Left(MonthName(Month(rsBilling("BillIssueDate"))), 3)) = "OCT" Then
174                 OCT = rsBilling("CurrentBillAmount")
176             ElseIf UCase(Left(MonthName(Month(rsBilling("BillIssueDate"))), 3)) = "NOV" Then
178                 NOV = rsBilling("CurrentBillAmount")
180             ElseIf UCase(Left(MonthName(Month(rsBilling("BillIssueDate"))), 3)) = "DEC" Then
182                 DEC = rsBilling("CurrentBillAmount")
                End If
                
184             rsBilling.MoveNext
            Loop
            
            Dim rsInsert As ADODB.Recordset
186         sSQL = "Select* from YearlyBillingSummary"
188         Set rs = New ADODB.Recordset
190         rs.Open sSQL, Conn, 1, 3
            
192         rs.AddNew
194         rs("MemberCode") = MemberCode
196         rs("MemberName") = MemberName
198         rs("JAN") = JAN
200         rs("FEB") = FEB
202         rs("MAR") = MAR
204         rs("APR") = APR
206         rs("MAY") = MAY
208         rs("JUN") = JUN
210         rs("JUL") = JUL
212         rs("AUG") = AUG
214         rs("SEP") = SEP
216         rs("OCT") = OCT
218         rs("NOV") = NOV
220         rs("DEC") = DEC
222         rs.Update
224         rs.Close
            
            '174         sSQL = "INSERT INTO YearlyBillingSummary (MemberCode, MemberName, JAN, FEB, MAR, APR, MAY, JUN, JUL, AUG, SEP, OCT, NOV, [DEC]) " & _
             "VALUES ('MemberCode', 'MemberName', JAN, FEB, MAR, APR, MAY, JUN, JUL, AUG, SEP, OCT, NOV, DEC)"
            '176         Conn.Execute sSQL
226         ProgressBar1.Value = ProgressBar1.Value + 1
228         rsMembers.MoveNext
        Loop

        '<EhFooter>
        Exit Sub

cmdShowReport_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmDetailOfMemebersBillingPrint.cmdShowReport_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>
    
100     Call Fill_MembersName
    
        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Project1.repDetailOfMemebersBilling.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Fill_MembersName()
        '<EhHeader>
        On Error GoTo Fill_MembersName_Err
        '</EhHeader>

100     sSQL = "SELECT DISTINCT MemberCode, MemberName From dbo.MemberInformation ORDER BY MemberName"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     cmbMembersName.Clear
    
108     Do Until rs.EOF
        
110         cmbMembersName.AddItem rs("MemberName")
            'cmbMembersName.ItemData(cmbMembersName.NewIndex) = rs("MemberCode")
112         rs.MoveNext
        Loop
    
        '<EhFooter>
        Exit Sub

Fill_MembersName_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Project1.repDetailOfMemebersBilling.Fill_MembersName " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
