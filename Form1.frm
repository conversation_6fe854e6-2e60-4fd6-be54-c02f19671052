VERSION 5.00
Object = "{648A5603-2C6E-101B-82B6-000000000014}#1.1#0"; "MSCOMM32.OCX"
Begin VB.Form Form1 
   Caption         =   "Form1"
   ClientHeight    =   4245
   ClientLeft      =   3585
   ClientTop       =   2970
   ClientWidth     =   6585
   LinkTopic       =   "Form1"
   ScaleHeight     =   4245
   ScaleWidth      =   6585
   Begin VB.TextBox txtText1 
      Height          =   3420
      Left            =   0
      MultiLine       =   -1  'True
      ScrollBars      =   2  'Vertical
      TabIndex        =   0
      Text            =   "Form1.frx":0000
      Top             =   780
      Width           =   6540
   End
   Begin MSCommLib.MSComm MSComm1 
      Left            =   5835
      Top             =   60
      _ExtentX        =   1005
      _ExtentY        =   1005
      _Version        =   393216
      DTREnable       =   -1  'True
      InputLen        =   14
      NullDiscard     =   -1  'True
      RThreshold      =   14
      SThreshold      =   14
   End
End
Attribute VB_Name = "Form1"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub Form_Load()
    
    MSComm1.PortOpen = True
    
End Sub

Private Sub MSComm1_OnComm()
    
    txtText1.Text = txtText1.Text & MSComm1.Input
    
    Debug.Print InStr(txtText1.Text, Chr(13))
End Sub
