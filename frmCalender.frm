VERSION 5.00
Object = "{7888C00A-4808-4D27-9AAE-BD36EC13D16F}#1.0#0"; "LVbuttons.ocx"
Object = "{4DBFB8CD-9EF9-11D0-8BC4-00AA00B42B7C}#3.0#0"; "Cal32x30.ocx"
Begin VB.Form frmCalender 
   BackColor       =   &H00E0E0E0&
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "Lawn Booking Calender"
   ClientHeight    =   10065
   ClientLeft      =   105
   ClientTop       =   2310
   ClientWidth     =   14010
   BeginProperty Font 
      Name            =   "Consolas"
      Size            =   12
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmCalender.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   10065
   ScaleWidth      =   14010
   ShowInTaskbar   =   0   'False
   Begin VB.Frame Frame1 
      Appearance      =   0  'Flat
      BackColor       =   &H00EAD08E&
      ForeColor       =   &H80000008&
      Height          =   615
      Left            =   0
      TabIndex        =   3
      Top             =   -135
      Width           =   14010
      Begin VB.Label Label1 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Lawns Booking Calender"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   18
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Height          =   435
         Left            =   135
         TabIndex        =   4
         Top             =   165
         Width           =   4935
      End
   End
   Begin VB.ComboBox cmbLawnName 
      Height          =   405
      Left            =   1530
      Style           =   2  'Dropdown List
      TabIndex        =   1
      TabStop         =   0   'False
      Top             =   660
      Width           =   3045
   End
   Begin CalObjXLib.fpCalendar fpCalendar1 
      Height          =   8850
      Left            =   45
      TabIndex        =   0
      Top             =   1170
      Width           =   13950
      _Version        =   196608
      _ExtentX        =   24606
      _ExtentY        =   15610
      _StockProps     =   68
      FirstDayOfWeek  =   0
      CurrentDate     =   "20090714"
      DateMin         =   "00000000"
      DateMax         =   "00000000"
      GrayAreaStyle   =   0
      GrayAreaBackColor=   16777215
      GrayAreaForeColor=   -2147483632
      HeaderStyle     =   1
      MonthHeaderStyle=   1
      YearHeaderStyle =   2
      BorderGrayAreaColor=   -2147483637
      ElementPictureBackground=   0   'False
      DisplayFormat   =   2
      BorderInnerStyle=   0
      BorderInnerHighlightColor=   -2147483633
      BorderInnerShadowColor=   -2147483642
      BorderInnerWidth=   1
      BorderOuterStyle=   0
      BorderOuterHighlightColor=   -2147483628
      BorderOuterShadowColor=   -2147483632
      BorderOuterWidth=   1
      BorderFrameWidth=   0
      BorderOutlineColor=   -2147483642
      BorderFrameColor=   -2147483633
      BorderOutlineWidth=   1
      BorderOutlineStyle=   1
      SpeedScrollYearIncrement=   1
      SpeedScrollMonthIncrement=   1
      GrayAreaAllowScroll=   0   'False
      WeekNumbers     =   0
      WeekDayHeader   =   3
      ElementTextStyle=   "frmCalender.frx":000C
      DrawFocusRect   =   1
      MultiSelect     =   0
      YearStartMonth  =   1
      YearStartDay    =   1
      HeaderSeparatorWidth=   1
      HeaderSeparatorColor=   16777215
      YearFormatStyle =   4
      RangeBeginDate  =   "00000000"
      RangeEndDate    =   "00000000"
      GridLineColor   =   12632256
      GridLineStyle   =   4
      AutoSet         =   -1  'True
      InheritOverride =   1
      CompactFormat   =   ""
      MouseIcon       =   "frmCalender.frx":02B5
   End
   Begin LVbuttons.LaVolpeButton cmdExit 
      Cancel          =   -1  'True
      Height          =   510
      Left            =   12570
      TabIndex        =   5
      Top             =   585
      Width           =   1275
      _ExtentX        =   2249
      _ExtentY        =   900
      BTYPE           =   3
      TX              =   "E&xit"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   1
      BCOL            =   14215660
      FCOL            =   0
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmCalender.frx":02D1
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin VB.Label lblLawnID 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Lawn Name:"
      Height          =   285
      Left            =   90
      TabIndex        =   2
      Top             =   690
      Width           =   1350
   End
End
Attribute VB_Name = "frmCalender"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
Dim LawnID As String
Dim CurrentMonth As Integer
Dim CurrentYear As Integer

Private Sub cmbLawnName_Click()
        '<EhHeader>
        On Error GoTo cmbLawnName_Click_Err
        '</EhHeader>
    
100     LawnID = Format(cmbLawnName.ItemData(cmbLawnName.ListIndex), "0###")
        
102     If CurrentMonth <> 0 Then

104         Call MarkCalender(CurrentMonth, CurrentYear)

        End If
    
        '<EhFooter>
        Exit Sub

cmbLawnName_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmCalender.cmbLawnName_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdExit_Click()
        '<EhHeader>
        On Error GoTo cmdExit_Click_Err
        '</EhHeader>

100     Unload Me
        
        '<EhFooter>
        Exit Sub

cmdExit_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmCalender.cmdExit_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>

100     CenterForm Me
102     Call FillcmbLawnName

104     With fpCalendar1
106         .AutoStyle = AutoStyleFlat
108         .MonthHeaderStyle = MonthHeaderScroll
110         .YearHeaderStyle = YearHeaderScroll
112         .FontName = "Consolas"
114         .FontBold = True
116         .FontSize = 15
118         .Element = ElementDays
120         .ElementForeColor = vbBlue
122         .ElementAlignTextH = TextHLeft
124         .ElementAlignTextV = TextVTop
        End With

126     Call MarkCalender(Month(Date), Year(Date))
        fpCalendar1_ViewChange Month(Date) - 1, Day(Date), Year(Date), Month(Date), Day(Date), Year(Date), 0

        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmCalender.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub FillcmbLawnName()
        '<EhHeader>
        On Error GoTo FillcmbLawnName_Err
        '</EhHeader>
        
100     sSQL = "Select * from LawnSetup order by LawnName"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3

106     cmbLawnName.Clear

        cmbLawnName.AddItem "All Lawns"
        cmbLawnName.ItemData(cmbLawnName.NewIndex) = "0000"
108     Do Until rs.EOF
110         cmbLawnName.AddItem rs("LawnName")
112         cmbLawnName.ItemData(cmbLawnName.NewIndex) = rs("LawnID")
114         rs.MoveNext
        Loop
        
116     If cmbLawnName.ListCount > 0 Then cmbLawnName.ListIndex = 0

        '<EhFooter>
        Exit Sub

FillcmbLawnName_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmCalender.FillcmbLawnName " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub fpCalendar1_ViewChange(BeginMonth As Integer, _
                                   BeginDay As Integer, _
                                   BeginYear As Integer, _
                                   EndMonth As Integer, _
                                   EndDay As Integer, _
                                   EndYear As Integer, _
                                   Cancel As Integer)
        '<EhHeader>
        On Error GoTo fpCalendar1_ViewChange_Err
        '</EhHeader>
        
        CurrentMonth = EndMonth
        CurrentYear = EndYear
        
100     MarkCalender EndMonth, EndYear

        '<EhFooter>
        Exit Sub

fpCalendar1_ViewChange_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmCalender.fpCalendar1_ViewChange " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub MarkCalender(intMonthID As Integer, _
   intYearID As Integer)
        '<EhHeader>
        On Error GoTo MarkCalender_Err
        '</EhHeader>

        Dim FromDate As String
        Dim ToDate As String
        
100     FromDate = Format("01/" & intMonthID & "/" & intYearID, "dd/mmm/yyyy")

102     If intMonthID < 12 Then
104         ToDate = Format(CDate("01/" & intMonthID + 1 & "/" & intYearID) - 1, "dd/mmm/yyyy")
        Else
106         ToDate = Format(CDate("01/" & 1 & "/" & intYearID + 1) - 1, "dd/mmm/yyyy")
        End If
    
108     If LawnID = "0000" Then
110         sSQL = "SELECT BookingInformation.*, EventSetup.EventName AS EventName FROM BookingInformation INNER JOIN EventSetup ON BookingInformation.EventID = EventSetup.EventID " & _
               "WHERE (BookingInformation.TransactionType='LB') AND (BookingInformation.EventDate BETWEEN '" & FromDate & "' AND '" & ToDate & "') "
               
112         sSQL = "SELECT BookingInformation.*, EventSetup.EventName AS EventName, LawnSetup.LawnName AS LawnName FROM BookingInformation INNER JOIN EventSetup " & _
               "ON BookingInformation.EventID = EventSetup.EventID INNER JOIN LawnSetup ON BookingInformation.LawnID = LawnSetup.LawnID WHERE (BookingInformation.TransactionType = 'LB') " & _
               "AND (BookingInformation.EventDate BETWEEN '" & FromDate & "' AND '" & ToDate & "')"
        Else
114         sSQL = "SELECT BookingInformation.*, EventSetup.EventName AS EventName FROM BookingInformation INNER JOIN EventSetup ON BookingInformation.EventID = EventSetup.EventID " & _
               "WHERE (BookingInformation.TransactionType='LB') AND (BookingInformation.LawnID='" & LawnID & "') AND (BookingInformation.EventDate BETWEEN '" & FromDate & "' AND '" & ToDate & "') "
        End If
        
116     Debug.Print sSQL
118     Set rs = New ADODB.Recordset
120     rs.Open sSQL, Conn, 1, 3
        
122     fpCalendar1.CalReset 1, ""
124     fpCalendar1.CalReset 2, ""
126     fpCalendar1.CalReset 3, ""

128     With fpCalendar1
130         .FontName = "Consolas"
132         .FontBold = True

134         If LawnID = "0000" Then
136             .FontSize = 8
            Else
138             .FontSize = 15
            End If

140         .Element = ElementDays
142         .ElementForeColor = vbBlue
144         .ElementAlignTextH = TextHLeft
146         .ElementAlignTextV = TextVTop
        End With

148     fpCalendar1.Element = ElementDayIndex

150     For i = 1 To 31
152         fpCalendar1.ElementIndex = i
154         fpCalendar1.ElementForeColor = vbBlue
        Next

156     Do Until rs.EOF
            Dim DayChange As String
158         DayChange = Day(rs("EventDate"))
160         fpCalendar1.Element = ElementDayIndex
162         fpCalendar1.ElementIndex = Day(rs("EventDate"))
164         fpCalendar1.ElementTextStyle = ElementTextCustomFollows
166         fpCalendar1.ElementTextWrap = True
168         fpCalendar1.ElementForeColor = vbRed

170         If LawnID = "0000" Then

172             Do Until Day(rs("EventDate")) <> DayChange
174                 fpCalendar1.ElementText = fpCalendar1.ElementText & rs("EventName") & "(" & rs("LawnName") & ")" & vbNewLine
176                 rs.MoveNext

178                 If rs.EOF = True Then Exit Do
                Loop

            Else
180             fpCalendar1.ElementText = rs("EventName")
            End If

182         If rs.EOF = True Then Exit Do

'184        fpCalendar1.ElementPicture = LoadPicture(App.Path & "\" & rs("EventName") & ".bmp")
184        fpCalendar1.ElementPicture = LoadResPicture(101, vbResBitmap)
186        fpCalendar1.ElementAlignPictureH = ElementPictureHRight
188        fpCalendar1.ElementAlignPictureV = ElementPictureVBottomText

190         rs.MoveNext
        Loop

        '<EhFooter>
        Exit Sub

MarkCalender_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmCalender.MarkCalender " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
