VERSION 5.00
Begin VB.Form frmSQLConnection 
   BackColor       =   &H00EEEEEE&
   Caption         =   "Please Spacify the SQL Server Setting"
   ClientHeight    =   2535
   ClientLeft      =   4875
   ClientTop       =   6405
   ClientWidth     =   4950
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmSQLConnection.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   ScaleHeight     =   2535
   ScaleWidth      =   4950
   Begin VB.CommandButton cmdCancel 
      Cancel          =   -1  'True
      Caption         =   "Cancel"
      Height          =   405
      Left            =   2475
      TabIndex        =   9
      Top             =   2085
      Width           =   945
   End
   Begin VB.CommandButton cmdOk 
      Caption         =   "OK"
      Default         =   -1  'True
      Height          =   405
      Left            =   1320
      TabIndex        =   8
      Top             =   2085
      Width           =   945
   End
   Begin VB.CheckBox chkAuthentication 
      BackColor       =   &H00EEEEEE&
      Caption         =   "Use NT Authentication"
      Height          =   255
      Left            =   1440
      TabIndex        =   1
      Top             =   525
      Width           =   3285
   End
   Begin VB.Frame Frame1 
      BackColor       =   &H00EEEEEE&
      Height          =   1260
      Left            =   15
      TabIndex        =   5
      Top             =   780
      Width           =   4920
      Begin VB.TextBox txtUserName 
         Appearance      =   0  'Flat
         Height          =   285
         Left            =   1380
         TabIndex        =   2
         Text            =   "txtUserName"
         Top             =   300
         Width           =   3480
      End
      Begin VB.TextBox txtPassword 
         Appearance      =   0  'Flat
         Height          =   285
         IMEMode         =   3  'DISABLE
         Left            =   1380
         PasswordChar    =   "*"
         TabIndex        =   3
         Text            =   "txtPassword"
         Top             =   690
         Width           =   3480
      End
      Begin VB.Label lblUserName 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "User Name:"
         Height          =   195
         Left            =   75
         TabIndex        =   7
         Top             =   345
         Width           =   1125
      End
      Begin VB.Label lblPassword 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Password:"
         Height          =   195
         Left            =   75
         TabIndex        =   6
         Top             =   735
         Width           =   990
      End
   End
   Begin VB.ComboBox txtServer 
      Height          =   315
      Left            =   1440
      TabIndex        =   0
      Top             =   75
      Width           =   3480
   End
   Begin VB.Label Label1 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "SQL Server:"
      Height          =   195
      Left            =   135
      TabIndex        =   4
      Top             =   135
      Width           =   1140
   End
End
Attribute VB_Name = "frmSQLConnection"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
'Use the SQL DMO object to find available SQL Servers
Public oSQLServerDMOApp As SQLDMO.Application
'The Server we are connected to
Public oSQLServerActive As SQLDMO.SQLServer
'User name and password for SQL Server
Private sUsername As String
Private sPassword As String

'Use the SQLServer object to connect to a specific server
Public oSQLServer As SQLDMO.SQLServer

Private Sub cmdOk_Click()
        '<EhHeader>
        On Error GoTo cmdOk_Click_Err
        '</EhHeader>

        On Error GoTo ErrorHandler

100     Set oSQLServer = New SQLDMO.SQLServer

102     oSQLServer.LoginTimeout = -1 '-1 is the ODBC default (60) seconds
        'Connect to the Server

104     If chkAuthentication Then

106         With oSQLServer

                'Use NT Authentication
108             .LoginSecure = True
                'Do not reconnect automatically
110             .AutoReConnect = False
                'Now connect
112             .Connect txtServer.Text

            End With

        Else

114         With oSQLServer

                'Use SQL Server Authentication
116             .LoginSecure = False
                'Do not reconnect automatically
118             .AutoReConnect = False
                'Use SQL Security
120             .Connect txtServer.Text, sUsername, sPassword

            End With

        End If

        '
122     Set oSQLServerActive = oSQLServer
        'MsgBox "Your Login: " & oSQLServer.Login

        'Save Data to Registry
124     SaveSetting ApplicationName, "SQLServer Setting", "SQL_Server", txtServer.Text
126     SaveSetting ApplicationName, "SQLServer Setting", "SQL_UserName", txtUserName.Text
128     SaveSetting ApplicationName, "SQLServer Setting", "SQL_Password", txtPassword.Text
130     SaveSetting ApplicationName, "SQLServer Setting", "SQL_Authentication", chkAuthentication
132     MsgBox "Please restart the application....", vbOKOnly + vbInformation
134     End
    
        Exit Sub

ErrorHandler:
136     MsgBox "Error: " & Err.Number & " " & Err.Description, vbOKOnly, "Login Error"

        '<EhFooter>
        Exit Sub

cmdOk_Click_Err:
        MsgBox Err.Description & vbCrLf & "in School_Management.frmSQLConnection.cmdOk_Click " & "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdCancel_Click()
        '<EhHeader>
        On Error GoTo cmdCancel_Click_Err
        '</EhHeader>

100     Unload Me

        '<EhFooter>
        Exit Sub

cmdCancel_Click_Err:
        MsgBox Err.Description & vbCrLf & "in School_Management.frmSQLConnection.cmdCancel_Click " & "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>
  
        Dim i As Integer
        'Use the SQL DMO Application Object to find the available SQL Servers
100     Set oSQLServerDMOApp = New SQLDMO.Application
      
        Dim namX As NameList
102     Set namX = oSQLServerDMOApp.ListAvailableSQLServers
104     txtServer.Clear

106     For i = 1 To namX.Count

108         txtServer.AddItem namX.Item(i)

        Next

        'Show top server

110     If txtServer.ListCount > 0 Then

112         txtServer.ListIndex = 0

        End If
  
114     CenterForm Me
116     txtUserName.Text = ""
118     txtPassword.Text = ""
        'txtServer.SetFocus

        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & "in School_Management.frmSQLConnection.Form_Load " & "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

'Determine is NT Authentication is to be used or not
Private Sub chkAuthentication_Click()
        '<EhHeader>
        On Error GoTo chkAuthentication_Click_Err
        '</EhHeader>

100     If chkAuthentication.Value = vbChecked Then

            'This assumes a trusted connection
102         Frame1.Enabled = False
104         lblPassword.Enabled = False
106         lblUserName.Enabled = False
108         txtUserName.Text = ""
110         txtPassword.Text = ""

        Else

            'This assumes a login is required
112         Frame1.Enabled = True
114         lblPassword.Enabled = True
116         lblUserName.Enabled = True

        End If

        '<EhFooter>
        Exit Sub

chkAuthentication_Click_Err:
        MsgBox Err.Description & vbCrLf & "in School_Management.frmSQLConnection.chkAuthentication_Click " & "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtPassword_Change()
        '<EhHeader>
        On Error GoTo txtPassword_Change_Err
        '</EhHeader>

100     sPassword = txtPassword.Text

        '<EhFooter>
        Exit Sub

txtPassword_Change_Err:
        MsgBox Err.Description & vbCrLf & "in School_Management.frmSQLConnection.txtPassword_Change " & "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtUserName_Change()
        '<EhHeader>
        On Error GoTo txtUserName_Change_Err
        '</EhHeader>

100     sUsername = txtUserName.Text

        '<EhFooter>
        Exit Sub

txtUserName_Change_Err:
        MsgBox Err.Description & vbCrLf & "in School_Management.frmSQLConnection.txtUserName_Change " & "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

