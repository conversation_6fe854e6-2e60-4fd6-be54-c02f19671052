VERSION 5.00
Object = "{0ECD9B60-23AA-11D0-B351-00A0C9055D8E}#6.0#0"; "MSHFLXGD.OCX"
Begin VB.Form frmDataBrowser 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "Data Browser"
   ClientHeight    =   3570
   ClientLeft      =   2925
   ClientTop       =   1620
   ClientWidth     =   5625
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmDataBrowser.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   3570
   ScaleWidth      =   5625
   ShowInTaskbar   =   0   'False
   Begin VB.CommandButton cmdOk 
      Caption         =   "&OK"
      Default         =   -1  'True
      Height          =   375
      Left            =   1860
      TabIndex        =   7
      Top             =   3135
      Width           =   870
   End
   Begin VB.CommandButton cmdCancel 
      Cancel          =   -1  'True
      Caption         =   "&Cancel"
      Height          =   375
      Left            =   2850
      TabIndex        =   6
      Top             =   3135
      Width           =   870
   End
   Begin VB.Frame Frame1 
      BackColor       =   &H00EAD08E&
      Height          =   525
      Left            =   0
      TabIndex        =   3
      Top             =   -90
      Width           =   5625
      Begin VB.Label Label1 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Data Browser"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   15.75
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Height          =   375
         Left            =   45
         TabIndex        =   4
         Top             =   105
         Width           =   2145
      End
   End
   Begin VB.TextBox txtSearch 
      Appearance      =   0  'Flat
      DataField       =   "EmployeeName"
      DataMember      =   "Employees"
      DataSource      =   "DataEnvironment1"
      Height          =   285
      Left            =   750
      TabIndex        =   0
      Top             =   495
      Width           =   4860
   End
   Begin MSHierarchicalFlexGridLib.MSHFlexGrid MSHFlexGrid1 
      Height          =   2190
      Left            =   0
      TabIndex        =   1
      Top             =   810
      Width           =   5625
      _ExtentX        =   9922
      _ExtentY        =   3863
      _Version        =   393216
      _NumberOfBands  =   1
      _Band(0).Cols   =   2
   End
   Begin VB.Frame Frame2 
      Height          =   120
      Left            =   -45
      TabIndex        =   5
      Top             =   2970
      Width           =   5685
   End
   Begin VB.Label lblFieldLabel 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Search:"
      Height          =   195
      Index           =   1
      Left            =   15
      TabIndex        =   2
      Top             =   540
      Width           =   675
   End
End
Attribute VB_Name = "frmDataBrowser"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub cmdCancel_Click()
        '<EhHeader>
        On Error GoTo cmdCancel_Click_Err
        '</EhHeader>

100     Unload Me

        '<EhFooter>
        Exit Sub

cmdCancel_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmDataBrowser.cmdCancel_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdOk_Click()
        '<EhHeader>
        On Error GoTo cmdOk_Click_Err
        '</EhHeader>

100     MSHFlexGrid1.Col = 1

102     Select Case DataBrowserMode

            Case Is = 1
104             frmMemberInformation.txtMemberCode.Text = Left$(MSHFlexGrid1.Text, 5)
            
106         Case Is = 2
108             frmMonthlyCharges.txtMemberCode.Text = Left$(MSHFlexGrid1.Text, 5)

110         Case Is = 3
112             frmMembersFoodReport.txtMemberCode.Text = Left$(MSHFlexGrid1.Text, 5)

114         Case Is = 4
116             frmPOSPavilianEndClub.txtMemberCode.Text = MSHFlexGrid1.Text

118         Case Is = 5
120             frmPaymentReceived.txtMemberCode.Text = Left$(MSHFlexGrid1.Text, 5)

122         Case Is = 6
124             frmMemberPaymentRecievedReport.txtMemberCode.Text = Left$(MSHFlexGrid1.Text, 5)

126         Case Is = 7
128             frmMemberLedger.txtMemberCode.Text = Left$(MSHFlexGrid1.Text, 5)
            
130         Case Is = 8
132             frmCatersBooking.txtBookingID.Text = MSHFlexGrid1.Text
        
134         Case Is = 9
136             frmDecoratersBooking.txtBookingID.Text = MSHFlexGrid1.Text

138         Case Is = 10
140             frmLawnPayments.txtBookingID.Text = MSHFlexGrid1.Text

            Case Is = 11
                frmDetailOfMemebersAnalysisPrint.txtMemberCode.Text = MSHFlexGrid1.Text
            
        End Select

142     Unload Me

        '<EhFooter>
        Exit Sub

cmdOk_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmDataBrowser.cmdOk_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>

100     txtSearch.Text = ""
102     txtSearch_Change
104     CenterForm Me

        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmDataBrowser.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub MSHFlexGrid1_Click()
        '<EhHeader>
        On Error GoTo MSHFlexGrid1_Click_Err
        '</EhHeader>

100     cmdOk_Click

        '<EhFooter>
        Exit Sub

MSHFlexGrid1_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmDataBrowser.MSHFlexGrid1_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtSearch_Change()
        '<EhHeader>
        On Error GoTo txtSearch_Change_Err
        '</EhHeader>

100     Select Case DataBrowserMode

            Case Is = 1
102             sSQL = "SELECT MemberCode, MemberName, FatherName From MemberInformation Where MemberName Like '%" & txtSearch.Text & "%' Order by MemberCode"

104         Case Is = 2
106             sSQL = "SELECT MemberCode, MemberName, FatherName From MemberInformation Where MemberName Like '%" & txtSearch.Text & "%' Order by MemberCode"
           
108         Case Is = 3
110             sSQL = "SELECT MemberCode, MemberName, FatherName From MemberInformation Where MemberName Like '%" & txtSearch.Text & "%' Order by MemberCode"
            
112         Case Is = 4
114             sSQL = "SELECT MemberCode, MemberName, FatherName From MemberInformation Where MemberName Like '%" & txtSearch.Text & "%' Order by MemberCode"
            
116         Case Is = 5
118             sSQL = "SELECT MemberCode, MemberName, FatherName From MemberInformation Where MemberName Like '%" & txtSearch.Text & "%' Order by MemberCode"
        
120         Case Is = 6
122             sSQL = "SELECT MemberCode, MemberName, FatherName From MemberInformation Where MemberName Like '%" & txtSearch.Text & "%' Order by MemberCode"
        
124         Case Is = 7
126             sSQL = "SELECT MemberCode, MemberName, FatherName From MemberInformation Where MemberName Like '%" & txtSearch.Text & "%' Order by MemberCode"

128         Case Is = 8
130             sSQL = "SELECT DISTINCT BookingID, CustomerName From BookingInformation Where CustomerName Like '%" & txtSearch.Text & "%' Order by BookingID"
            
132         Case Is = 9
134             sSQL = "SELECT DISTINCT BookingID, CustomerName From BookingInformation Where CustomerName Like '%" & txtSearch.Text & "%' Order by BookingID"

136         Case Is = 10
138             sSQL = "SELECT DISTINCT BookingID, CustomerName From BookingInformation Where TransactionType='" & Left(TransactionType, 1) & "B' And CustomerName Like '%" & txtSearch.Text & "%' Order by BookingID"
            
            Case Is = 11
139             sSQL = "SELECT MemberCode, MemberName, FatherName From MemberInformation Where MemberName Like '%" & txtSearch.Text & "%' Order by MemberCode"

        End Select

140     Set rs = New ADODB.Recordset
142     rs.Open sSQL, Conn, 1, 3

144     With MSHFlexGrid1

146         Set .Recordset = rs
148         .ColWidth(0) = TextWidth("A") * 3
150         .ColWidth(1) = TextWidth("A") * 8
152         .ColWidth(2) = TextWidth("A") * 30
154         .ColWidth(3) = TextWidth("A") * 18

        End With

        '<EhFooter>
        Exit Sub

txtSearch_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmDataBrowser.txtSearch_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

