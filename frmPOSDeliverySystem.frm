VERSION 5.00
Object = "{CDF3B183-D408-11CE-AE2C-0080C786E37D}#3.0#0"; "Edt32x30.ocx"
Object = "{B02F3647-766B-11CE-AF28-C3A2FBE76A13}#3.0#0"; "SPR32X30.ocx"
Object = "{648A5603-2C6E-101B-82B6-000000000014}#1.1#0"; "MSCOMM32.OCX"
Begin VB.Form frmPOSDeliverySystem 
   BackColor       =   &H00EEEEEE&
   Caption         =   "POS Delivery System"
   ClientHeight    =   9735
   ClientLeft      =   1155
   ClientTop       =   915
   ClientWidth     =   15720
   BeginProperty Font 
      Name            =   "Arial"
      Size            =   8.25
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmPOSDeliverySystem.frx":0000
   LinkTopic       =   "Form1"
   ScaleHeight     =   9735
   ScaleWidth      =   15720
   Begin VB.PictureBox Picture3 
      Appearance      =   0  'Flat
      BackColor       =   &H80000005&
      Enabled         =   0   'False
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H80000008&
      Height          =   555
      Left            =   12585
      ScaleHeight     =   525
      ScaleWidth      =   2535
      TabIndex        =   11
      Top             =   8730
      Width           =   2565
      Begin EditLib.fpCurrency txtPayableAmount 
         Height          =   555
         Left            =   -15
         TabIndex        =   12
         Top             =   -15
         Width           =   2565
         _Version        =   196608
         _ExtentX        =   4524
         _ExtentY        =   979
         Enabled         =   -1  'True
         MousePointer    =   0
         Object.TabStop         =   -1  'True
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   20.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         BackColor       =   11856344
         ForeColor       =   -2147483640
         ThreeDInsideStyle=   0
         ThreeDInsideHighlightColor=   -2147483633
         ThreeDInsideShadowColor=   -2147483642
         ThreeDInsideWidth=   1
         ThreeDOutsideStyle=   0
         ThreeDOutsideHighlightColor=   -2147483628
         ThreeDOutsideShadowColor=   -2147483632
         ThreeDOutsideWidth=   1
         ThreeDFrameWidth=   0
         BorderStyle     =   1
         BorderColor     =   -2147483642
         BorderWidth     =   1
         ButtonDisable   =   0   'False
         ButtonHide      =   0   'False
         ButtonIncrement =   1
         ButtonMin       =   0
         ButtonMax       =   100
         ButtonStyle     =   0
         ButtonWidth     =   0
         ButtonWrap      =   -1  'True
         ButtonDefaultAction=   -1  'True
         ThreeDText      =   0
         ThreeDTextHighlightColor=   -2147483633
         ThreeDTextShadowColor=   -2147483632
         ThreeDTextOffset=   1
         AlignTextH      =   2
         AlignTextV      =   0
         AllowNull       =   0   'False
         NoSpecialKeys   =   0
         AutoAdvance     =   0   'False
         AutoBeep        =   0   'False
         CaretInsert     =   0
         CaretOverWrite  =   3
         UserEntry       =   0
         HideSelection   =   -1  'True
         InvalidColor    =   -2147483637
         InvalidOption   =   0
         MarginLeft      =   3
         MarginTop       =   3
         MarginRight     =   3
         MarginBottom    =   3
         NullColor       =   -2147483637
         OnFocusAlignH   =   0
         OnFocusAlignV   =   0
         OnFocusNoSelect =   0   'False
         OnFocusPosition =   0
         ControlType     =   0
         Text            =   "Rs:0.00"
         CurrencyDecimalPlaces=   -1
         CurrencyNegFormat=   0
         CurrencyPlacement=   0
         CurrencySymbol  =   ""
         DecimalPoint    =   ""
         FixedPoint      =   -1  'True
         LeadZero        =   0
         MaxValue        =   "9000000000"
         MinValue        =   "-9000000000"
         NegToggle       =   0   'False
         Separator       =   ""
         UseSeparator    =   0   'False
         IncInt          =   1
         IncDec          =   1
         BorderGrayAreaColor=   -2147483637
         ThreeDOnFocusInvert=   0   'False
         ThreeDFrameColor=   -2147483633
         Appearance      =   1
         BorderDropShadow=   0
         BorderDropShadowColor=   -2147483632
         BorderDropShadowWidth=   3
         ButtonColor     =   -2147483633
         AutoMenu        =   0   'False
         ButtonAlign     =   0
         OLEDropMode     =   0
         OLEDragMode     =   0
      End
   End
   Begin VB.PictureBox Picture2 
      Appearance      =   0  'Flat
      AutoSize        =   -1  'True
      BackColor       =   &H80000005&
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H80000008&
      Height          =   1065
      Left            =   11925
      Picture         =   "frmPOSDeliverySystem.frx":000C
      ScaleHeight     =   1035
      ScaleWidth      =   3465
      TabIndex        =   10
      Top             =   0
      Width           =   3495
   End
   Begin VB.PictureBox Picture1 
      Appearance      =   0  'Flat
      AutoSize        =   -1  'True
      BackColor       =   &H80000005&
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H80000008&
      Height          =   1005
      Left            =   6240
      Picture         =   "frmPOSDeliverySystem.frx":06F8
      ScaleHeight     =   975
      ScaleWidth      =   3360
      TabIndex        =   9
      Top             =   45
      Width           =   3390
   End
   Begin VB.ComboBox cmbDiscount 
      BeginProperty Font 
         Name            =   "Arial"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   360
      Left            =   9105
      Style           =   2  'Dropdown List
      TabIndex        =   8
      Top             =   7065
      Width           =   2490
   End
   Begin VB.TextBox txtCardNumber 
      Appearance      =   0  'Flat
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   285
      Left            =   7350
      TabIndex        =   7
      Text            =   "Text1"
      Top             =   12855
      Visible         =   0   'False
      Width           =   2400
   End
   Begin VB.PictureBox picEmployeePicture 
      Appearance      =   0  'Flat
      BackColor       =   &H80000005&
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H80000008&
      Height          =   1800
      Left            =   7365
      ScaleHeight     =   1770
      ScaleWidth      =   2370
      TabIndex        =   6
      TabStop         =   0   'False
      Top             =   11355
      Visible         =   0   'False
      Width           =   2400
   End
   Begin VB.Frame Frame1 
      BackColor       =   &H00EEEEEE&
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   120
      Left            =   0
      TabIndex        =   5
      Top             =   9645
      Width           =   15810
   End
   Begin VB.TextBox txtCallerName 
      Appearance      =   0  'Flat
      Height          =   315
      Left            =   1905
      TabIndex        =   4
      Text            =   "Caller Name"
      Top             =   7605
      Width           =   2835
   End
   Begin VB.TextBox txtTelephoneNo 
      Appearance      =   0  'Flat
      Height          =   315
      Left            =   1905
      TabIndex        =   3
      Text            =   "Telephone No"
      Top             =   7230
      Width           =   2835
   End
   Begin VB.TextBox txtDeliveryAddress 
      Appearance      =   0  'Flat
      Height          =   630
      Left            =   1905
      MultiLine       =   -1  'True
      ScrollBars      =   2  'Vertical
      TabIndex        =   2
      Text            =   "frmPOSDeliverySystem.frx":13A1
      Top             =   7995
      Width           =   6105
   End
   Begin VB.ComboBox cmbSubwayBranch 
      Height          =   330
      ItemData        =   "frmPOSDeliverySystem.frx":13B1
      Left            =   1905
      List            =   "frmPOSDeliverySystem.frx":13BB
      Style           =   2  'Dropdown List
      TabIndex        =   1
      Top             =   9090
      Width           =   3375
   End
   Begin VB.ComboBox cmbPopularPlace 
      Height          =   330
      ItemData        =   "frmPOSDeliverySystem.frx":13EC
      Left            =   1905
      List            =   "frmPOSDeliverySystem.frx":13F6
      Style           =   2  'Dropdown List
      TabIndex        =   0
      Top             =   8685
      Width           =   3375
   End
   Begin FPSpread.vaSpread vaSpread3 
      Height          =   2040
      Left            =   12960
      TabIndex        =   13
      Top             =   1800
      Width           =   2400
      _Version        =   196608
      _ExtentX        =   4233
      _ExtentY        =   3598
      _StockProps     =   64
      Enabled         =   0   'False
      DisplayRowHeaders=   0   'False
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   3
      MaxRows         =   10
      ScrollBars      =   2
      SpreadDesigner  =   "frmPOSDeliverySystem.frx":1412
      UserResize      =   1
   End
   Begin EditLib.fpCurrency txtTotalAmount 
      Height          =   300
      Left            =   13335
      TabIndex        =   14
      Top             =   7110
      Width           =   1830
      _Version        =   196608
      _ExtentX        =   3228
      _ExtentY        =   529
      Enabled         =   0   'False
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin MSCommLib.MSComm MSComm1 
      Left            =   9150
      Top             =   12390
      _ExtentX        =   1005
      _ExtentY        =   1005
      _Version        =   393216
      DTREnable       =   -1  'True
      InputLen        =   14
      RThreshold      =   14
      SThreshold      =   14
   End
   Begin Point_of_Sale_System.lvButtons_H cmdStart 
      Height          =   840
      Left            =   105
      TabIndex        =   15
      Top             =   75
      Width           =   1860
      _ExtentX        =   3281
      _ExtentY        =   1482
      Caption         =   "Start"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPOSDeliverySystem.frx":17BE
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   1
      Left            =   6930
      TabIndex        =   16
      Tag             =   "0005"
      Top             =   1110
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Cookies Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSDeliverySystem.frx":7630
      ImgSize         =   24
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.ucStatusBar UcSStatText 
      Height          =   330
      Left            =   0
      TabIndex        =   17
      Top             =   10470
      Width           =   15480
      _ExtentX        =   27305
      _ExtentY        =   582
      Text            =   " Point of Sale-={by www.intelysol.com"
   End
   Begin FPSpread.vaSpread vaSpread2 
      Height          =   5190
      Left            =   5145
      TabIndex        =   18
      Top             =   1785
      Width           =   7740
      _Version        =   196608
      _ExtentX        =   13653
      _ExtentY        =   9155
      _StockProps     =   64
      EditEnterAction =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   9
      MaxRows         =   20
      ScrollBars      =   2
      SpreadDesigner  =   "frmPOSDeliverySystem.frx":99B2
   End
   Begin FPSpread.vaSpread vaSpread1 
      Height          =   5175
      Left            =   105
      TabIndex        =   19
      Top             =   1785
      Width           =   4950
      _Version        =   196608
      _ExtentX        =   8731
      _ExtentY        =   9128
      _StockProps     =   64
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   3
      MaxRows         =   20
      ScrollBars      =   2
      SpreadDesigner  =   "frmPOSDeliverySystem.frx":A0EC
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   0
      Left            =   1834
      TabIndex        =   20
      Tag             =   "0002"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Beef    Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPOSDeliverySystem.frx":A668
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   2
      Left            =   5232
      TabIndex        =   21
      Tag             =   "0004"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Ice Cream && Drink"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSDeliverySystem.frx":252CA
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   3
      Left            =   8640
      TabIndex        =   22
      Tag             =   "0006"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Others && Salats"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSDeliverySystem.frx":2764C
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   4
      Left            =   90
      TabIndex        =   23
      Tag             =   "0001"
      Top             =   1125
      Width           =   1710
      _ExtentX        =   3016
      _ExtentY        =   1032
      Caption         =   "Chicken Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      CapStyle        =   1
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSDeliverySystem.frx":2821E
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdClose 
      Height          =   630
      Left            =   13545
      TabIndex        =   24
      Top             =   9810
      Width           =   1860
      _ExtentX        =   3281
      _ExtentY        =   1111
      Caption         =   "Close"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      cGradient       =   0
      Mode            =   0
      Value           =   0   'False
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   5
      Left            =   3533
      TabIndex        =   25
      Tag             =   "0003"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Rice    Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPOSDeliverySystem.frx":28DF0
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   6
      Left            =   10329
      TabIndex        =   26
      Tag             =   "0007"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Extra Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSDeliverySystem.frx":2B172
      ImgSize         =   24
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   7
      Left            =   12030
      TabIndex        =   27
      Tag             =   "0008"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Sea Foods"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSDeliverySystem.frx":30964
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin EditLib.fpCurrency txtDiscount 
      Height          =   300
      Left            =   13335
      TabIndex        =   28
      Top             =   7485
      Width           =   1830
      _Version        =   196608
      _ExtentX        =   3228
      _ExtentY        =   529
      Enabled         =   0   'False
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin Point_of_Sale_System.lvButtons_H cmdSendDelivery 
      Height          =   630
      Left            =   11595
      TabIndex        =   29
      Top             =   9810
      Width           =   1695
      _ExtentX        =   2990
      _ExtentY        =   1111
      Caption         =   "Send"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPOSDeliverySystem.frx":36156
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin EditLib.fpCurrency txtSalesTaxAmount 
      Height          =   300
      Left            =   13335
      TabIndex        =   30
      Top             =   7845
      Width           =   1830
      _Version        =   196608
      _ExtentX        =   3228
      _ExtentY        =   529
      Enabled         =   0   'False
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   -1  'True
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin Point_of_Sale_System.lvButtons_H cmdDeliveryPayment 
      Height          =   585
      Left            =   12960
      TabIndex        =   31
      Tag             =   "0100"
      Top             =   6390
      Width           =   2370
      _ExtentX        =   4180
      _ExtentY        =   1032
      Caption         =   "Unlock for Payment"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSDeliverySystem.frx":3BFC8
      ImgSize         =   24
      cBack           =   -2147483633
   End
   Begin FPSpread.vaSpread vaSpread4 
      Height          =   2040
      Left            =   12960
      TabIndex        =   32
      Top             =   4245
      Width           =   2400
      _Version        =   196608
      _ExtentX        =   4233
      _ExtentY        =   3598
      _StockProps     =   64
      Enabled         =   0   'False
      DisplayRowHeaders=   0   'False
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   3
      MaxRows         =   10
      ScrollBars      =   2
      SpreadDesigner  =   "frmPOSDeliverySystem.frx":417BA
      UserResize      =   1
   End
   Begin Point_of_Sale_System.lvButtons_H cmdFree 
      Height          =   585
      Index           =   8
      Left            =   13740
      TabIndex        =   33
      Tag             =   "0008"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Free"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin VB.Frame Frame2 
      BackColor       =   &H00EEEEEE&
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   120
      Left            =   30
      TabIndex        =   34
      Top             =   975
      Width           =   15810
   End
   Begin VB.Label Label5 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Basket:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   15.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   360
      Left            =   12960
      TabIndex        =   48
      Top             =   3885
      Width           =   1260
   End
   Begin VB.Label lblMemberCode 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Label1"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   195
      Left            =   6570
      TabIndex        =   47
      Top             =   11550
      Visible         =   0   'False
      Width           =   645
   End
   Begin VB.Label lblMemberName 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Label1"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   195
      Left            =   6570
      TabIndex        =   46
      Top             =   11850
      Visible         =   0   'False
      Width           =   645
   End
   Begin VB.Label lblSalesTaxRate 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Sales Tax @16%:"
      BeginProperty Font 
         Name            =   "Arial"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   240
      Left            =   11580
      TabIndex        =   45
      Top             =   7905
      Width           =   1620
   End
   Begin VB.Label Label3 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Recieveable:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   480
      Left            =   12570
      TabIndex        =   44
      Top             =   8295
      Width           =   2700
   End
   Begin VB.Label Label2 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Discount:"
      BeginProperty Font 
         Name            =   "Arial"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   240
      Left            =   12330
      TabIndex        =   43
      Top             =   7530
      Width           =   870
   End
   Begin VB.Label lblDiscount 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Discount:"
      BeginProperty Font 
         Name            =   "Arial"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   240
      Left            =   8115
      TabIndex        =   42
      Top             =   7125
      Width           =   870
   End
   Begin VB.Label Label1 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Total Amount:"
      BeginProperty Font 
         Name            =   "Arial"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   240
      Left            =   11865
      TabIndex        =   41
      Top             =   7170
      Width           =   1335
   End
   Begin VB.Label lblLabel6 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Caller Name:"
      BeginProperty Font 
         Name            =   "Arial"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   240
      Left            =   75
      TabIndex        =   40
      Top             =   7635
      Width           =   1245
   End
   Begin VB.Label Label4 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Telephone No:"
      BeginProperty Font 
         Name            =   "Arial"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   240
      Left            =   75
      TabIndex        =   39
      Top             =   7260
      Width           =   1395
   End
   Begin VB.Label lblDeliveryAddress 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Delivery Address:"
      BeginProperty Font 
         Name            =   "Arial"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   240
      Left            =   75
      TabIndex        =   38
      Top             =   8010
      Width           =   1650
   End
   Begin VB.Label lblSubwayBranch 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Subway Branch:"
      BeginProperty Font 
         Name            =   "Arial"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   240
      Left            =   75
      TabIndex        =   37
      Top             =   9135
      Width           =   1650
   End
   Begin VB.Label lblPopularPlace 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Popular Place:"
      BeginProperty Font 
         Name            =   "Arial"
         Size            =   9.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   240
      Left            =   75
      TabIndex        =   36
      Top             =   8730
      Width           =   1650
   End
   Begin VB.Label Label6 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Date: 25-JUL-2008 Time: 12:35 AM"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   480
      Left            =   75
      TabIndex        =   35
      Top             =   9870
      Width           =   7200
   End
End
Attribute VB_Name = "frmPOSDeliverySystem"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
Dim MemberCode As String
Dim CardNumber As String
Dim DiscountCode As String
Dim DiscountAmount As String
Dim IsPercentage As Boolean
Dim TransactionNo As String
Dim SalesTaxRate As String
Dim DockItPrints As Integer
Dim EmployeeCode As String

Private Sub cmbDiscount_Click()
        '<EhHeader>
        On Error GoTo cmbDiscount_Click_Err
        '</EhHeader>
    
100     DiscountCode = Format(cmbDiscount.ItemData(cmbDiscount.ListIndex), "0##")
    
102     sSQL = "Select * From DiscountSetup Where DiscountCode='" & DiscountCode & "'"
104     Set rs = New ADODB.Recordset
106     rs.Open sSQL, Conn, 1, 3
    
108     IsPercentage = rs("IsPercentage")
110     DiscountAmount = rs("DiscountAmount")
                
112     CalculateBill
        
        '<EhFooter>
        Exit Sub

cmbDiscount_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmbDiscount_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdClose_Click()
        '<EhHeader>
        On Error GoTo cmdClose_Click_Err
        '</EhHeader>
    
100     Unload Me
    
        '<EhFooter>
        Exit Sub

cmdClose_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmdClose_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdFree_Click(Index As Integer)
        '<EhHeader>
        On Error GoTo cmdFree_Click_Err
        '</EhHeader>
        
100     vaSpread2.Row = vaSpread2.ActiveRow
102     vaSpread2.Col = 4
104     vaSpread2.Text = 0
106     CalculateBill
        
        '<EhFooter>
        Exit Sub

cmdFree_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmdFree_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub


Private Sub cmdStart_Click()
        '<EhHeader>
        On Error GoTo cmdStart_Click_Err
        '</EhHeader>
                   
100     POS_Lock False

102     sSQL = "Select Max(TransactionNo) as LastNumber  From Transactions Where TransactionDate='" & TransactionDate & "'"
104     Set rs = New ADODB.Recordset
106     rs.Open sSQL, Conn, 1, 3
        
108     If rs.EOF = True Then
110         TransactionNo = 1
        Else
112         TransactionNo = Val(rs("LastNumber") & "") + 1
        End If
        
114     TransactionNo = Format(TransactionNo, "0###")
        
116     IsPercentage = True
118     DiscountAmount = 0
        
120     MsgBox "Transaction No.: " & TransactionNo
        
122     cmdItems_Click 4
        
        '<EhFooter>
        Exit Sub

cmdStart_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmdStart_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdDeliveryPayment_Click()
        '<EhHeader>
        On Error GoTo cmdDeliveryPayment_Click_Err
        '</EhHeader>
    
100     vaSpread3.Enabled = Not vaSpread3.Enabled
102     vaSpread4.Enabled = Not vaSpread4.Enabled

104     If vaSpread3.Enabled = True Or vaSpread4.Enabled = True Then
106         cmdDeliveryPayment.Caption = "Lock Payments"
        Else
108         cmdDeliveryPayment.Caption = "Unlock for Payments"
        End If
    
        '<EhFooter>
        Exit Sub

cmdDeliveryPayment_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmdDeliveryPayment_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>
    
100     CenterForm Me
102     Me.WindowState = vbMaximized
        '106     MSComm1.PortOpen = True
        
104     TransactionDate = Format(TransactionDate, "dd/mmm/yyyy")
                        
112     POS_Lock True
        
114     sSQL = "Select * From TaxesSetup Where TaxesCode='001'"
116     Set rs = New ADODB.Recordset
118     rs.Open sSQL, Conn, 1, 3
        
120     SalesTaxRate = rs("TaxesAmount")
        
124     Call FillcmbDiscount
126     Call LoadDeliveries
        
        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdItems_Click(Index As Integer)
        '<EhHeader>
        On Error GoTo cmdItems_Click_Err
        '</EhHeader>
    
100     sSQL = "Select * From ItemSetup Where CatagoryCode='" & Format(cmdItems(Index).Tag, "0###") & "'"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
        
106     vaSpread1.MaxRows = 0
108     vaSpread1.MaxRows = 1
110     vaSpread1.Row = 1
        'vaSpread1.RowHeight(vaSpread1.Row) = 20

112     Do Until rs.EOF
114         vaSpread1.Col = 0
116         vaSpread1.Text = rs("ItemCode")
118         vaSpread1.Col = 1
120         vaSpread1.Text = rs("ItemName")
122         vaSpread1.Col = 2
124         vaSpread1.Text = rs("Price")
126         rs.MoveNext
128         vaSpread1.MaxRows = vaSpread1.MaxRows + 1
130         vaSpread1.Row = vaSpread1.Row + 1
            'vaSpread1.RowHeight(vaSpread1.Row) = 20
        Loop

132     vaSpread1.MaxRows = vaSpread1.DataRowCnt
        
        '<EhFooter>
        Exit Sub

cmdItems_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmdItems_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub


Private Sub MSComm1_OnComm()
        '<EhHeader>
        On Error GoTo MSComm1_OnComm_Err
        '</EhHeader>

        Do
100         CardNumber = CardNumber & MSComm1.Input

102         DoEvents
104     Loop Until InStr(CardNumber, Chr(13))

106     Debug.Print CardNumber
        
108     txtCardNumber.Text = Left(CardNumber, 14)
        
110     CardNumber = ""
    
        '<EhFooter>
        Exit Sub

MSComm1_OnComm_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.MSComm1_OnComm " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtCardNumber_Change()
        '<EhHeader>
        On Error GoTo txtCardNumber_Change_Err
        '</EhHeader>
    
100     Set rs = New ADODB.Recordset
102     sSQL = "Select * From MemberInformation Where CardNumber='" & txtCardNumber.Text & "'"
104     rs.Open sSQL, Conn, 1, 3
    
106     If rs.EOF = False Then
        
108         lblMemberCode.Caption = "Member Code: " & rs("MemberCode")
110         lblMemberName.Caption = "Member Name: " & rs("MemberName")
            
112         picEmployeePicture.DataField = "Picture"
114         Set picEmployeePicture.DataSource = rs
    
        Else
        
116         lblMemberCode.Caption = ""
118         lblMemberName.Caption = ""
120         picEmployeePicture.Picture = LoadPicture("")
        
        End If
    
        '<EhFooter>
        Exit Sub

txtCardNumber_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.txtCardNumber_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread1_ButtonClicked(ByVal Col As Long, _
                                    ByVal Row As Long, _
                                    ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread1_ButtonClicked_Err
        '</EhHeader>
        
100     vaSpread1.Row = vaSpread1.ActiveRow
102     vaSpread2.Row = vaSpread2.ActiveRow
104     vaSpread1.Col = 0
106     vaSpread2.Col = 1
108     vaSpread2.Text = vaSpread1.Text
110     vaSpread1.Col = 1
112     vaSpread2.Col = 2
114     vaSpread2.Text = vaSpread1.Text
116     vaSpread1.Col = 2
118     vaSpread2.Col = 3
120     vaSpread2.Text = vaSpread1.Text
122     vaSpread2.Col = 4
124     vaSpread2.Text = 1
126     vaSpread2.Row = vaSpread2.Row + 1
128     vaSpread2.Col = 1
130     vaSpread2.Action = ActionActiveCell
    
132     Call CalculateBill
    
        '<EhFooter>
        Exit Sub

vaSpread1_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.vaSpread1_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread2_ButtonClicked(ByVal Col As Long, _
                                    ByVal Row As Long, _
                                    ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread2_ButtonClicked_Err
        '</EhHeader>
    
100     vaSpread2.Row = vaSpread2.ActiveRow
102     vaSpread2.Action = ActionDeleteRow
        
        '<EhFooter>
        Exit Sub

vaSpread2_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.vaSpread2_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub CalculateBill()
        '<EhHeader>
        On Error GoTo CalculateBill_Err
        '</EhHeader>
    
        Dim TotalAmount As String
        Dim TotalTaxAmount As String

100     For i = 1 To vaSpread2.MaxRows
102         vaSpread2.Row = i
104         vaSpread2.Col = 8
106         TotalTaxAmount = Val(TotalTaxAmount) + Val(vaSpread2.Value & "")
108         vaSpread2.Col = 9
110         TotalAmount = Val(TotalAmount) + Val(vaSpread2.Value & "")
        Next

112     txtTotalAmount.Text = TotalAmount
         
114     If IsPercentage = True Then
116         txtDiscount.Text = Round((txtTotalAmount.Text * DiscountAmount) / 100, 0)
        Else
118         txtDiscount.Text = DiscountAmount
        End If

120     If DiscountAmount = "100" Then
122         txtSalesTaxAmount.Text = 0
        Else
124         txtSalesTaxAmount.Text = TotalTaxAmount
        End If

126     txtPayableAmount.Text = Val(txtTotalAmount.Value) - Val(txtDiscount.Value) + Val(txtSalesTaxAmount.Value)
    
        '<EhFooter>
        Exit Sub

CalculateBill_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.CalculateBill " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub FillcmbDiscount()
        '<EhHeader>
        On Error GoTo FillcmbDiscount_Err
        '</EhHeader>
    
100     sSQL = "Select * From DiscountSetup"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     cmbDiscount.Clear

108     Do Until rs.EOF
110         cmbDiscount.AddItem rs("DiscountDescription")
112         cmbDiscount.ItemData(cmbDiscount.NewIndex) = rs("DiscountCode")
114         rs.MoveNext
        Loop
    
116     cmbDiscount.Text = "No Discount"

        '<EhFooter>
        Exit Sub

FillcmbDiscount_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.FillcmbDiscount " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub POS_Lock(strStatus As Boolean)
        '<EhHeader>
        On Error GoTo POS_Lock_Err
        '</EhHeader>

100     strStatus = Not strStatus
        
102     cmdStart.Enabled = Not strStatus
        
108     cmdItems(0).Enabled = strStatus
110     cmdItems(1).Enabled = strStatus
112     cmdItems(2).Enabled = strStatus
114     cmdItems(3).Enabled = strStatus
116     cmdItems(4).Enabled = strStatus
118     cmdItems(5).Enabled = strStatus
120     cmdItems(6).Enabled = strStatus
122     cmdItems(7).Enabled = strStatus
        
124     vaSpread1.Enabled = strStatus
126     vaSpread2.Enabled = strStatus

128     If strStatus = True Then

130         vaSpread1.MaxRows = 0
132         vaSpread1.MaxRows = 25

134         vaSpread2.MaxRows = 0
136         vaSpread2.MaxRows = 20
        
        End If
        
138     cmdSendDelivery.Enabled = strStatus
140     cmbDiscount.Enabled = strStatus
    
        '<EhFooter>
        Exit Sub

POS_Lock_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.POS_Lock " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread2_LeaveCell(ByVal Col As Long, _
                                ByVal Row As Long, _
                                ByVal NewCol As Long, _
                                ByVal NewRow As Long, _
                                Cancel As Boolean)
        '<EhHeader>
        On Error GoTo vaSpread2_LeaveCell_Err
        '</EhHeader>
        
100     CalculateBill

        '<EhFooter>
        Exit Sub

vaSpread2_LeaveCell_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.vaSpread2_LeaveCell " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub LoadDeliveries()
        '<EhHeader>
        On Error GoTo LoadDeliveries_Err
        '</EhHeader>
           
100     sSQL = "SELECT DISTINCT TransactionNo, SUM(ItemPrice * ItemQuantity) AS ItemAmount, TaxAmount From Transactions " & _
           "WHERE TransactionDate='" & TransactionDate & "' AND TransactionType='Delivery' AND TransactionClose = 0 GROUP BY TransactionNo, TaxAmount"
        
102     Debug.Print sSQL
104     Set rs = New ADODB.Recordset
106     rs.Open sSQL, Conn, 1, 3
        
108     vaSpread3.MaxRows = 0
110     vaSpread3.MaxRows = 1
112     vaSpread3.Row = 1

114     Do Until rs.EOF
116         vaSpread3.Col = 1
118         vaSpread3.Text = rs("TransactionNo")
120         vaSpread3.Col = 2
122         vaSpread3.Text = Val(rs("ItemAmount")) + Val(rs("TaxAmount"))
124         vaSpread3.MaxRows = vaSpread3.MaxRows + 1
126         vaSpread3.Row = vaSpread3.Row + 1
128         rs.MoveNext
        Loop

130     vaSpread3.MaxRows = vaSpread3.DataRowCnt

132     sSQL = "SELECT DISTINCT TransactionNo, SUM(ItemPrice * ItemQuantity) AS ItemAmount, TaxAmount From Transactions " & _
           "WHERE TransactionDate='" & TransactionDate & "' AND TransactionType='Dine In' AND TransactionClose = 0 GROUP BY TransactionNo, TaxAmount"
        
134     Debug.Print sSQL
136     Set rs = New ADODB.Recordset
138     rs.Open sSQL, Conn, 1, 3
        
140     vaSpread4.MaxRows = 0
142     vaSpread4.MaxRows = 1
144     vaSpread4.Row = 1

146     Do Until rs.EOF
148         vaSpread4.Col = 1
150         vaSpread4.Text = rs("TransactionNo")
152         vaSpread4.Col = 2
154         vaSpread4.Text = Val(rs("ItemAmount")) + Val(rs("TaxAmount"))
156         vaSpread4.MaxRows = vaSpread4.MaxRows + 1
158         vaSpread4.Row = vaSpread4.Row + 1
160         rs.MoveNext
        Loop

162     vaSpread4.MaxRows = vaSpread4.DataRowCnt

        '<EhFooter>
        Exit Sub

LoadDeliveries_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.LoadDeliveries " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread3_ButtonClicked(ByVal Col As Long, _
                                    ByVal Row As Long, _
                                    ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread3_ButtonClicked_Err
        '</EhHeader>
        
        Dim TransactionEnd As String

100     vaSpread3.Row = vaSpread3.ActiveRow
102     vaSpread3.Col = 1
104     TransactionNo = vaSpread3.Text
106     TransactionEnd = Time
        
108     Printer.FontName = "FontControl"
110     Printer.Print "A"
112     Printer.EndDoc
        
114     sSQL = "UPDATE Transactions SET TransactionEndTime='" & TransactionEnd & "', TransactionClose=1 WHERE (TransactionNo='" & TransactionNo & "') AND (TransactionDate='" & TransactionDate & "')"
116     Conn.Execute sSQL
        
118     LoadDeliveries

        '<EhFooter>
        Exit Sub

vaSpread3_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.vaSpread3_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread4_ButtonClicked(ByVal Col As Long, _
                                    ByVal Row As Long, _
                                    ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread4_ButtonClicked_Err
        '</EhHeader>
        
        Dim TransactionEnd As String

100     vaSpread4.Row = vaSpread4.ActiveRow
102     vaSpread4.Col = 1
104     TransactionNo = vaSpread4.Text
106     TransactionEnd = Time
        
108     For i = 1 To DockItPrints
        
110         sSQL = "SELECT Transactions.*, ItemPrice * ItemQuantity AS ItemTotal From Transactions " & _
               "WHERE TransactionNo='" & TransactionNo & "' AND TransactionDate='" & TransactionDate & "'"
112         Set rs = New ADODB.Recordset
114         rs.Open sSQL, Conn, 1, 3
        
116         With repPrintDockit
118             .Restart
120             .documentName = "Dockit"
122             .DataControl1.Recordset = rs
124             .PrintReport False
            End With

        Next
        
126     Printer.FontName = "FontControl"
128     Printer.Print "A"
130     Printer.EndDoc
        
132     sSQL = "UPDATE Transactions SET TransactionEndTime='" & TransactionEnd & "', TransactionClose=1 WHERE (TransactionNo='" & TransactionNo & "') AND (TransactionDate='" & TransactionDate & "')"
134     Conn.Execute sSQL
        
136     LoadDeliveries

        '<EhFooter>
        Exit Sub

vaSpread4_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.vaSpread4_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
