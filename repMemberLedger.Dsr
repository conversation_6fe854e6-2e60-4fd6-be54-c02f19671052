VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} repMemberLedger 
   Caption         =   "Point_of_Sale_System - repMemberLedger (ActiveReport)"
   ClientHeight    =   10620
   ClientLeft      =   0
   ClientTop       =   390
   ClientWidth     =   15360
   _ExtentX        =   27093
   _ExtentY        =   18733
   SectionData     =   "repMemberLedger.dsx":0000
End
Attribute VB_Name = "repMemberLedger"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
Dim BalanceAmount As String

Private Sub TransactionNoFooter_Format()
        '<EhHeader>
        On Error GoTo TransactionNoFooter_Format_Err
        '</EhHeader>
    
100     If Val(txtChargesAmountTotal.DataValue) = 0 Then
102         txtChargesAmountTotal.Text = ""
104         BalanceAmount = Val(BalanceAmount) + Val(txtChargesAmountTotal.DataValue) - Val(txtPaidAmountTotal.DataValue)
        End If
    
106     If Val(txtPaidAmountTotal.DataValue) = 0 Then
108         txtPaidAmountTotal.Text = ""

110         If txtCheckBounced.Text = -1 Then
112             BalanceAmount = Val(BalanceAmount) + Val(txtChargesAmountTotal.DataValue) - Val(txtPaidAmountTotal.DataValue)
            Else
114             BalanceAmount = Val(txtChargesAmountTotal.DataValue) - Val(txtPaidAmountTotal.DataValue)
            End If
        End If
    
116     txtBalanceAmount.DataValue = Val(BalanceAmount)
    
        '<EhFooter>
        Exit Sub

TransactionNoFooter_Format_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.repMemberLedger.TransactionNoFooter_Format " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
