VERSION 5.00
Object = "{CDF3B183-D408-11CE-AE2C-0080C786E37D}#3.0#0"; "Edt32x30.ocx"
Object = "{B02F3647-766B-11CE-AF28-C3A2FBE76A13}#3.0#0"; "SPR32X30.ocx"
Begin VB.Form frmPointOfSale 
   Appearance      =   0  'Flat
   BackColor       =   &H00EEEEEE&
   Caption         =   "Point of Sale (by Intelysol)"
   ClientHeight    =   13110
   ClientLeft      =   5175
   ClientTop       =   750
   ClientWidth     =   17955
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmPointOfSale.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   PaletteMode     =   2  'Custom
   ScaleHeight     =   13110
   ScaleWidth      =   17955
   Begin VB.Timer Timer1 
      Enabled         =   0   'False
      Interval        =   5000
      Left            =   690
      Top             =   5250
   End
   Begin VB.PictureBox picMain 
      Appearance      =   0  'Flat
      BackColor       =   &H80000005&
      ForeColor       =   &H80000008&
      Height          =   11370
      Left            =   1245
      ScaleHeight     =   11340
      ScaleWidth      =   15630
      TabIndex        =   0
      Top             =   1200
      Width           =   15660
      Begin VB.ComboBox cmbMonths 
         Height          =   315
         Left            =   2340
         Style           =   2  'Dropdown List
         TabIndex        =   51
         Top             =   330
         Visible         =   0   'False
         Width           =   1200
      End
      Begin VB.TextBox txtComments 
         Appearance      =   0  'Flat
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   660
         Left            =   135
         MultiLine       =   -1  'True
         ScrollBars      =   2  'Vertical
         TabIndex        =   49
         Text            =   "frmPointOfSale.frx":000C
         Top             =   8730
         Width           =   7485
      End
      Begin VB.PictureBox picMaisonSonraj 
         Appearance      =   0  'Flat
         AutoRedraw      =   -1  'True
         BackColor       =   &H80000005&
         BorderStyle     =   0  'None
         DrawStyle       =   2  'Dot
         ForeColor       =   &H80000008&
         Height          =   1065
         Left            =   4575
         ScaleHeight     =   18.785
         ScaleMode       =   6  'Millimeter
         ScaleWidth      =   106.363
         TabIndex        =   44
         TabStop         =   0   'False
         Top             =   30
         Width           =   6030
         Begin VB.Image Image1 
            Height          =   960
            Left            =   0
            Picture         =   "frmPointOfSale.frx":0015
            Stretch         =   -1  'True
            Top             =   15
            Width           =   6045
         End
      End
      Begin VB.ComboBox cmbInvoiceList 
         Height          =   315
         Left            =   2325
         Style           =   2  'Dropdown List
         TabIndex        =   43
         Top             =   690
         Visible         =   0   'False
         Width           =   3165
      End
      Begin VB.PictureBox picCollectables 
         Appearance      =   0  'Flat
         AutoRedraw      =   -1  'True
         BackColor       =   &H80000005&
         ForeColor       =   &H80000008&
         Height          =   1065
         Left            =   4575
         Picture         =   "frmPointOfSale.frx":3834
         ScaleHeight     =   1035
         ScaleWidth      =   3435
         TabIndex        =   42
         TabStop         =   0   'False
         Top             =   30
         Width           =   3465
      End
      Begin VB.TextBox txtFBRInvoiceNo 
         Appearance      =   0  'Flat
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   330
         Left            =   8985
         TabIndex        =   7
         Text            =   "Customer"
         Top             =   2475
         Width           =   2940
      End
      Begin VB.TextBox txtContactNo 
         Appearance      =   0  'Flat
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   330
         Left            =   8985
         TabIndex        =   5
         Text            =   "Contact No"
         Top             =   1755
         Width           =   2985
      End
      Begin VB.PictureBox picAMPM 
         Appearance      =   0  'Flat
         AutoRedraw      =   -1  'True
         BackColor       =   &H80000005&
         ForeColor       =   &H80000008&
         Height          =   1065
         Left            =   4800
         Picture         =   "frmPointOfSale.frx":49E2
         ScaleHeight     =   1035
         ScaleWidth      =   2640
         TabIndex        =   15
         TabStop         =   0   'False
         Top             =   30
         Width           =   2670
      End
      Begin VB.PictureBox Picture2 
         Appearance      =   0  'Flat
         AutoSize        =   -1  'True
         BackColor       =   &H80000005&
         ForeColor       =   &H80000008&
         Height          =   1065
         Left            =   11925
         Picture         =   "frmPointOfSale.frx":57EF
         ScaleHeight     =   1035
         ScaleWidth      =   3465
         TabIndex        =   14
         Top             =   30
         Width           =   3495
      End
      Begin VB.ComboBox cmbPaymentType 
         Height          =   315
         ItemData        =   "frmPointOfSale.frx":5EDB
         Left            =   135
         List            =   "frmPointOfSale.frx":5EE8
         Style           =   2  'Dropdown List
         TabIndex        =   13
         Top             =   9840
         Width           =   2625
      End
      Begin VB.PictureBox Picture3 
         Appearance      =   0  'Flat
         BackColor       =   &H80000005&
         Enabled         =   0   'False
         ForeColor       =   &H80000008&
         Height          =   450
         Left            =   12705
         ScaleHeight     =   420
         ScaleWidth      =   2535
         TabIndex        =   11
         Top             =   8775
         Width           =   2565
         Begin EditLib.fpCurrency txtPayableAmount 
            Height          =   555
            Left            =   -15
            TabIndex        =   12
            Top             =   -15
            Width           =   2565
            _Version        =   196608
            _ExtentX        =   4524
            _ExtentY        =   979
            Enabled         =   -1  'True
            MousePointer    =   0
            Object.TabStop         =   -1  'True
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Consolas"
               Size            =   15.75
               Charset         =   0
               Weight          =   700
               Underline       =   0   'False
               Italic          =   -1  'True
               Strikethrough   =   0   'False
            EndProperty
            BackColor       =   11856344
            ForeColor       =   -2147483640
            ThreeDInsideStyle=   0
            ThreeDInsideHighlightColor=   -2147483633
            ThreeDInsideShadowColor=   -2147483642
            ThreeDInsideWidth=   1
            ThreeDOutsideStyle=   0
            ThreeDOutsideHighlightColor=   -2147483628
            ThreeDOutsideShadowColor=   -2147483632
            ThreeDOutsideWidth=   1
            ThreeDFrameWidth=   0
            BorderStyle     =   1
            BorderColor     =   -2147483642
            BorderWidth     =   1
            ButtonDisable   =   0   'False
            ButtonHide      =   0   'False
            ButtonIncrement =   1
            ButtonMin       =   0
            ButtonMax       =   100
            ButtonStyle     =   0
            ButtonWidth     =   0
            ButtonWrap      =   -1  'True
            ButtonDefaultAction=   -1  'True
            ThreeDText      =   0
            ThreeDTextHighlightColor=   -2147483633
            ThreeDTextShadowColor=   -2147483632
            ThreeDTextOffset=   1
            AlignTextH      =   2
            AlignTextV      =   0
            AllowNull       =   0   'False
            NoSpecialKeys   =   0
            AutoAdvance     =   0   'False
            AutoBeep        =   0   'False
            CaretInsert     =   0
            CaretOverWrite  =   3
            UserEntry       =   0
            HideSelection   =   -1  'True
            InvalidColor    =   -2147483637
            InvalidOption   =   0
            MarginLeft      =   3
            MarginTop       =   3
            MarginRight     =   3
            MarginBottom    =   3
            NullColor       =   -2147483637
            OnFocusAlignH   =   0
            OnFocusAlignV   =   0
            OnFocusNoSelect =   0   'False
            OnFocusPosition =   0
            ControlType     =   0
            Text            =   "Rs:0.00"
            CurrencyDecimalPlaces=   -1
            CurrencyNegFormat=   0
            CurrencyPlacement=   0
            CurrencySymbol  =   ""
            DecimalPoint    =   ""
            FixedPoint      =   -1  'True
            LeadZero        =   0
            MaxValue        =   "9000000000"
            MinValue        =   "-9000000000"
            NegToggle       =   0   'False
            Separator       =   ""
            UseSeparator    =   0   'False
            IncInt          =   1
            IncDec          =   1
            BorderGrayAreaColor=   -2147483637
            ThreeDOnFocusInvert=   0   'False
            ThreeDFrameColor=   -2147483633
            Appearance      =   1
            BorderDropShadow=   0
            BorderDropShadowColor=   -2147483632
            BorderDropShadowWidth=   3
            ButtonColor     =   -2147483633
            AutoMenu        =   0   'False
            ButtonAlign     =   0
            OLEDropMode     =   0
            OLEDragMode     =   0
         End
      End
      Begin VB.ComboBox cmbEmployees 
         Height          =   315
         ItemData        =   "frmPointOfSale.frx":5F0B
         Left            =   2850
         List            =   "frmPointOfSale.frx":5F0D
         Style           =   2  'Dropdown List
         TabIndex        =   10
         Top             =   9840
         Width           =   2625
      End
      Begin VB.TextBox txtTransactionNo 
         Appearance      =   0  'Flat
         Height          =   360
         Left            =   9960
         TabIndex        =   9
         Top             =   435
         Visible         =   0   'False
         Width           =   1305
      End
      Begin VB.ComboBox cmbBrands 
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   345
         ItemData        =   "frmPointOfSale.frx":5F0F
         Left            =   1125
         List            =   "frmPointOfSale.frx":5F19
         Style           =   2  'Dropdown List
         TabIndex        =   2
         Top             =   1260
         Width           =   3960
      End
      Begin VB.TextBox txtSearchItem 
         Appearance      =   0  'Flat
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   330
         Left            =   1125
         TabIndex        =   3
         Text            =   "SearchItem"
         Top             =   1725
         Width           =   3960
      End
      Begin VB.TextBox txtCustomer 
         Appearance      =   0  'Flat
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   330
         Left            =   5250
         TabIndex        =   4
         Text            =   "Customer"
         Top             =   1755
         Width           =   3570
      End
      Begin VB.TextBox txtSalesTaxNumber 
         Appearance      =   0  'Flat
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   330
         Left            =   5250
         TabIndex        =   6
         Text            =   "Sales Tax"
         Top             =   2475
         Width           =   3570
      End
      Begin VB.TextBox txtRegistrationAddress 
         Appearance      =   0  'Flat
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   330
         Left            =   5250
         TabIndex        =   8
         Text            =   "Registration Address"
         Top             =   3165
         Width           =   7665
      End
      Begin EditLib.fpCurrency txtAmountExcludingTaxes 
         Height          =   375
         Left            =   10830
         TabIndex        =   18
         Top             =   7530
         Width           =   1830
         _Version        =   196608
         _ExtentX        =   3228
         _ExtentY        =   661
         Enabled         =   0   'False
         MousePointer    =   0
         Object.TabStop         =   -1  'True
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   11.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BackColor       =   11856344
         ForeColor       =   -2147483640
         ThreeDInsideStyle=   0
         ThreeDInsideHighlightColor=   -2147483633
         ThreeDInsideShadowColor=   -2147483642
         ThreeDInsideWidth=   1
         ThreeDOutsideStyle=   0
         ThreeDOutsideHighlightColor=   -2147483628
         ThreeDOutsideShadowColor=   -2147483632
         ThreeDOutsideWidth=   1
         ThreeDFrameWidth=   0
         BorderStyle     =   1
         BorderColor     =   -2147483642
         BorderWidth     =   1
         ButtonDisable   =   0   'False
         ButtonHide      =   0   'False
         ButtonIncrement =   1
         ButtonMin       =   0
         ButtonMax       =   100
         ButtonStyle     =   0
         ButtonWidth     =   0
         ButtonWrap      =   -1  'True
         ButtonDefaultAction=   -1  'True
         ThreeDText      =   0
         ThreeDTextHighlightColor=   -2147483633
         ThreeDTextShadowColor=   -2147483632
         ThreeDTextOffset=   1
         AlignTextH      =   2
         AlignTextV      =   0
         AllowNull       =   0   'False
         NoSpecialKeys   =   0
         AutoAdvance     =   0   'False
         AutoBeep        =   0   'False
         CaretInsert     =   0
         CaretOverWrite  =   3
         UserEntry       =   0
         HideSelection   =   -1  'True
         InvalidColor    =   -2147483637
         InvalidOption   =   0
         MarginLeft      =   3
         MarginTop       =   3
         MarginRight     =   3
         MarginBottom    =   3
         NullColor       =   -2147483637
         OnFocusAlignH   =   0
         OnFocusAlignV   =   0
         OnFocusNoSelect =   0   'False
         OnFocusPosition =   0
         ControlType     =   0
         Text            =   "Rs:0.00"
         CurrencyDecimalPlaces=   -1
         CurrencyNegFormat=   0
         CurrencyPlacement=   0
         CurrencySymbol  =   ""
         DecimalPoint    =   ""
         FixedPoint      =   -1  'True
         LeadZero        =   0
         MaxValue        =   "9000000000"
         MinValue        =   "-9000000000"
         NegToggle       =   0   'False
         Separator       =   ""
         UseSeparator    =   0   'False
         IncInt          =   1
         IncDec          =   1
         BorderGrayAreaColor=   -2147483637
         ThreeDOnFocusInvert=   0   'False
         ThreeDFrameColor=   -2147483633
         Appearance      =   1
         BorderDropShadow=   0
         BorderDropShadowColor=   -2147483632
         BorderDropShadowWidth=   3
         ButtonColor     =   -2147483633
         AutoMenu        =   0   'False
         ButtonAlign     =   0
         OLEDropMode     =   0
         OLEDragMode     =   0
      End
      Begin Point_of_Sale_System.lvButtons_H cmdStart 
         Height          =   840
         Left            =   105
         TabIndex        =   1
         Top             =   120
         Width           =   1860
         _ExtentX        =   3281
         _ExtentY        =   1482
         Caption         =   "Start"
         CapAlign        =   2
         BackStyle       =   2
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   18
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Mode            =   0
         Value           =   0   'False
         ImgAlign        =   2
         Image           =   "frmPointOfSale.frx":5F30
         ImgSize         =   32
         cBack           =   -2147483633
      End
      Begin Point_of_Sale_System.ucStatusBar UcSStatText 
         Height          =   330
         Left            =   60
         TabIndex        =   16
         Top             =   12090
         Width           =   15375
         _ExtentX        =   27120
         _ExtentY        =   582
         Text            =   " Point of Sale-={by www.intelysol.com"
      End
      Begin FPSpread.vaSpread vaSpread2 
         Height          =   3750
         Left            =   5250
         TabIndex        =   17
         Top             =   3735
         Width           =   8355
         _Version        =   196608
         _ExtentX        =   14737
         _ExtentY        =   6615
         _StockProps     =   64
         AllowDragDrop   =   -1  'True
         AllowMultiBlocks=   -1  'True
         AllowUserFormulas=   -1  'True
         EditEnterAction =   2
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         MaxCols         =   10
         MaxRows         =   20
         Protect         =   0   'False
         SpreadDesigner  =   "frmPointOfSale.frx":BDA2
      End
      Begin Point_of_Sale_System.lvButtons_H cmdClose 
         Height          =   840
         Left            =   13545
         TabIndex        =   25
         Top             =   10425
         Width           =   1860
         _ExtentX        =   3281
         _ExtentY        =   1482
         Caption         =   "Close"
         CapAlign        =   2
         BackStyle       =   2
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   18
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         cGradient       =   0
         Mode            =   0
         Value           =   0   'False
         cBack           =   -2147483633
      End
      Begin Point_of_Sale_System.lvButtons_H cmdPayments 
         Height          =   840
         Left            =   105
         TabIndex        =   24
         Top             =   10425
         Width           =   2415
         _ExtentX        =   4260
         _ExtentY        =   1482
         Caption         =   "Payments"
         CapAlign        =   2
         BackStyle       =   2
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   18
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Mode            =   0
         Value           =   0   'False
         ImgAlign        =   2
         Image           =   "frmPointOfSale.frx":C4FB
         ImgSize         =   32
         cBack           =   -2147483633
      End
      Begin EditLib.fpCurrency txtSalesTaxAmount 
         Height          =   375
         Left            =   10830
         TabIndex        =   19
         Top             =   7950
         Width           =   1830
         _Version        =   196608
         _ExtentX        =   3228
         _ExtentY        =   661
         Enabled         =   0   'False
         MousePointer    =   0
         Object.TabStop         =   -1  'True
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   11.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BackColor       =   11856344
         ForeColor       =   -2147483640
         ThreeDInsideStyle=   0
         ThreeDInsideHighlightColor=   -2147483633
         ThreeDInsideShadowColor=   -2147483642
         ThreeDInsideWidth=   1
         ThreeDOutsideStyle=   0
         ThreeDOutsideHighlightColor=   -2147483628
         ThreeDOutsideShadowColor=   -2147483632
         ThreeDOutsideWidth=   1
         ThreeDFrameWidth=   0
         BorderStyle     =   1
         BorderColor     =   -2147483642
         BorderWidth     =   1
         ButtonDisable   =   0   'False
         ButtonHide      =   0   'False
         ButtonIncrement =   1
         ButtonMin       =   0
         ButtonMax       =   100
         ButtonStyle     =   0
         ButtonWidth     =   0
         ButtonWrap      =   -1  'True
         ButtonDefaultAction=   -1  'True
         ThreeDText      =   0
         ThreeDTextHighlightColor=   -2147483633
         ThreeDTextShadowColor=   -2147483632
         ThreeDTextOffset=   1
         AlignTextH      =   2
         AlignTextV      =   0
         AllowNull       =   0   'False
         NoSpecialKeys   =   0
         AutoAdvance     =   0   'False
         AutoBeep        =   0   'False
         CaretInsert     =   0
         CaretOverWrite  =   3
         UserEntry       =   0
         HideSelection   =   -1  'True
         InvalidColor    =   -2147483637
         InvalidOption   =   0
         MarginLeft      =   3
         MarginTop       =   3
         MarginRight     =   3
         MarginBottom    =   3
         NullColor       =   -2147483637
         OnFocusAlignH   =   0
         OnFocusAlignV   =   0
         OnFocusNoSelect =   0   'False
         OnFocusPosition =   0
         ControlType     =   0
         Text            =   "Rs:0.00"
         CurrencyDecimalPlaces=   -1
         CurrencyNegFormat=   0
         CurrencyPlacement=   0
         CurrencySymbol  =   ""
         DecimalPoint    =   ""
         FixedPoint      =   -1  'True
         LeadZero        =   0
         MaxValue        =   "9000000000"
         MinValue        =   "-9000000000"
         NegToggle       =   -1  'True
         Separator       =   ""
         UseSeparator    =   0   'False
         IncInt          =   1
         IncDec          =   1
         BorderGrayAreaColor=   -2147483637
         ThreeDOnFocusInvert=   0   'False
         ThreeDFrameColor=   -2147483633
         Appearance      =   1
         BorderDropShadow=   0
         BorderDropShadowColor=   -2147483632
         BorderDropShadowWidth=   3
         ButtonColor     =   -2147483633
         AutoMenu        =   0   'False
         ButtonAlign     =   0
         OLEDropMode     =   0
         OLEDragMode     =   0
      End
      Begin EditLib.fpCurrency txtCustomerAmount 
         Height          =   450
         Left            =   12705
         TabIndex        =   20
         Top             =   9285
         Width           =   2565
         _Version        =   196608
         _ExtentX        =   4524
         _ExtentY        =   794
         Enabled         =   -1  'True
         MousePointer    =   0
         Object.TabStop         =   -1  'True
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   15.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         BackColor       =   11856344
         ForeColor       =   -2147483640
         ThreeDInsideStyle=   0
         ThreeDInsideHighlightColor=   -2147483633
         ThreeDInsideShadowColor=   -2147483642
         ThreeDInsideWidth=   1
         ThreeDOutsideStyle=   0
         ThreeDOutsideHighlightColor=   -2147483628
         ThreeDOutsideShadowColor=   -2147483632
         ThreeDOutsideWidth=   1
         ThreeDFrameWidth=   0
         BorderStyle     =   1
         BorderColor     =   -2147483642
         BorderWidth     =   1
         ButtonDisable   =   0   'False
         ButtonHide      =   0   'False
         ButtonIncrement =   1
         ButtonMin       =   0
         ButtonMax       =   100
         ButtonStyle     =   0
         ButtonWidth     =   0
         ButtonWrap      =   -1  'True
         ButtonDefaultAction=   -1  'True
         ThreeDText      =   0
         ThreeDTextHighlightColor=   -2147483633
         ThreeDTextShadowColor=   -2147483632
         ThreeDTextOffset=   1
         AlignTextH      =   2
         AlignTextV      =   0
         AllowNull       =   0   'False
         NoSpecialKeys   =   0
         AutoAdvance     =   0   'False
         AutoBeep        =   0   'False
         CaretInsert     =   0
         CaretOverWrite  =   3
         UserEntry       =   0
         HideSelection   =   -1  'True
         InvalidColor    =   -2147483637
         InvalidOption   =   0
         MarginLeft      =   3
         MarginTop       =   3
         MarginRight     =   3
         MarginBottom    =   3
         NullColor       =   -2147483637
         OnFocusAlignH   =   0
         OnFocusAlignV   =   0
         OnFocusNoSelect =   0   'False
         OnFocusPosition =   0
         ControlType     =   0
         Text            =   "Rs:0.00"
         CurrencyDecimalPlaces=   -1
         CurrencyNegFormat=   0
         CurrencyPlacement=   0
         CurrencySymbol  =   ""
         DecimalPoint    =   ""
         FixedPoint      =   -1  'True
         LeadZero        =   0
         MaxValue        =   "9000000000"
         MinValue        =   "-9000000000"
         NegToggle       =   0   'False
         Separator       =   ""
         UseSeparator    =   0   'False
         IncInt          =   1
         IncDec          =   1
         BorderGrayAreaColor=   -2147483637
         ThreeDOnFocusInvert=   0   'False
         ThreeDFrameColor=   -2147483633
         Appearance      =   1
         BorderDropShadow=   0
         BorderDropShadowColor=   -2147483632
         BorderDropShadowWidth=   3
         ButtonColor     =   -2147483633
         AutoMenu        =   0   'False
         ButtonAlign     =   0
         OLEDropMode     =   0
         OLEDragMode     =   0
      End
      Begin EditLib.fpCurrency txtChangeAmount 
         Height          =   450
         Left            =   12705
         TabIndex        =   21
         Top             =   9795
         Width           =   2565
         _Version        =   196608
         _ExtentX        =   4524
         _ExtentY        =   794
         Enabled         =   -1  'True
         MousePointer    =   0
         Object.TabStop         =   -1  'True
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   15.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         BackColor       =   11856344
         ForeColor       =   -2147483640
         ThreeDInsideStyle=   0
         ThreeDInsideHighlightColor=   -2147483633
         ThreeDInsideShadowColor=   -2147483642
         ThreeDInsideWidth=   1
         ThreeDOutsideStyle=   0
         ThreeDOutsideHighlightColor=   -2147483628
         ThreeDOutsideShadowColor=   -2147483632
         ThreeDOutsideWidth=   1
         ThreeDFrameWidth=   0
         BorderStyle     =   1
         BorderColor     =   -2147483642
         BorderWidth     =   1
         ButtonDisable   =   0   'False
         ButtonHide      =   0   'False
         ButtonIncrement =   1
         ButtonMin       =   0
         ButtonMax       =   100
         ButtonStyle     =   0
         ButtonWidth     =   0
         ButtonWrap      =   -1  'True
         ButtonDefaultAction=   -1  'True
         ThreeDText      =   0
         ThreeDTextHighlightColor=   -2147483633
         ThreeDTextShadowColor=   -2147483632
         ThreeDTextOffset=   1
         AlignTextH      =   2
         AlignTextV      =   0
         AllowNull       =   0   'False
         NoSpecialKeys   =   0
         AutoAdvance     =   0   'False
         AutoBeep        =   0   'False
         CaretInsert     =   0
         CaretOverWrite  =   3
         UserEntry       =   0
         HideSelection   =   -1  'True
         InvalidColor    =   -2147483637
         InvalidOption   =   0
         MarginLeft      =   3
         MarginTop       =   3
         MarginRight     =   3
         MarginBottom    =   3
         NullColor       =   -2147483637
         OnFocusAlignH   =   0
         OnFocusAlignV   =   0
         OnFocusNoSelect =   0   'False
         OnFocusPosition =   0
         ControlType     =   0
         Text            =   "Rs:0.00"
         CurrencyDecimalPlaces=   -1
         CurrencyNegFormat=   0
         CurrencyPlacement=   0
         CurrencySymbol  =   ""
         DecimalPoint    =   ""
         FixedPoint      =   -1  'True
         LeadZero        =   0
         MaxValue        =   "9000000000"
         MinValue        =   "-9000000000"
         NegToggle       =   0   'False
         Separator       =   ""
         UseSeparator    =   0   'False
         IncInt          =   1
         IncDec          =   1
         BorderGrayAreaColor=   -2147483637
         ThreeDOnFocusInvert=   0   'False
         ThreeDFrameColor=   -2147483633
         Appearance      =   1
         BorderDropShadow=   0
         BorderDropShadowColor=   -2147483632
         BorderDropShadowWidth=   3
         ButtonColor     =   -2147483633
         AutoMenu        =   0   'False
         ButtonAlign     =   0
         OLEDropMode     =   0
         OLEDragMode     =   0
      End
      Begin FPSpread.vaSpread vaSpread1 
         Height          =   6045
         Left            =   135
         TabIndex        =   22
         Top             =   2175
         Width           =   4950
         _Version        =   196608
         _ExtentX        =   8731
         _ExtentY        =   10663
         _StockProps     =   64
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         MaxCols         =   4
         MaxRows         =   20
         ScrollBars      =   2
         SpreadDesigner  =   "frmPointOfSale.frx":1211D
      End
      Begin VB.Frame Frame2 
         BackColor       =   &H0080C0FF&
         Height          =   45
         Left            =   0
         TabIndex        =   27
         Top             =   1125
         Width           =   15810
      End
      Begin VB.Frame Frame1 
         BackColor       =   &H0080C0FF&
         Height          =   60
         Left            =   0
         TabIndex        =   26
         Top             =   10290
         Width           =   15585
      End
      Begin Point_of_Sale_System.lvButtons_H btnSendFBR 
         Height          =   750
         Left            =   13290
         TabIndex        =   23
         Top             =   1185
         Width           =   2070
         _ExtentX        =   3651
         _ExtentY        =   1323
         Caption         =   "Get FBR Invoice"
         CapAlign        =   2
         BackStyle       =   2
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   12
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Mode            =   0
         Value           =   0   'False
         ImgAlign        =   2
         ImgSize         =   32
         cBack           =   -2147483633
      End
      Begin EditLib.fpCurrency txtDiscount 
         Height          =   375
         Left            =   10965
         TabIndex        =   45
         Top             =   8370
         Visible         =   0   'False
         Width           =   1695
         _Version        =   196608
         _ExtentX        =   2990
         _ExtentY        =   661
         Enabled         =   -1  'True
         MousePointer    =   0
         Object.TabStop         =   -1  'True
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   11.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BackColor       =   11856344
         ForeColor       =   -2147483640
         ThreeDInsideStyle=   0
         ThreeDInsideHighlightColor=   -2147483633
         ThreeDInsideShadowColor=   -2147483642
         ThreeDInsideWidth=   1
         ThreeDOutsideStyle=   0
         ThreeDOutsideHighlightColor=   -2147483628
         ThreeDOutsideShadowColor=   -2147483632
         ThreeDOutsideWidth=   1
         ThreeDFrameWidth=   0
         BorderStyle     =   1
         BorderColor     =   -2147483642
         BorderWidth     =   1
         ButtonDisable   =   0   'False
         ButtonHide      =   0   'False
         ButtonIncrement =   1
         ButtonMin       =   0
         ButtonMax       =   100
         ButtonStyle     =   0
         ButtonWidth     =   0
         ButtonWrap      =   -1  'True
         ButtonDefaultAction=   -1  'True
         ThreeDText      =   0
         ThreeDTextHighlightColor=   -2147483633
         ThreeDTextShadowColor=   -2147483632
         ThreeDTextOffset=   1
         AlignTextH      =   2
         AlignTextV      =   0
         AllowNull       =   0   'False
         NoSpecialKeys   =   0
         AutoAdvance     =   0   'False
         AutoBeep        =   0   'False
         CaretInsert     =   0
         CaretOverWrite  =   3
         UserEntry       =   0
         HideSelection   =   -1  'True
         InvalidColor    =   -2147483637
         InvalidOption   =   0
         MarginLeft      =   3
         MarginTop       =   3
         MarginRight     =   3
         MarginBottom    =   3
         NullColor       =   -2147483637
         OnFocusAlignH   =   0
         OnFocusAlignV   =   0
         OnFocusNoSelect =   0   'False
         OnFocusPosition =   0
         ControlType     =   0
         Text            =   "Rs:0.00"
         CurrencyDecimalPlaces=   -1
         CurrencyNegFormat=   0
         CurrencyPlacement=   0
         CurrencySymbol  =   ""
         DecimalPoint    =   ""
         FixedPoint      =   -1  'True
         LeadZero        =   0
         MaxValue        =   "9000000000"
         MinValue        =   "-9000000000"
         NegToggle       =   -1  'True
         Separator       =   ""
         UseSeparator    =   0   'False
         IncInt          =   1
         IncDec          =   1
         BorderGrayAreaColor=   -2147483637
         ThreeDOnFocusInvert=   0   'False
         ThreeDFrameColor=   -2147483633
         Appearance      =   1
         BorderDropShadow=   0
         BorderDropShadowColor=   -2147483632
         BorderDropShadowWidth=   3
         ButtonColor     =   -2147483633
         AutoMenu        =   0   'False
         ButtonAlign     =   0
         OLEDropMode     =   0
         OLEDragMode     =   0
      End
      Begin EditLib.fpCurrency txtDiscPercentage 
         Height          =   375
         Left            =   10125
         TabIndex        =   47
         Top             =   8370
         Visible         =   0   'False
         Width           =   540
         _Version        =   196608
         _ExtentX        =   952
         _ExtentY        =   661
         Enabled         =   -1  'True
         MousePointer    =   0
         Object.TabStop         =   -1  'True
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   11.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BackColor       =   11856344
         ForeColor       =   -2147483640
         ThreeDInsideStyle=   0
         ThreeDInsideHighlightColor=   -2147483633
         ThreeDInsideShadowColor=   -2147483642
         ThreeDInsideWidth=   1
         ThreeDOutsideStyle=   0
         ThreeDOutsideHighlightColor=   -2147483628
         ThreeDOutsideShadowColor=   -2147483632
         ThreeDOutsideWidth=   1
         ThreeDFrameWidth=   0
         BorderStyle     =   1
         BorderColor     =   -2147483642
         BorderWidth     =   1
         ButtonDisable   =   0   'False
         ButtonHide      =   0   'False
         ButtonIncrement =   1
         ButtonMin       =   0
         ButtonMax       =   100
         ButtonStyle     =   0
         ButtonWidth     =   0
         ButtonWrap      =   -1  'True
         ButtonDefaultAction=   -1  'True
         ThreeDText      =   0
         ThreeDTextHighlightColor=   -2147483633
         ThreeDTextShadowColor=   -2147483632
         ThreeDTextOffset=   1
         AlignTextH      =   2
         AlignTextV      =   0
         AllowNull       =   0   'False
         NoSpecialKeys   =   0
         AutoAdvance     =   0   'False
         AutoBeep        =   0   'False
         CaretInsert     =   0
         CaretOverWrite  =   3
         UserEntry       =   0
         HideSelection   =   -1  'True
         InvalidColor    =   -2147483637
         InvalidOption   =   0
         MarginLeft      =   3
         MarginTop       =   3
         MarginRight     =   3
         MarginBottom    =   3
         NullColor       =   -2147483637
         OnFocusAlignH   =   0
         OnFocusAlignV   =   0
         OnFocusNoSelect =   0   'False
         OnFocusPosition =   0
         ControlType     =   0
         Text            =   " 0"
         CurrencyDecimalPlaces=   0
         CurrencyNegFormat=   0
         CurrencyPlacement=   0
         CurrencySymbol  =   " "
         DecimalPoint    =   ""
         FixedPoint      =   -1  'True
         LeadZero        =   0
         MaxValue        =   "9000000000"
         MinValue        =   "-9000000000"
         NegToggle       =   -1  'True
         Separator       =   ""
         UseSeparator    =   0   'False
         IncInt          =   1
         IncDec          =   1
         BorderGrayAreaColor=   -2147483637
         ThreeDOnFocusInvert=   0   'False
         ThreeDFrameColor=   -2147483633
         Appearance      =   1
         BorderDropShadow=   0
         BorderDropShadowColor=   -2147483632
         BorderDropShadowWidth=   3
         ButtonColor     =   -2147483633
         AutoMenu        =   0   'False
         ButtonAlign     =   0
         OLEDropMode     =   0
         OLEDragMode     =   0
      End
      Begin VB.Label Label11 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Comments:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   225
         Left            =   135
         TabIndex        =   50
         Top             =   8415
         Width           =   945
      End
      Begin VB.Label Label10 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "%"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   11.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   270
         Left            =   10755
         TabIndex        =   48
         Top             =   8415
         Visible         =   0   'False
         Width           =   120
      End
      Begin VB.Label Label9 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Discount:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   11.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   270
         Left            =   8955
         TabIndex        =   46
         Top             =   8430
         Visible         =   0   'False
         Width           =   1080
      End
      Begin VB.Image picQRCode 
         BorderStyle     =   1  'Fixed Single
         Height          =   1905
         Left            =   13305
         Stretch         =   -1  'True
         Top             =   2010
         Width           =   2055
      End
      Begin VB.Label Label8 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "FBR Invoice:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   180
         Left            =   8940
         TabIndex        =   41
         Top             =   2220
         Width           =   1260
      End
      Begin VB.Label Label2 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Contact No:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   180
         Left            =   8970
         TabIndex        =   40
         Top             =   1425
         Width           =   1155
      End
      Begin VB.Label Label1 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Total Amount:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   11.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   270
         Left            =   9195
         TabIndex        =   39
         Top             =   7590
         Width           =   1560
      End
      Begin VB.Label Label3 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Recieveable:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   15.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   360
         Left            =   12750
         TabIndex        =   38
         Top             =   8400
         Width           =   2160
      End
      Begin VB.Label lblSalesTaxRate 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Sales Tax @16%:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   11.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   270
         Left            =   8955
         TabIndex        =   37
         Top             =   8010
         Width           =   1800
      End
      Begin VB.Label lblPaymentType 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Payment Type:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   15.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   360
         Left            =   135
         TabIndex        =   36
         Top             =   9420
         Width           =   2340
      End
      Begin VB.Label lblTransactionDate 
         BackStyle       =   0  'Transparent
         Caption         =   "Date:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   12
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   375
         Left            =   10710
         TabIndex        =   35
         Top             =   11640
         Width           =   2535
      End
      Begin VB.Label lblLabel4 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Brand:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   225
         Left            =   135
         TabIndex        =   34
         Top             =   1305
         Width           =   630
      End
      Begin VB.Label Label4 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Search:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   225
         Left            =   135
         TabIndex        =   33
         Top             =   1785
         Width           =   735
      End
      Begin VB.Label lblChangeAmount 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Change:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   15.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   360
         Left            =   11385
         TabIndex        =   32
         Top             =   9840
         Width           =   1260
      End
      Begin VB.Label lblCustomerAmount 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Customer:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   15.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   360
         Left            =   11025
         TabIndex        =   31
         Top             =   9330
         Width           =   1620
      End
      Begin VB.Label Label5 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Customer / Company Name:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   180
         Left            =   5250
         TabIndex        =   30
         Top             =   1455
         Width           =   2520
      End
      Begin VB.Label Label6 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Sales Tax / CNIC No:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   180
         Left            =   5250
         TabIndex        =   29
         Top             =   2175
         Width           =   2100
      End
      Begin VB.Label Label7 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Registration Address:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   180
         Left            =   5250
         TabIndex        =   28
         Top             =   2850
         Width           =   2205
      End
   End
End
Attribute VB_Name = "frmPointOfSale"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
Dim MemberCode As String
Dim CardNumber As String
Dim DiscountCode As String
Dim DiscountAmount As String
Dim IsPercentage As Boolean
Dim TransactionNo As String
Dim DockItPrints As Integer
Dim EmployeeCode As String
Dim strCatagoryCode As String
Dim GridTotalAmount As String

Private Sub btnSendFBR_Click()
        '<EhHeader>
        On Error GoTo btnSendFBR_Click_Err
        '</EhHeader>

        Dim ItempriceExTaXes  As Double
        Dim ItemQuantity As Double
        Dim TaxAmount As Double
        
100     If Len(Trim$(txtCustomer.Text)) = 0 Then
102         MsgBox "Please enter the Customer Name", vbOKOnly + vbCritical
104         txtCustomer.SetFocus
            Exit Sub
        End If
                       
106     If Len(Trim$(txtSalesTaxNumber.Text)) = 0 Then
108         MsgBox "Please enter the Sales Tax Number of CNIC Number", vbOKOnly + vbCritical
110         txtSalesTaxNumber.SetFocus
            Exit Sub
        End If
                       
112     If Len(Trim$(txtContactNo.Text)) = 0 Then
114         MsgBox "Please enter the Customer Contact Number", vbOKOnly + vbCritical
116         txtContactNo.SetFocus
            Exit Sub
        End If
    
118     If Len(Trim$(txtRegistrationAddress.Text)) = 0 Then
120         MsgBox "Please enter the Registration Address", vbOKOnly + vbCritical
122         txtRegistrationAddress.SetFocus
            Exit Sub
        End If
                       
124     sSQL = "Delete From Transactions Where TransactionNo='" & TransactionNo & "'"
126     Conn.Execute sSQL

128     sSQL = "Select * From Transactions Where TransactionNo='" & TransactionNo & "'"
130     Debug.Print sSQL
132     Set rs = New ADODB.Recordset
134     rs.Open sSQL, Conn, 1, 3
        
136     For i = 1 To vaSpread2.DataRowCnt
138         rs.AddNew
140         rs("TransactionNo") = TransactionNo
142         rs("TransactionDate") = TransactionDate
            
144         rs("Customers") = txtCustomer.Text
146         rs("SalesTaxNo") = txtSalesTaxNumber.Text
148         rs("ContactNo") = txtContactNo.Text
150         rs("RegistrationAddress") = txtRegistrationAddress.Text
152         rs("PaymentType") = cmbPaymentType.Text
154         rs("TransactionStartTime") = Time
156         rs("TransactionEndTime") = Time
158         rs("TransactionType") = cmbPaymentType.Text
160         rs("Comments") = txtComments.Text & ""

162         If cmbPaymentType.Text = "Credit A/C" Then
164             rs("EmployeeCode") = EmployeeCode
166             rs("EmployeeName") = cmbEmployees.Text
            End If

168         rs("UserID") = UserID
170         vaSpread2.Row = i
172         vaSpread2.Col = 1
174         rs("ItemCode") = vaSpread2.Text
176         vaSpread2.Col = 2
178         rs("ItemName") = vaSpread2.Text
180         vaSpread2.Col = 3
182         rs("ItemPrice") = Val(vaSpread2.Value & "")
            vaSpread2.Col = 7
184         rs("ItemPriceExTaxes") = Val(vaSpread2.Value & "")
'184         rs("ItemPriceExTaxes") = Val(txtAmountExcludingTaxes.Value & "")
186         vaSpread2.Col = 4
188         rs("ItemQuantity") = Val(vaSpread2.Value & "")
190         vaSpread2.Col = 5
192         rs("ItemTotalAmount") = Val(vaSpread2.Value & "")
194         rs("InvTotalAmount") = Val(txtPayableAmount.Value)

196         rs("TaxRate") = SalesTaxRate
198         vaSpread2.Col = 8
200         rs("TaxAmount") = Val(vaSpread2.Value & "")
202         rs("TransactionClose") = "Y"
204         rs("TransactionsVoid") = "F"
206         rs("VoidDescription") = ""
208         vaSpread2.Col = 10
210         rs("HSCode") = vaSpread2.Text
212         rs.Update

        Next
        
214     If Len(Dir(Left(strFBRInvoiceSend, Len(strFBRInvoiceSend) - 4))) > 0 Then
216         Call Shell(strFBRInvoiceSend, vbHide)
218         Timer1.Enabled = True
        Else
220         MsgBox ("Service is not found at:" & Chr(13) & strFBRInvoiceSend)
        End If

222     cmdPayments.Enabled = True

        '<EhFooter>
        Exit Sub

btnSendFBR_Click_Err:
        
                MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.btnSendFBR_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmbEmployees_Click()
        '<EhHeader>
        On Error GoTo cmbEmployees_Click_Err
        '</EhHeader>
    
100     EmployeeCode = Format(cmbEmployees.ItemData(cmbEmployees.ListIndex), "0###")
    
        '<EhFooter>
        Exit Sub

cmbEmployees_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmbEmployees_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmbInvoiceList_Click()
    
    Call txtTransactionNo_LostFocus
    
End Sub

Private Sub cmbPaymentType_Click()
        '<EhHeader>
        On Error GoTo cmbPaymentType_Click_Err
        '</EhHeader>
    
100     If cmbPaymentType.Text = "Cash" Then

102         DockItPrints = 1
        Else

104         DockItPrints = 2
        End If
  
106     If cmbPaymentType.Text = "Credit A/C" Then
108         cmbEmployees.Visible = True
        Else
110         cmbEmployees.Visible = False
        End If
        
        '<EhFooter>
        Exit Sub

cmbPaymentType_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmbPaymentType_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmbBrands_Click()
        '<EhHeader>
        On Error GoTo cmbBrands_Click_Err
        '</EhHeader>
    
100     strCatagoryCode = Format(cmbBrands.ItemData(cmbBrands.ListIndex), "0###")
102     Call Fill_vsSpreadItems
    
        '<EhFooter>
        Exit Sub

cmbBrands_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmbBrands_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdClose_Click()
        '<EhHeader>
        On Error GoTo cmdClose_Click_Err
        '</EhHeader>
    
100     Unload Me
    
        '<EhFooter>
        Exit Sub

cmdClose_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmdClose_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdFree_Click(Index As Integer)
        '<EhHeader>
        On Error GoTo cmdFree_Click_Err
        '</EhHeader>
        
100     vaSpread2.Row = vaSpread2.ActiveRow
102     vaSpread2.Col = 4
104     vaSpread2.Text = 0
106     CalculateBill
        
        '<EhFooter>
        Exit Sub

cmdFree_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmdFree_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdStart_Click()
        '<EhHeader>
        On Error GoTo cmdStart_Click_Err
        '</EhHeader>
                   
100     POS_Lock False

        '102     sSQL = "Select Max(TransactionNo) as LastNumber  From Transactions"
102     sSQL = "SELECT MAX(RIGHT(TransactionNo, 4)) AS LastNumber FROM  Transactions WHERE (YEAR(TransactionDate) = " & Year(Date) & ") AND (MONTH(TransactionDate) = " & cmbMonths.Text & ")"
104     Set rs = New ADODB.Recordset
106     rs.Open sSQL, Conn, 1, 3
        
108     If rs.EOF = True Then
110         TransactionNo = Format(1, "0###")
        Else
112         TransactionNo = Format(Val(rs("LastNumber") & "") + 1, "0###")
        End If
        
114     TransactionNo = UCase(Left(CompanyName, 2) & Right(Year(Date), 2) & Format(cmbMonths.Text, "0#") & TransactionNo)
                
116     IsPercentage = True
118     DiscountAmount = 0
        
120     cmbBrands.ListIndex = 0
122     txtSearchItem.Text = ""
124     txtCustomer.Text = ""
126     txtSalesTaxNumber.Text = ""
128     txtContactNo.Text = ""
130     txtFBRInvoiceNo.Enabled = False
132     txtFBRInvoiceNo.Text = ""
134     txtAmountExcludingTaxes.Text = ""
136     txtRegistrationAddress.Text = ""
        txtComments.Text = ""
        
        If cmbBrands.ListCount > 0 Then
            cmbBrands.ListIndex = 0
            cmbBrands_Click
        End If
138     MsgBox "Transaction No.: " & TransactionNo
        
        '<EhFooter>
        Exit Sub

cmdStart_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmdStart_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdPayments_Click()
        '<EhHeader>
        On Error GoTo cmdPayments_Click_Err
        '</EhHeader>
                
100     sSQL = "SELECT * From Transactions " & _
           "WHERE TransactionNo='" & TransactionNo & "'"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
        
106     With repSalesInvoiceFBR
108         .Restart
110         .imgMaison.Visible = False
112         .imgCollectibles.Visible = False
114         .imgLogoAMPM.Visible = False
                
116         If UCase(Left(CompanyName, 4)) = "AMPM" Then
118             .imgLogoAMPM.Visible = True
120         ElseIf UCase(Left(CompanyName, 4)) = "MAIS" Then
122             .imgMaison.Visible = True
            Else
124             .imgCollectibles.Visible = True
            End If
            
126         .lblCompanyAddress.Text = CompanyAddress
128         .txtInvoiceFooter.Text = DockitFooter
130         .txtCompanySalesTaxNumber.Text = CompanySalesTaxNumber
132         .lblSalesTaxHeading.Caption = "Sales Tax @ " & SalesTaxRate & " %:"
134         .documentName = "Invoice Print"
136         .DataControl1.Recordset = rs
138         .Show 1
        End With
                
140     POS_Lock True
        
        '<EhFooter>
        Exit Sub

cmdPayments_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmdPayments_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>
    
100     picCollectables.Visible = False
102     picMaisonSonraj.Visible = False
104     picAMPM.Visible = False
    
106     If UCase(Left(CompanyName, 4)) = "AMPM" Then
108         picAMPM.Visible = True
110     ElseIf UCase$(Left$(CompanyName, 4)) = "MAIS" Then
112         picMaisonSonraj.Visible = True
        Else
114         picCollectables.Visible = True
        End If
            
116     CenterForm Me
118     Me.WindowState = vbMaximized
        
        For i = 1 To 12
            cmbMonths.AddItem (i)
        Next
        cmbMonths.Text = Month(Date)
        
        If Command = "SHOWMONTH" Then

            cmbMonths.Visible = True
        End If
        
120     vaSpread2.Col = 7
122     vaSpread2.Row = -1
        'C#*100/125*D#
124     vaSpread2.Formula = "C#*" & 100 & "/" & 100 + Val(SalesTaxRate) & "*D#"
126     lblSalesTaxRate.Caption = "Sales Tax @" & SalesTaxRate & "%:"
128     FontControl = GetSetting(ApplicationName, "Drawer Settings", "FontName", "FontControl")
130     DrawerPort = GetSetting(ApplicationName, "Drawer Settings", "DrawerPort", "1")
        
132     TransactionDate = Format(TransactionDate, "dd/mmm/yyyy")
134     lblTransactionDate.Caption = "Date: " & TransactionDate
                
136     cmbBrands.ListIndex = 0
138     cmbPaymentType.ListIndex = 0
        
140     Call Fill_cmbBrands
142     POS_Lock True
144     Call FillcmbEmployees

146     txtSearchItem.Text = ""
148     txtCustomer.Text = ""
150     txtSalesTaxNumber.Text = ""
152     txtContactNo.Text = ""
154     txtRegistrationAddress.Text = ""

156     Fill_cmbInvoiceList
        
        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.frmPointOfSale.Form_Load " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub


Private Sub Fill_cmbInvoiceList()
    
    Dim FromDate As String
    Dim ToDate As String
     
    FromDate = Format("01/" & Month(TransactionDate) & "/" & Year(TransactionDate), "dd-MMM-yyyy")

    If (Month(TransactionDate) = 12) Then
        ToDate = Format("01/" & 1 & "/" & Year(TransactionDate) + 1, "dd-MMM-yyyy")
    Else
        ToDate = Format("01/" & Month(TransactionDate) + 1 & "/" & Year(TransactionDate), "dd-MM-yyyy")
    End If
    
    ToDate = Format(CDate(ToDate) - 1, "dd-MMM-yyyy")

    sSQL = "SELECT Distinct TransactionNo From Transactions WHERE (TransactionDate BETWEEN '" & FromDate & "' AND '" & ToDate & "') "
    Debug.Print sSQL
    Set rs = New ADODB.Recordset
    rs.Open sSQL, Conn, 1, 3
    
    cmbInvoiceList.Clear
    Do Until rs.EOF
        cmbInvoiceList.AddItem rs("TransactionNo")
        rs.MoveNext
    Loop
    
    
End Sub

Private Sub Fill_cmbBrands()
        '<EhHeader>
        On Error GoTo Fill_cmbBrands_Err
        '</EhHeader>

100     sSQL = "SELECT * FROM CatagorySetup"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     cmbBrands.Clear

108     Do Until rs.EOF
110         cmbBrands.AddItem rs("CatagoryName")
112         cmbBrands.ItemData(cmbBrands.NewIndex) = rs("CatagoryCode")
114         rs.MoveNext
        Loop
    
116     If cmbBrands.ListCount > 0 Then cmbBrands.ListIndex = 0

        '<EhFooter>
        Exit Sub

Fill_cmbBrands_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.Fill_cmbBrands " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Fill_vsSpreadItems()
        '<EhHeader>
        On Error GoTo Fill_vsSpreadItems_Err
        '</EhHeader>
    
100     If Len(Trim$(txtSearchItem.Text)) = 0 Then
102         sSQL = "SELECT ItemCode, ItemName, ItemDesc, HSCode, Price From ItemSetup WHERE (CatagoryCode='" & strCatagoryCode & "') AND IsActive='Y'"
        Else
104         sSQL = "SELECT ItemCode, ItemName, ItemDesc, HSCode, Price From ItemSetup WHERE (CatagoryCode='" & strCatagoryCode & "') AND (ItemName LIKE '%" & txtSearchItem.Text & "%') AND IsActive='Y'"
        End If

106     Debug.Print sSQL
108     Set rs = New ADODB.Recordset
110     rs.Open sSQL, Conn, 1, 3
        
112     vaSpread1.MaxRows = 0
114     vaSpread1.MaxRows = 1
116     vaSpread1.Row = 1

118     Do Until rs.EOF
120         vaSpread1.Col = 0
122         vaSpread1.Text = rs("ItemCode")
124         vaSpread1.Col = 1
126         vaSpread1.Text = rs("ItemName")
128         vaSpread1.Col = 2
130         vaSpread1.Text = rs("Price")
132         vaSpread1.Col = 4
134         vaSpread1.Text = rs("HSCode") & ""
136         rs.MoveNext
138         vaSpread1.MaxRows = vaSpread1.MaxRows + 1
140         vaSpread1.Row = vaSpread1.Row + 1
        Loop

142     vaSpread1.MaxRows = vaSpread1.DataRowCnt
        
        '<EhFooter>
        Exit Sub

Fill_vsSpreadItems_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.Fill_vsSpreadItems " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdBasket_Click()
        '<EhHeader>
        On Error GoTo cmdBasket_Click_Err
        '</EhHeader>

100     sSQL = "Delete From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
102     Conn.Execute sSQL

104     sSQL = "Select * From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
106     Debug.Print sSQL
108     Set rs = New ADODB.Recordset
110     rs.Open sSQL, Conn, 1, 3
        
112     For i = 1 To vaSpread2.DataRowCnt
114         rs.AddNew
116         rs("TransactionNo") = TransactionNo
118         rs("TransactionDate") = TransactionDate
120         rs("TransactionStartTime") = Time
122         rs("TransactionType") = cmbBrands.Text

124         If cmbPaymentType.Text = "Credit A/C" Then
126             rs("EmployeeCode") = EmployeeCode
128             rs("EmployeeName") = cmbEmployees.Text
            End If

130         rs("UserID") = UserID
132         vaSpread2.Row = i
134         vaSpread2.Col = 1
136         rs("ItemCode") = vaSpread2.Text
138         vaSpread2.Col = 2
140         rs("ItemName") = vaSpread2.Text
142         vaSpread2.Col = 9
144         rs("ItemPrice") = vaSpread2.Value
146         vaSpread2.Col = 4

148         If DiscountAmount <> 100 Then
150             rs("ItemQuantity") = vaSpread2.Value
            Else
152             rs("ItemQuantity") = 0
            End If

154         rs("DiscountCode") = DiscountCode
'156         rs("DiscountAmount") = txtDiscount.Text
158         rs("TaxRate") = SalesTaxRate
160         rs("TaxAmount") = txtSalesTaxAmount.Text
162         rs("TransactionClose") = False
164         rs("TransactionsVoid") = False
166         rs("VoidDescription") = ""
172         rs.Update
        Next

        
176     POS_Lock True

        '<EhFooter>
        Exit Sub

cmdBasket_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.cmdBasket_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Resize()
    '<EhHeader>
    On Error Resume Next
    '</EhHeader>

    picMain.Left = (Me.ScaleWidth - picMain.Width) / 2
    picMain.Top = (Me.ScaleHeight - picMain.Height) / 2

End Sub

Private Sub Timer1_Timer()
    
    Timer1.Enabled = False
    
    sSQL = "SELECT * FROM Transactions WHERE (TransactionNo='" & TransactionNo & "')"
    Set rs = New ADODB.Recordset
    rs.Open sSQL, Conn, 1, 3
            
    If Len(rs("FBRInvoiceNumber") & "") > 0 Then
        txtFBRInvoiceNo.Text = rs("FBRInvoiceNumber")
        picQRCode.Picture = LoadPicture(strQRCode & txtFBRInvoiceNo.Text & ".jpg")
        cmdPayments.Enabled = True
        Exit Sub
    End If
    
    Timer1.Enabled = True

End Sub

Private Sub txtCustomerAmount_Change()
        '<EhHeader>
        On Error GoTo txtCustomerAmount_Change_Err
        '</EhHeader>

100     txtChangeAmount.Value = txtCustomerAmount.Value - txtPayableAmount.Value
    
        '<EhFooter>
        Exit Sub

txtCustomerAmount_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.txtCustomerAmount_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtCustomerAmount_LostFocus()
        '<EhHeader>
        On Error GoTo txtCustomerAmount_LostFocus_Err
        '</EhHeader>

100     txtChangeAmount.Value = txtCustomerAmount.Value - txtPayableAmount.Value

        '<EhFooter>
        Exit Sub

txtCustomerAmount_LostFocus_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.txtCustomerAmount_LostFocus " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtDiscount_Change()
    
    CalculateBill
    
End Sub

Private Sub txtDiscPercentage_Change()
        '<EhHeader>
        On Error GoTo txtDiscPercentage_Change_Err
        '</EhHeader>
        
        If txtDiscPercentage.Value > 0 Then
100         txtDiscount.Value = Round(Val(GridTotalAmount) * txtDiscPercentage.Value / 100, 2)
        End If
    
        '<EhFooter>
        Exit Sub

txtDiscPercentage_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.txtDiscPercentage_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtPayableAmount_Change()
        '<EhHeader>
        On Error GoTo txtPayableAmount_Change_Err
        '</EhHeader>
    
100     Call txtCustomerAmount_Change
    
        '<EhFooter>
        Exit Sub

txtPayableAmount_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.txtPayableAmount_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtSearchItem_Change()
        '<EhHeader>
        On Error GoTo txtSearchItem_Change_Err
        '</EhHeader>

100     Call Fill_vsSpreadItems

        '<EhFooter>
        Exit Sub

txtSearchItem_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.txtSearchItem_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtTransactionNo_LostFocus()
        '<EhHeader>
        On Error GoTo txtTransactionNo_LostFocus_Err
        '</EhHeader>
        
        Dim MemberCode As String
        Dim CardNumber As String
        txtTransactionNo.Text = cmbInvoiceList.Text
100     If Len(txtTransactionNo.Text) = 0 Then
102         MsgBox "Invalid Transaction Number ", vbOKOnly + vbCritical, "Error!"
            Exit Sub
        End If

108     TransactionDate = Format(TransactionDate, "dd/mmm/yyyy")

110     sSQL = "SELECT Transactions.*, ItemSetup.Price AS Price FROM Transactions INNER JOIN ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode " & _
           "WHERE  Transactions.TransactionNo='" & TransactionNo & "'"
        
112     Debug.Print sSQL

114     Set rs = New ADODB.Recordset
116     rs.Open sSQL, Conn, 1, 3
        
118     vaSpread2.MaxRows = 0
120     vaSpread2.MaxRows = 20
122     vaSpread2.Row = 1

        strCatagoryCode = rs("CatagoryCode")
146         txtCustomer.Text = rs("Customers")
148         txtSalesTaxNumber.Text = rs("SalesTaxNo")
150         txtContactNo.Text = rs("ContactNo")
152         txtRegistrationAddress.Text = rs("RegistrationAddress")
154         cmbPaymentType.Text = rs("PaymentType")
160         cmbPaymentType.Text = rs("TransactionType")

168         UserID = rs("UserID")

124     Do Until rs.EOF
        
172         vaSpread2.Col = 1
174         vaSpread2.Text = rs("ItemCode")
176         vaSpread2.Col = 2
178         vaSpread2.Text = rs("ItemName")
180         vaSpread2.Col = 3
182         vaSpread2.Value = Val(rs("ItemPrice") & "")
            vaSpread2.Col = 7
            vaSpread2.Value = Val(rs("ItemPriceExTaxes") & "")
184         vaSpread2.Col = 4
186         vaSpread2.Value = Val(rs("ItemQuantity") & "")
188         vaSpread2.Col = 5
190         vaSpread2.Value = Val(rs("ItemTotalAmount") & "")
            vaSpread2.Col = 8
196         vaSpread2.Value = Val(rs("TaxAmount") & "")
            
192         txtAmountExcludingTaxes.Text = rs("InvTotalAmount")
194         SalesTaxRate = rs("TaxRate")
                
1461         vaSpread2.Row = vaSpread2.Row + 1
1481         rs.MoveNext
        Loop
        
1501     Call CalculateBill
        '<EhFooter>
        Exit Sub

txtTransactionNo_LostFocus_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.txtTransactionNo_LostFocus " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread1_ButtonClicked(ByVal Col As Long, _
                                    ByVal Row As Long, _
                                    ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread1_ButtonClicked_Err
        '</EhHeader>
        
100     vaSpread1.Row = vaSpread1.ActiveRow
102     vaSpread2.Row = vaSpread2.ActiveRow
104     vaSpread1.Col = 0
106     vaSpread2.Col = 1
108     vaSpread2.Text = vaSpread1.Text
110     vaSpread1.Col = 1
112     vaSpread2.Col = 2
114     vaSpread2.Text = vaSpread1.Text
116     vaSpread1.Col = 2
118     vaSpread2.Col = 3
120     vaSpread2.Text = vaSpread1.Text
122     vaSpread2.Col = 4
124     vaSpread2.Text = 1
130     vaSpread1.Col = 4
132     vaSpread2.Col = 10
134     vaSpread2.Text = vaSpread1.Text & ""
126     vaSpread2.Row = vaSpread2.Row + 1
128     vaSpread2.Col = 1
136     vaSpread2.Action = ActionActiveCell
    
138     Call CalculateBill
    
        '<EhFooter>
        Exit Sub

vaSpread1_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.vaSpread1_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread2_ButtonClicked(ByVal Col As Long, _
                                    ByVal Row As Long, _
                                    ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread2_ButtonClicked_Err
        '</EhHeader>
    
100     vaSpread2.Row = vaSpread2.ActiveRow
102     vaSpread2.Action = ActionDeleteRow
        
        '<EhFooter>
        Exit Sub

vaSpread2_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.vaSpread2_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub CalculateBill()
        '<EhHeader>
        On Error GoTo CalculateBill_Err
        '</EhHeader>
    
        Dim TotalAmount As String
        Dim TotalTaxableAmount As String
        Dim TotalTaxAmount As String

100     For i = 1 To vaSpread2.MaxRows
102         vaSpread2.Row = i
104         vaSpread2.Col = 5
110         TotalAmount = Val(TotalAmount) + Val(vaSpread2.Value & "")
'108         vaSpread2.Col = 8
'106         TotalTaxAmount = Val(TotalTaxAmount) + Val(vaSpread2.Value & "")
        
        Next
        
        GridTotalAmount = TotalAmount
                 
120     If DiscountAmount = "100" Then
122         txtSalesTaxAmount.Text = 0
        Else
126         txtSalesTaxAmount.Text = TotalTaxAmount
        End If
        
        TotalTaxableAmount = TotalAmount - txtDiscount.Value
        

        txtSalesTaxAmount.Value = TotalTaxableAmount * Val(SalesTaxRate) / (100 + Val(SalesTaxRate))
        txtAmountExcludingTaxes.Value = Val(TotalTaxableAmount) - Val(txtSalesTaxAmount.Value)

128     txtPayableAmount.Text = Val(TotalTaxableAmount)
    
        '<EhFooter>
        Exit Sub

CalculateBill_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.CalculateBill " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub POS_Lock(strStatus As Boolean)
        '<EhHeader>
        On Error GoTo POS_Lock_Err
        '</EhHeader>

104     strStatus = Not strStatus
        
106     cmdStart.Enabled = Not strStatus
        cmdPayments.Enabled = Not strStatus
        
128     vaSpread1.Enabled = strStatus
130     vaSpread2.Enabled = strStatus

132     If strStatus = True Then

134         vaSpread1.MaxRows = 0
136         vaSpread1.MaxRows = 25

138         vaSpread2.MaxRows = 0
140         vaSpread2.MaxRows = 20
        
        End If
        
142     cmdPayments.Enabled = strStatus
    
        '<EhFooter>
        Exit Sub

POS_Lock_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.POS_Lock " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread2_LeaveCell(ByVal Col As Long, _
                                ByVal Row As Long, _
                                ByVal NewCol As Long, _
                                ByVal NewRow As Long, _
                                Cancel As Boolean)
        '<EhHeader>
        On Error GoTo vaSpread2_LeaveCell_Err
        '</EhHeader>
        
100     CalculateBill

        '<EhFooter>
        Exit Sub

vaSpread2_LeaveCell_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.vaSpread2_LeaveCell " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub


Sub FillcmbEmployees()
        '<EhHeader>
        On Error GoTo FillcmbEmployees_Err
        '</EhHeader>

100     sSQL = "SELECT EmployeeCode, EmployeeName From EmployeeInformation"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     cmbEmployees.Clear

108     Do Until rs.EOF
110         cmbEmployees.AddItem rs("EmployeeName")
112         cmbEmployees.ItemData(cmbEmployees.NewIndex) = rs("EmployeeCode")
114         rs.MoveNext
        Loop

116     cmbEmployees.ListIndex = 0
    
        '<EhFooter>
        Exit Sub

FillcmbEmployees_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPointOfSale.FillcmbEmployees " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

