VERSION 5.00
Object = "{CDF3B183-D408-11CE-AE2C-0080C786E37D}#3.0#0"; "Edt32x30.ocx"
Object = "{B02F3647-766B-11CE-AF28-C3A2FBE76A13}#3.0#0"; "SPR32X30.ocx"
Object = "{648A5603-2C6E-101B-82B6-000000000014}#1.1#0"; "MSCOMM32.OCX"
Begin VB.Form frmPOSPavilianEndClub 
   BackColor       =   &H00EEEEEE&
   Caption         =   "POS for PavilianEndClub"
   ClientHeight    =   9435
   ClientLeft      =   525
   ClientTop       =   1140
   ClientWidth     =   12420
   BeginProperty Font 
      Name            =   "Arial"
      Size            =   8.25
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmPOSPavilianEndClub.frx":0000
   LinkTopic       =   "Form1"
   ScaleHeight     =   9435
   ScaleWidth      =   12420
   Tag             =   "Pavilian End Club"
   Begin EditLib.fpDateTime txtTransactionDate 
      Height          =   360
      Left            =   5505
      TabIndex        =   58
      Top             =   60
      Visible         =   0   'False
      Width           =   1485
      _Version        =   196608
      _ExtentX        =   2619
      _ExtentY        =   635
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   -2147483643
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   1
      AlignTextV      =   1
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "01/06/2009"
      DateCalcMethod  =   0
      DateTimeFormat  =   5
      UserDefinedFormat=   "dd/mm/yyyy"
      DateMax         =   "00000000"
      DateMin         =   "00000000"
      TimeMax         =   "000000"
      TimeMin         =   "000000"
      TimeString1159  =   ""
      TimeString2359  =   ""
      DateDefault     =   "00000000"
      TimeDefault     =   "000000"
      TimeStyle       =   0
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      PopUpType       =   0
      DateCalcY2KSplit=   60
      CaretPosition   =   0
      IncYear         =   1
      IncMonth        =   1
      IncDay          =   1
      IncHour         =   1
      IncMinute       =   1
      IncSecond       =   1
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      StartMonth      =   4
      ButtonAlign     =   0
      BoundDataType   =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin VB.TextBox txtTransactionNo 
      Appearance      =   0  'Flat
      Height          =   360
      Left            =   7050
      TabIndex        =   57
      Top             =   60
      Width           =   1305
   End
   Begin VB.Frame fraMemberInformation 
      BackColor       =   &H00EEEEEE&
      Caption         =   "Member Information:"
      Height          =   1545
      Left            =   105
      TabIndex        =   46
      Top             =   7785
      Width           =   8505
      Begin VB.TextBox txtCardNumber 
         Appearance      =   0  'Flat
         Height          =   315
         Left            =   5145
         Locked          =   -1  'True
         TabIndex        =   51
         Top             =   315
         Width           =   1680
      End
      Begin VB.TextBox txtMemberCode 
         Appearance      =   0  'Flat
         Height          =   315
         Left            =   1785
         TabIndex        =   48
         Top             =   315
         Width           =   1470
      End
      Begin VB.TextBox txtMemberName 
         Appearance      =   0  'Flat
         Height          =   315
         Left            =   1785
         Locked          =   -1  'True
         TabIndex        =   47
         Top             =   712
         Width           =   5040
      End
      Begin EditLib.fpCurrency txtBalanceAmount 
         Height          =   300
         Left            =   1785
         TabIndex        =   54
         Top             =   1125
         Width           =   1830
         _Version        =   196608
         _ExtentX        =   3228
         _ExtentY        =   529
         Enabled         =   0   'False
         MousePointer    =   0
         Object.TabStop         =   -1  'True
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BackColor       =   11856344
         ForeColor       =   -2147483640
         ThreeDInsideStyle=   0
         ThreeDInsideHighlightColor=   -2147483633
         ThreeDInsideShadowColor=   -2147483642
         ThreeDInsideWidth=   1
         ThreeDOutsideStyle=   0
         ThreeDOutsideHighlightColor=   -2147483628
         ThreeDOutsideShadowColor=   -2147483632
         ThreeDOutsideWidth=   1
         ThreeDFrameWidth=   0
         BorderStyle     =   1
         BorderColor     =   -2147483642
         BorderWidth     =   1
         ButtonDisable   =   0   'False
         ButtonHide      =   0   'False
         ButtonIncrement =   1
         ButtonMin       =   0
         ButtonMax       =   100
         ButtonStyle     =   0
         ButtonWidth     =   0
         ButtonWrap      =   -1  'True
         ButtonDefaultAction=   -1  'True
         ThreeDText      =   0
         ThreeDTextHighlightColor=   -2147483633
         ThreeDTextShadowColor=   -2147483632
         ThreeDTextOffset=   1
         AlignTextH      =   2
         AlignTextV      =   0
         AllowNull       =   0   'False
         NoSpecialKeys   =   0
         AutoAdvance     =   0   'False
         AutoBeep        =   0   'False
         CaretInsert     =   0
         CaretOverWrite  =   3
         UserEntry       =   0
         HideSelection   =   -1  'True
         InvalidColor    =   -2147483637
         InvalidOption   =   0
         MarginLeft      =   3
         MarginTop       =   3
         MarginRight     =   3
         MarginBottom    =   3
         NullColor       =   -2147483637
         OnFocusAlignH   =   0
         OnFocusAlignV   =   0
         OnFocusNoSelect =   0   'False
         OnFocusPosition =   0
         ControlType     =   0
         Text            =   "Rs:0.00"
         CurrencyDecimalPlaces=   -1
         CurrencyNegFormat=   0
         CurrencyPlacement=   0
         CurrencySymbol  =   ""
         DecimalPoint    =   ""
         FixedPoint      =   -1  'True
         LeadZero        =   0
         MaxValue        =   "9000000000"
         MinValue        =   "-9000000000"
         NegToggle       =   0   'False
         Separator       =   ""
         UseSeparator    =   0   'False
         IncInt          =   1
         IncDec          =   1
         BorderGrayAreaColor=   -2147483637
         ThreeDOnFocusInvert=   0   'False
         ThreeDFrameColor=   -2147483633
         Appearance      =   1
         BorderDropShadow=   0
         BorderDropShadowColor=   -2147483632
         BorderDropShadowWidth=   3
         ButtonColor     =   -2147483633
         AutoMenu        =   0   'False
         ButtonAlign     =   0
         OLEDropMode     =   0
         OLEDragMode     =   0
      End
      Begin Point_of_Sale_System.lvButtons_H cmdClear 
         Height          =   390
         Left            =   5820
         TabIndex        =   55
         Top             =   1065
         Width           =   1005
         _ExtentX        =   1773
         _ExtentY        =   688
         Caption         =   "Clear"
         CapAlign        =   2
         BackStyle       =   2
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   12
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Mode            =   0
         Value           =   0   'False
         ImgAlign        =   2
         ImgSize         =   32
         cBack           =   -2147483633
      End
      Begin Point_of_Sale_System.lvButtons_H cmdBrowser 
         Height          =   330
         Left            =   3315
         TabIndex        =   56
         Top             =   300
         Width           =   360
         _ExtentX        =   635
         _ExtentY        =   582
         Caption         =   "..."
         CapAlign        =   2
         BackStyle       =   2
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   9
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Mode            =   0
         Value           =   0   'False
         ImgAlign        =   2
         ImgSize         =   32
         cBack           =   -2147483633
      End
      Begin VB.Image PicMemberPhoto 
         Appearance      =   0  'Flat
         Height          =   1200
         Left            =   6885
         Stretch         =   -1  'True
         Top             =   225
         Width           =   1500
      End
      Begin VB.Label lblBalanceAmount 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Balance Amount:"
         BeginProperty Font 
            Name            =   "Arial"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   240
         Left            =   90
         TabIndex        =   53
         Top             =   1155
         Width           =   1635
      End
      Begin VB.Label lblCardNumber 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Card Number:"
         BeginProperty Font 
            Name            =   "Arial"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   240
         Left            =   3735
         TabIndex        =   52
         Top             =   345
         Width           =   1320
      End
      Begin VB.Label Label7 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Member Code:"
         BeginProperty Font 
            Name            =   "Arial"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   240
         Left            =   90
         TabIndex        =   50
         Top             =   352
         Width           =   1395
      End
      Begin VB.Label lblLabel6 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Member Name:"
         BeginProperty Font 
            Name            =   "Arial"
            Size            =   9.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   240
         Left            =   90
         TabIndex        =   49
         Top             =   749
         Width           =   1455
      End
   End
   Begin VB.Frame Frame1 
      BackColor       =   &H00EEEEEE&
      Height          =   105
      Left            =   -75
      TabIndex        =   19
      Top             =   9330
      Width           =   15810
   End
   Begin VB.ComboBox cmbDiscount 
      Height          =   330
      Left            =   6270
      Style           =   2  'Dropdown List
      TabIndex        =   7
      Top             =   7050
      Width           =   2280
   End
   Begin VB.ComboBox cmbTables 
      Height          =   330
      ItemData        =   "frmPOSPavilianEndClub.frx":000C
      Left            =   2100
      List            =   "frmPOSPavilianEndClub.frx":0016
      Style           =   2  'Dropdown List
      TabIndex        =   6
      Top             =   75
      Width           =   3375
   End
   Begin VB.PictureBox Picture2 
      Appearance      =   0  'Flat
      AutoSize        =   -1  'True
      BackColor       =   &H80000005&
      ForeColor       =   &H80000008&
      Height          =   1065
      Left            =   11850
      Picture         =   "frmPOSPavilianEndClub.frx":002D
      ScaleHeight     =   1035
      ScaleWidth      =   3465
      TabIndex        =   4
      Top             =   -15
      Width           =   3495
   End
   Begin VB.ComboBox cmbPaymentType 
      Height          =   330
      ItemData        =   "frmPOSPavilianEndClub.frx":0719
      Left            =   60
      List            =   "frmPOSPavilianEndClub.frx":0726
      Style           =   2  'Dropdown List
      TabIndex        =   3
      Top             =   7425
      Width           =   2625
   End
   Begin VB.PictureBox Picture3 
      Appearance      =   0  'Flat
      BackColor       =   &H80000005&
      Enabled         =   0   'False
      ForeColor       =   &H80000008&
      Height          =   555
      Left            =   12570
      ScaleHeight     =   525
      ScaleWidth      =   2535
      TabIndex        =   1
      Top             =   7515
      Width           =   2565
      Begin EditLib.fpCurrency txtPayableAmount 
         Height          =   555
         Left            =   -15
         TabIndex        =   2
         Top             =   -15
         Width           =   2565
         _Version        =   196608
         _ExtentX        =   4524
         _ExtentY        =   979
         Enabled         =   -1  'True
         MousePointer    =   0
         Object.TabStop         =   -1  'True
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Consolas"
            Size            =   20.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         BackColor       =   11856344
         ForeColor       =   -2147483640
         ThreeDInsideStyle=   0
         ThreeDInsideHighlightColor=   -2147483633
         ThreeDInsideShadowColor=   -2147483642
         ThreeDInsideWidth=   1
         ThreeDOutsideStyle=   0
         ThreeDOutsideHighlightColor=   -2147483628
         ThreeDOutsideShadowColor=   -2147483632
         ThreeDOutsideWidth=   1
         ThreeDFrameWidth=   0
         BorderStyle     =   1
         BorderColor     =   -2147483642
         BorderWidth     =   1
         ButtonDisable   =   0   'False
         ButtonHide      =   0   'False
         ButtonIncrement =   1
         ButtonMin       =   0
         ButtonMax       =   100
         ButtonStyle     =   0
         ButtonWidth     =   0
         ButtonWrap      =   -1  'True
         ButtonDefaultAction=   -1  'True
         ThreeDText      =   0
         ThreeDTextHighlightColor=   -2147483633
         ThreeDTextShadowColor=   -2147483632
         ThreeDTextOffset=   1
         AlignTextH      =   2
         AlignTextV      =   0
         AllowNull       =   0   'False
         NoSpecialKeys   =   0
         AutoAdvance     =   0   'False
         AutoBeep        =   0   'False
         CaretInsert     =   0
         CaretOverWrite  =   3
         UserEntry       =   0
         HideSelection   =   -1  'True
         InvalidColor    =   -2147483637
         InvalidOption   =   0
         MarginLeft      =   3
         MarginTop       =   3
         MarginRight     =   3
         MarginBottom    =   3
         NullColor       =   -2147483637
         OnFocusAlignH   =   0
         OnFocusAlignV   =   0
         OnFocusNoSelect =   0   'False
         OnFocusPosition =   0
         ControlType     =   0
         Text            =   "Rs:0.00"
         CurrencyDecimalPlaces=   -1
         CurrencyNegFormat=   0
         CurrencyPlacement=   0
         CurrencySymbol  =   ""
         DecimalPoint    =   ""
         FixedPoint      =   -1  'True
         LeadZero        =   0
         MaxValue        =   "9000000000"
         MinValue        =   "-9000000000"
         NegToggle       =   0   'False
         Separator       =   ""
         UseSeparator    =   0   'False
         IncInt          =   1
         IncDec          =   1
         BorderGrayAreaColor=   -2147483637
         ThreeDOnFocusInvert=   0   'False
         ThreeDFrameColor=   -2147483633
         Appearance      =   1
         BorderDropShadow=   0
         BorderDropShadowColor=   -2147483632
         BorderDropShadowWidth=   3
         ButtonColor     =   -2147483633
         AutoMenu        =   0   'False
         ButtonAlign     =   0
         OLEDropMode     =   0
         OLEDragMode     =   0
      End
   End
   Begin VB.ComboBox cmbEmployees 
      Height          =   330
      ItemData        =   "frmPOSPavilianEndClub.frx":0749
      Left            =   2760
      List            =   "frmPOSPavilianEndClub.frx":074B
      Style           =   2  'Dropdown List
      TabIndex        =   0
      Top             =   7425
      Width           =   2625
   End
   Begin EditLib.fpLongInteger txtCustomers 
      Height          =   480
      Left            =   4275
      TabIndex        =   5
      Top             =   450
      Width           =   1170
      _Version        =   196608
      _ExtentX        =   2064
      _ExtentY        =   847
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   14.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   -2147483643
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   1
      ButtonMax       =   100
      ButtonStyle     =   1
      ButtonWidth     =   0
      ButtonWrap      =   0   'False
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   0
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "1"
      MaxValue        =   "100"
      MinValue        =   "1"
      NegFormat       =   1
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin EditLib.fpCurrency txtTotalAmount 
      Height          =   300
      Left            =   10575
      TabIndex        =   8
      Top             =   7050
      Width           =   1830
      _Version        =   196608
      _ExtentX        =   3228
      _ExtentY        =   529
      Enabled         =   0   'False
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin MSCommLib.MSComm MSComm1 
      Left            =   8970
      Top             =   8475
      _ExtentX        =   1005
      _ExtentY        =   1005
      _Version        =   393216
      DTREnable       =   -1  'True
      InputLen        =   14
      RThreshold      =   14
      SThreshold      =   14
   End
   Begin Point_of_Sale_System.lvButtons_H cmdStart 
      Height          =   840
      Left            =   30
      TabIndex        =   9
      Top             =   75
      Width           =   1860
      _ExtentX        =   3281
      _ExtentY        =   1482
      Caption         =   "Start"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPOSPavilianEndClub.frx":074D
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   1
      Left            =   6855
      TabIndex        =   10
      Tag             =   "0005"
      Top             =   1110
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Cookies Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSPavilianEndClub.frx":65BF
      ImgSize         =   24
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.ucStatusBar UcSStatText 
      Height          =   330
      Left            =   -15
      TabIndex        =   11
      Top             =   10470
      Width           =   15375
      _ExtentX        =   27120
      _ExtentY        =   582
      Text            =   " Point of Sale-={by www.intelysol.com"
   End
   Begin FPSpread.vaSpread vaSpread2 
      Height          =   5190
      Left            =   5070
      TabIndex        =   12
      Top             =   1785
      Width           =   7365
      _Version        =   196608
      _ExtentX        =   12991
      _ExtentY        =   9155
      _StockProps     =   64
      EditEnterAction =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   9
      MaxRows         =   20
      ScrollBars      =   2
      SpreadDesigner  =   "frmPOSPavilianEndClub.frx":8941
   End
   Begin FPSpread.vaSpread vaSpread1 
      Height          =   5175
      Left            =   30
      TabIndex        =   13
      Top             =   1785
      Width           =   4950
      _Version        =   196608
      _ExtentX        =   8731
      _ExtentY        =   9128
      _StockProps     =   64
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   3
      MaxRows         =   20
      ScrollBars      =   2
      SpreadDesigner  =   "frmPOSPavilianEndClub.frx":90E2
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   0
      Left            =   1755
      TabIndex        =   14
      Tag             =   "0002"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Beef    Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPOSPavilianEndClub.frx":965E
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   2
      Left            =   5160
      TabIndex        =   15
      Tag             =   "0004"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Ice Cream && Drink"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSPavilianEndClub.frx":242C0
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   3
      Left            =   8565
      TabIndex        =   16
      Tag             =   "0006"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Others && Salats"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSPavilianEndClub.frx":26642
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   4
      Left            =   15
      TabIndex        =   17
      Tag             =   "0001"
      Top             =   1125
      Width           =   1710
      _ExtentX        =   3016
      _ExtentY        =   1032
      Caption         =   "Chicken Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      CapStyle        =   1
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSPavilianEndClub.frx":27214
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdClose 
      Height          =   840
      Left            =   13470
      TabIndex        =   18
      Top             =   9510
      Width           =   1860
      _ExtentX        =   3281
      _ExtentY        =   1482
      Caption         =   "Close"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      cGradient       =   0
      Mode            =   0
      Value           =   0   'False
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   5
      Left            =   3465
      TabIndex        =   20
      Tag             =   "0003"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Rice    Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPOSPavilianEndClub.frx":27DE6
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   6
      Left            =   10260
      TabIndex        =   21
      Tag             =   "0007"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Extra Items"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSPavilianEndClub.frx":2A168
      ImgSize         =   24
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdItems 
      Height          =   585
      Index           =   7
      Left            =   11955
      TabIndex        =   22
      Tag             =   "0008"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Sea Foods"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      Image           =   "frmPOSPavilianEndClub.frx":2F95A
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin EditLib.fpCurrency txtDiscount 
      Height          =   300
      Left            =   10575
      TabIndex        =   23
      Top             =   7425
      Width           =   1830
      _Version        =   196608
      _ExtentX        =   3228
      _ExtentY        =   529
      Enabled         =   0   'False
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin Point_of_Sale_System.lvButtons_H cmdPayments 
      Height          =   840
      Left            =   30
      TabIndex        =   24
      Top             =   9525
      Width           =   2415
      _ExtentX        =   4260
      _ExtentY        =   1482
      Caption         =   "Payments"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPOSPavilianEndClub.frx":3514C
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin EditLib.fpCurrency txtSalesTaxAmount 
      Height          =   300
      Left            =   10575
      TabIndex        =   26
      Top             =   7785
      Width           =   1830
      _Version        =   196608
      _ExtentX        =   3228
      _ExtentY        =   529
      Enabled         =   0   'False
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   -1  'True
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin EditLib.fpCurrency txtCustomerAmount 
      Height          =   555
      Left            =   12570
      TabIndex        =   27
      Top             =   8145
      Width           =   2565
      _Version        =   196608
      _ExtentX        =   4524
      _ExtentY        =   979
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin EditLib.fpCurrency txtChangeAmount 
      Height          =   555
      Left            =   12570
      TabIndex        =   28
      Top             =   8775
      Width           =   2565
      _Version        =   196608
      _ExtentX        =   4524
      _ExtentY        =   979
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin FPSpread.vaSpread vaSpread3 
      Height          =   4905
      Left            =   12525
      TabIndex        =   29
      Top             =   2040
      Width           =   2700
      _Version        =   196608
      _ExtentX        =   4762
      _ExtentY        =   8652
      _StockProps     =   64
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   3
      MaxRows         =   20
      ScrollBars      =   2
      SpreadDesigner  =   "frmPOSPavilianEndClub.frx":3AD6E
      UserResize      =   1
   End
   Begin Point_of_Sale_System.lvButtons_H cmdSave 
      Height          =   840
      Left            =   5055
      TabIndex        =   30
      Top             =   9525
      Width           =   2415
      _ExtentX        =   4260
      _ExtentY        =   1482
      Caption         =   "Save"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPOSPavilianEndClub.frx":3B14E
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdFree 
      Height          =   585
      Index           =   8
      Left            =   13665
      TabIndex        =   31
      Tag             =   "0008"
      Top             =   1125
      Width           =   1665
      _ExtentX        =   2937
      _ExtentY        =   1032
      Caption         =   "Free"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   3
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin VB.Frame Frame2 
      BackColor       =   &H00EEEEEE&
      Height          =   105
      Left            =   -45
      TabIndex        =   25
      Top             =   990
      Width           =   15810
   End
   Begin Point_of_Sale_System.lvButtons_H cmdPrintAgain 
      Height          =   840
      Left            =   2542
      TabIndex        =   59
      Top             =   9525
      Width           =   2415
      _ExtentX        =   4260
      _ExtentY        =   1482
      Caption         =   "RePrint"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPOSPavilianEndClub.frx":3FF50
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin VB.Label lblTime 
      BackStyle       =   0  'Transparent
      Caption         =   "Time:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   375
      Left            =   10650
      TabIndex        =   45
      Top             =   9960
      Width           =   2535
   End
   Begin VB.Label Label1 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Total Amount :"
      Height          =   210
      Left            =   9180
      TabIndex        =   42
      Top             =   7110
      Width           =   1200
   End
   Begin VB.Label lblDiscount 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Discount:"
      Height          =   210
      Left            =   5175
      TabIndex        =   41
      Top             =   7110
      Width           =   765
   End
   Begin VB.Label Label2 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Discount :"
      Height          =   210
      Left            =   9570
      TabIndex        =   40
      Top             =   7470
      Width           =   810
   End
   Begin VB.Label Label3 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Recieveable:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   480
      Left            =   12555
      TabIndex        =   39
      Top             =   7080
      Width           =   2700
   End
   Begin VB.Label lblLabel4 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Customers:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   15.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   360
      Left            =   2130
      TabIndex        =   38
      Top             =   510
      Width           =   1800
   End
   Begin VB.Label lblSalesTaxRate 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Sales Tax @16% :"
      Height          =   210
      Left            =   8985
      TabIndex        =   37
      Top             =   7845
      Width           =   1395
   End
   Begin VB.Label lblPaymentType 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Payment Type:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   15.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   360
      Left            =   60
      TabIndex        =   36
      Top             =   7005
      Width           =   2340
   End
   Begin VB.Label lblCustomerAmount 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Customer:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   480
      Left            =   10365
      TabIndex        =   35
      Top             =   8175
      Width           =   2025
   End
   Begin VB.Label lblChangeAmount 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Change:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   480
      Left            =   10815
      TabIndex        =   34
      Top             =   8805
      Width           =   1575
   End
   Begin VB.Label lblTransactionDate 
      BackStyle       =   0  'Transparent
      Caption         =   "Date:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   375
      Left            =   10650
      TabIndex        =   33
      Top             =   9645
      Width           =   2535
   End
   Begin VB.Label Label5 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Tables:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   15.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   360
      Left            =   12495
      TabIndex        =   32
      Top             =   1710
      Width           =   1260
   End
   Begin VB.Label Label6 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Pavilian End Club"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   26.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H00E269A6&
      Height          =   615
      Left            =   5940
      TabIndex        =   44
      Top             =   285
      Width           =   4845
   End
   Begin VB.Label Label4 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Pavilian End Club"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   26.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H00404040&
      Height          =   615
      Left            =   5910
      TabIndex        =   43
      Top             =   300
      Width           =   4845
   End
End
Attribute VB_Name = "frmPOSPavilianEndClub"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
Dim MemberCode As String
Dim CardNumber As String
Dim DiscountCode As String
Dim DiscountAmount As String
Dim IsPercentage As Boolean
Dim TransactionNo As String
Dim SalesTaxRate As String
Dim DockItPrints As Integer
Dim EmployeeCode As String
Dim TableCode As String
Dim PaymentType As String
Dim LastBillingDate As String
Dim MemberCreditLimit As String

Private Sub cmdBrowser_Click()
        '<EhHeader>
        On Error GoTo cmdBrowser_Click_Err
        '</EhHeader>

100     DataBrowserMode = 4
102     frmDataBrowser.Show 1
104     txtMemberCode_Change

        '<EhFooter>
        Exit Sub

cmdBrowser_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmdBrowser_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdClear_Click()
        '<EhHeader>
        On Error GoTo cmdClear_Click_Err
        '</EhHeader>
    
100     txtMemberCode.Text = ""
102     txtCardNumber.Text = ""
104     txtMemberName.Text = ""
106     txtBalanceAmount.Text = 0
108     PicMemberPhoto.Picture = LoadPicture("")
    
        '<EhFooter>
        Exit Sub

cmdClear_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmdClear_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmbDiscount_Click()
        '<EhHeader>
        On Error GoTo cmbDiscount_Click_Err
        '</EhHeader>
    
100     DiscountCode = Format(cmbDiscount.ItemData(cmbDiscount.ListIndex), "0##")
    
102     sSQL = "Select * From DiscountSetup Where DiscountCode='" & DiscountCode & "'"
104     Set rs = New ADODB.Recordset
106     rs.Open sSQL, Conn, 1, 3
    
108     IsPercentage = rs("IsPercentage")
110     DiscountAmount = rs("DiscountAmount")
                
112     CalculateBill
        
        '<EhFooter>
        Exit Sub

cmbDiscount_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmbDiscount_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmbEmployees_Click()
        '<EhHeader>
        On Error GoTo cmbEmployees_Click_Err
        '</EhHeader>
    
100     EmployeeCode = Format(cmbEmployees.ItemData(cmbEmployees.ListIndex), "0###")
    
        '<EhFooter>
        Exit Sub

cmbEmployees_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmbEmployees_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmbPaymentType_Click()
        '<EhHeader>
        On Error GoTo cmbPaymentType_Click_Err
        '</EhHeader>
    
100     If cmbPaymentType.Text = "Cash" Then

102         DockItPrints = 1
        Else

104         DockItPrints = 2
        End If
  
106     DockItPrints = 2

108     If cmbPaymentType.Text = "Credit A/C" Then
110         Call cmdClear_Click
112         fraMemberInformation.Visible = True
        Else
114         Call cmdClear_Click
116         fraMemberInformation.Visible = False
        End If
        
        '<EhFooter>
        Exit Sub

cmbPaymentType_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmbPaymentType_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmbTables_Click()
        '<EhHeader>
        On Error GoTo cmbTables_Click_Err
        '</EhHeader>

100     TableCode = Format(cmbTables.ItemData(cmbTables.ListIndex), "0###")

        '<EhFooter>
        Exit Sub

cmbTables_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmbTables_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdClose_Click()
        '<EhHeader>
        On Error GoTo cmdClose_Click_Err
        '</EhHeader>
    
100     Unload Me
    
        '<EhFooter>
        Exit Sub

cmdClose_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmdClose_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdFree_Click(Index As Integer)
        '<EhHeader>
        On Error GoTo cmdFree_Click_Err
        '</EhHeader>
        
100     vaSpread2.Row = vaSpread2.ActiveRow
102     vaSpread2.Col = 4
104     vaSpread2.Text = 0
106     CalculateBill
        
        '<EhFooter>
        Exit Sub

cmdFree_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmdFree_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdRead_Click()
        '<EhHeader>
        On Error GoTo cmdRead_Click_Err
        '</EhHeader>
    
100     MSComm1.PortOpen = True

        '<EhFooter>
        Exit Sub

cmdRead_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmdRead_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdPrintAgain_Click()
        '<EhHeader>
        On Error GoTo cmdPrintAgain_Click_Err
        '</EhHeader>

100     If vaSpread2.DataRowCnt = 0 Then
102         MsgBox "There is no data for print or preview", vbOKOnly + vbCritical, "Error!"
            Exit Sub
        End If

104     sSQL = "SELECT Transactions.*, ItemPrice * ItemQuantity AS ItemTotal From Transactions " & _
           "WHERE TransactionNo='" & TransactionNo & "' AND TransactionDate='" & TransactionDate & "'"
106     Set rs = New ADODB.Recordset
108     rs.Open sSQL, Conn, 1, 3
        
110     With repDockitPavilianEndClub
112         .Restart
114         .documentName = "Dockit Print"
116         .DataControl1.Recordset = rs
118         .WindowState = vbMaximized
            '204             .Show 1
120         .PrintReport False
        End With

        '<EhFooter>
        Exit Sub

cmdPrintAgain_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmdPrintAgain_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdStart_Click()
        '<EhHeader>
        On Error GoTo cmdStart_Click_Err
        '</EhHeader>
                   
100     POS_Lock False

101     vaSpread1.MaxRows = 0
102     vaSpread1.MaxRows = 25

104     vaSpread2.MaxRows = 0
106     vaSpread2.MaxRows = 20

108     Call cmdClear_Click

110     sSQL = "Select Max(TransactionNo) as LastNumber  From Transactions Where TransactionDate='" & TransactionDate & "'"
112     Set rs = New ADODB.Recordset
114     rs.Open sSQL, Conn, 1, 3
        
116     If rs.EOF = True Then
118         TransactionNo = 1
        Else
120         TransactionNo = Val(rs("LastNumber") & "") + 1
        End If
        
122     TransactionNo = Format(TransactionNo, "0###")
        
124     IsPercentage = True
126     DiscountAmount = 0
        
128     MsgBox "Transaction No.: " & TransactionNo
        
130     cmdItems_Click 4
        
        '<EhFooter>
        Exit Sub

cmdStart_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmdStart_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdPayments_Click()
        '<EhHeader>
        On Error GoTo cmdPayments_Click_Err
        '</EhHeader>
                       
100     If cmbPaymentType.Text = "Credit A/C" Then
102         If Len(txtMemberCode.Text) < 8 Then
            
104             MsgBox "Please Select the Member form the Memer List.", vbOKOnly + vbCritical, "Error!"
                Exit Sub
        
            End If
                               
106         If Len(txtMemberName.Text) = 0 Then
            
108             MsgBox "Please Select the Member form the Memer List.", vbOKOnly + vbCritical, "Error!"
                Exit Sub
        
            End If
            
110         If txtBalanceAmount.Value > 0 Or txtBalanceAmount.Value > txtPayableAmount.Value Then
112             MsgBox "There is no credit limit avalible for this memeber.", vbOKOnly + vbCritical, "Error!"
                Exit Sub
            End If
           
        End If
                       
114     If Len(TransactionNo) = 0 Then
116         MsgBox "Invalid Transaction Number Please try to input again", vbOKOnly + vbCritical, "Error!"
118         POS_Lock True
            Exit Sub
        End If
                       
120     sSQL = "Delete From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
122     Conn.Execute sSQL

124     sSQL = "Select * From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
126     Debug.Print sSQL
128     Set rs = New ADODB.Recordset
130     rs.Open sSQL, Conn, 1, 3
        
132     For i = 1 To vaSpread2.DataRowCnt
134         rs.AddNew
136         rs("TransactionNo") = TransactionNo
138         rs("TransactionDate") = TransactionDate
140         rs("Customers") = txtCustomers.Text
142         rs("PaymentType") = cmbPaymentType.Text
144         rs("TransactionStartTime") = Time
146         rs("TransactionEndTime") = Time
148         rs("TransactionType") = "DineIn"
150         rs("TableNumber") = cmbTables.Text
            
152         If cmbPaymentType.Text = "Credit A/C" Then
154             rs("EmployeeCode") = EmployeeCode
156             rs("EmployeeName") = cmbEmployees.Text
            End If

158         rs("UserID") = UserID
160         vaSpread2.Row = i
162         vaSpread2.Col = 1
164         rs("ItemCode") = vaSpread2.Text
166         vaSpread2.Col = 2
168         rs("ItemName") = vaSpread2.Text
170         vaSpread2.Col = 3
172         rs("ItemPrice") = vaSpread2.Value
174         vaSpread2.Col = 4

176         If DiscountAmount <> 100 Then
178             rs("ItemQuantity") = vaSpread2.Value
            Else
180             rs("ItemQuantity") = 0
            End If

182         rs("DiscountCode") = DiscountCode
184         rs("DiscountAmount") = txtDiscount.Text
186         rs("TaxRate") = SalesTaxRate
188         rs("TaxAmount") = txtSalesTaxAmount.Text
190         rs("TransactionClose") = True
192         rs("TransactionsVoid") = False
194         rs("VoidDescription") = ""

196         If Len(Trim$(txtMemberCode.Text)) <> 0 Then
198             rs("MemberCode") = txtMemberCode.Text
            End If

200         If Len(Trim$(txtCardNumber.Text)) <> 0 Then
202             rs("CardNumber") = txtCardNumber.Text
            End If
            
204         If Len(Trim$(txtMemberName.Text)) <> 0 Then
206             rs("MemberName") = txtMemberName.Text
            End If

208         rs.Update
        Next
        
210     For i = 1 To DockItPrints
        
212         sSQL = "SELECT Transactions.*, ItemPrice * ItemQuantity AS ItemTotal From Transactions " & _
               "WHERE TransactionNo='" & TransactionNo & "' AND TransactionDate='" & TransactionDate & "'"
214         Set rs = New ADODB.Recordset
216         rs.Open sSQL, Conn, 1, 3
        
218         With repDockitPavilianEndClub
220             .Restart
222             .documentName = "Dockit Print"
224             .DataControl1.Recordset = rs
226             .WindowState = vbMaximized
                '204             .Show 1
228             .PrintReport False
            End With

        Next
        
230     Call cmdClear_Click
232     Call LoadOpenDockits
        
        ''        Exit Sub
        ''206     Printer.FontName = "FontControl"
        ''207     Printer.Print "A"
        ''208     Printer.EndDoc
        ''
        ''209     Printer.FontName = "Control"
        ''210     Printer.Print "A"
        ''211     Printer.EndDoc
        ''
        ''212     MSComm1.Output = "Open Drawer"
        ''214     MSComm1.Output = "Open Drawer"
        ''216     MSComm1.Output = "Open Drawer"
        
234     POS_Lock True
        
        '<EhFooter>
        Exit Sub

cmdPayments_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmdPayments_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>
    
100     CenterForm Me
102     Me.WindowState = vbMaximized

104     vaSpread2.Col = 8
106     vaSpread2.Row = -1
108     vaSpread2.Formula = "C#*" & (Val(SalesTaxRate) / 100)
110     lblSalesTaxRate.Caption = "Sales Tax @" & SalesTaxRate & "%:"

112     TransactionDate = Format(Date, "dd/mmm/yyyy")
114     lblTransactionDate.Caption = "Date: " & TransactionDate
                
116     txtTransactionNo.Text = ""
118     txtTransactionDate.Text = ""

120     If UserID = "Shahid" Then
122         txtTransactionNo.Visible = True
124         txtTransactionDate.Visible = True
        Else
126         txtTransactionNo.Visible = False
128         txtTransactionDate.Visible = False
        End If
                
130     cmbPaymentType.ListIndex = 0
                
132     MSComm1.PortOpen = True
        
134     POS_Lock True

136     sSQL = "SELECT MAX(BillIssueDate) AS FromDate From MemberTransactions WHERE (MemberPayment = 0)"
138     Set rs = New ADODB.Recordset
140     rs.Open sSQL, Conn, 1, 3
        
142     LastBillingDate = Format(rs("FromDate"), "dd/mmm/yyyy")
        
144     sSQL = "Select * From TaxesSetup Where TaxesCode='001'"
146     Set rs = New ADODB.Recordset
148     rs.Open sSQL, Conn, 1, 3
        
150     SalesTaxRate = rs("TaxesAmount")
        
152     Call FillcmbTables
154     Call FillcmbEmployees
156     Call FillcmbDiscount
158     Call LoadOpenDockits
        
        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdItems_Click(Index As Integer)
        '<EhHeader>
        On Error GoTo cmdItems_Click_Err
        '</EhHeader>
    
100     sSQL = "Select * From ItemSetup Where CatagoryCode='" & Format(cmdItems(Index).Tag, "0###") & "' AND IsActive=1"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
        
106     vaSpread1.MaxRows = 0
108     vaSpread1.MaxRows = 1
110     vaSpread1.Row = 1
        'vaSpread1.RowHeight(vaSpread1.Row) = 20

112     Do Until rs.EOF
114         vaSpread1.Col = 0
116         vaSpread1.Text = rs("ItemCode")
118         vaSpread1.Col = 1
120         vaSpread1.Text = rs("ItemName")
122         vaSpread1.Col = 2
124         vaSpread1.Text = rs("Price")
126         rs.MoveNext
128         vaSpread1.MaxRows = vaSpread1.MaxRows + 1
130         vaSpread1.Row = vaSpread1.Row + 1
            'vaSpread1.RowHeight(vaSpread1.Row) = 20
        Loop

132     vaSpread1.MaxRows = vaSpread1.DataRowCnt
        
        '<EhFooter>
        Exit Sub

cmdItems_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmdItems_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdSave_Click()
        '<EhHeader>
        On Error GoTo cmdSave_Click_Err
        '</EhHeader>

100     If cmbPaymentType.Text = "Credit A/C" Then
102         If Len(txtMemberCode.Text) < 8 Then
            
104             MsgBox "Please Select the Member form the Memer List.", vbOKOnly + vbCritical, "Error!"
                Exit Sub
        
            End If
                               
106         If Len(txtMemberName.Text) = 0 Then
            
108             MsgBox "Please Select the Member form the Memer List.", vbOKOnly + vbCritical, "Error!"
                Exit Sub
        
            End If
            
110         If txtBalanceAmount.Value > 0 Or txtBalanceAmount.Value > txtPayableAmount.Value Then
112             MsgBox "There is no credit limit avalible for this memeber.", vbOKOnly + vbCritical, "Error!"
                Exit Sub
            End If
           
        End If
                       
114     If Len(TransactionNo) = 0 Then
116         MsgBox "Invalid Transaction Number Please try to input again", vbOKOnly + vbCritical, "Error!"
118         POS_Lock True
            Exit Sub
        End If
                
120     sSQL = "Delete From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
122     Conn.Execute sSQL

124     sSQL = "Select * From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
126     Debug.Print sSQL
128     Set rs = New ADODB.Recordset
130     rs.Open sSQL, Conn, 1, 3
        
132     For i = 1 To vaSpread2.DataRowCnt
134         rs.AddNew
136         rs("TransactionNo") = TransactionNo
138         rs("TransactionDate") = TransactionDate
140         rs("TransactionStartTime") = Time
142         rs("TransactionType") = "DineIn"
144         rs("TableNumber") = cmbTables.Text
            
146         rs("PaymentType") = cmbPaymentType.Text

148         If cmbPaymentType.Text = "Credit A/C" Then
150             rs("EmployeeCode") = EmployeeCode
152             rs("EmployeeName") = cmbEmployees.Text
            End If

154         rs("UserID") = UserID
156         vaSpread2.Row = i
158         vaSpread2.Col = 1
160         rs("ItemCode") = vaSpread2.Text
162         vaSpread2.Col = 2
164         rs("ItemName") = vaSpread2.Text
166         vaSpread2.Col = 3
168         rs("ItemPrice") = vaSpread2.Value
170         vaSpread2.Col = 4

172         If DiscountAmount <> 100 Then
174             rs("ItemQuantity") = vaSpread2.Value
            Else
176             rs("ItemQuantity") = 0
            End If

178         rs("DiscountCode") = DiscountCode
180         rs("DiscountAmount") = txtDiscount.Text
182         rs("TaxRate") = SalesTaxRate
184         rs("TaxAmount") = txtSalesTaxAmount.Text
186         rs("TransactionClose") = False
188         rs("TransactionsVoid") = False
190         rs("VoidDescription") = ""

192         If Len(Trim$(txtMemberCode.Text)) <> 0 Then
194             rs("MemberCode") = txtMemberCode.Text
            End If

196         If Len(Trim$(txtCardNumber.Text)) <> 0 Then
198             rs("CardNumber") = txtCardNumber.Text
            End If
            
200         If Len(Trim$(txtMemberName.Text)) <> 0 Then
202             rs("MemberName") = txtMemberName.Text
            End If
                        
204         rs.Update
        Next
        
        'cmdClear_Click
        
206     Call LoadOpenDockits
        
208     POS_Lock True

        '<EhFooter>
        Exit Sub

cmdSave_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.cmdSave_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub MSComm1_OnComm()
        '<EhHeader>
        On Error GoTo MSComm1_OnComm_Err
        '</EhHeader>
        Dim iCounter As Integer
        
        Do
100         CardNumber = CardNumber & MSComm1.Input

102         DoEvents
104         iCounter = iCounter + 1

106         If iCounter > 300 Then Exit Do
108     Loop Until InStr(CardNumber, Chr(13))

110     Debug.Print CardNumber
        
112     txtCardNumber.Text = Left(CardNumber, 14)
        
114     CardNumber = ""
    
        '<EhFooter>
        Exit Sub

MSComm1_OnComm_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.MSComm1_OnComm " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtCardNumber_Change()
        '<EhHeader>
        On Error GoTo txtCardNumber_Change_Err
        '</EhHeader>
        
100     If Len(Trim(txtCardNumber.Text)) = 0 Then Exit Sub
        Dim rsMember As New ADODB.Recordset
102     sSQL = "SELECT MemberCode, MemberName, CardNumber, Picture, CreditLimit  From MemberInformation WHERE CardNumber='" & txtCardNumber.Text & "'"
104     Set rsMember = New ADODB.Recordset
106     rsMember.Open sSQL, Conn, 1, 3
        
108     MemberCreditLimit = 0
110     txtCardNumber.Text = ""
112     txtMemberName.Text = ""
114     txtBalanceAmount.Text = 0
116     PicMemberPhoto.Picture = LoadPicture("")

118     If rsMember.EOF = False Then
120         txtCardNumber.Text = rsMember("CardNumber")
122         txtMemberCode.Text = rsMember("MemberCode")
124         txtMemberName.Text = rsMember("MemberName")
126         PicMemberPhoto.DataField = "Picture"
128         Set PicMemberPhoto.DataSource = rsMember
130         MemberCreditLimit = rsMember("CreditLimit")
132         txtBalanceAmount.Text = MemberCreditLimit - GetBalancePayments(txtMemberCode.Text)
        End If
    
        '<EhFooter>
        Exit Sub

txtCardNumber_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.txtCardNumber_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Function GetBalancePayments(strMemberCode As String) As String
        '<EhHeader>
        On Error GoTo GetBalancePayments_Err
        '</EhHeader>
        
        Dim strFromDate As String
        Dim strToDate As String
        
        Dim ChargesAmount As String
        Dim PaidAmount As String
        
        Dim rsPrev As New ADODB.Recordset
        
100     strFromDate = Format(LastBillingDate, "dd/mmm/yyyy")
102     strToDate = Format(Date, "dd/mmm/yyyy")
        
104     sSQL = "SELECT SUM(ChargesAmount) AS ChargesAmount From MemberTransactions WHERE (BillIssueDate ='" & strFromDate & "') AND (MemberCode='" & strMemberCode & "')"
106     Set rsPrev = New ADODB.Recordset
108     rsPrev.Open sSQL, Conn, 1, 3
        
110     If rsPrev.EOF = True Then
112         ChargesAmount = 0
        Else
114         ChargesAmount = Val(rsPrev("ChargesAmount") & "")
        End If
                    
116     strFromDate = Format(CDate(strFromDate) + 1, "dd/mmm/yyyy")
                
118     sSQL = "SELECT SUM(PaidAmount) AS PaidAmount From MemberTransactions WHERE (BillIssueDate BETWEEN '" & strFromDate & "' AND '" & strToDate & "') AND (MemberCode='" & strMemberCode & "')"
120     Set rsPrev = New ADODB.Recordset
122     rsPrev.Open sSQL, Conn, 1, 3
        
124     If rsPrev.EOF = True Then
126         PaidAmount = 0
        Else
128         PaidAmount = Val(rsPrev("PaidAmount") & "")
        End If
        
130     GetBalancePayments = Val(ChargesAmount) - Val(PaidAmount)

        '<EhFooter>
        Exit Function

GetBalancePayments_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.GetBalancePayments " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Function

Private Sub txtCustomerAmount_Change()
        '<EhHeader>
        On Error GoTo txtCustomerAmount_Change_Err
        '</EhHeader>

100     txtChangeAmount.Value = txtCustomerAmount.Value - txtPayableAmount.Value
    
        '<EhFooter>
        Exit Sub

txtCustomerAmount_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.txtCustomerAmount_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtCustomerAmount_LostFocus()
        '<EhHeader>
        On Error GoTo txtCustomerAmount_LostFocus_Err
        '</EhHeader>

100     txtChangeAmount.Value = txtCustomerAmount.Value - txtPayableAmount.Value

        '<EhFooter>
        Exit Sub

txtCustomerAmount_LostFocus_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.txtCustomerAmount_LostFocus " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtMemberCode_Change()
        '<EhHeader>
        On Error GoTo txtMemberCode_Change_Err
        '</EhHeader>
        
        Dim rsMember As New ADODB.Recordset
100     sSQL = "SELECT MemberCode, MemberName, CardNumber, Picture, CreditLimit From MemberInformation WHERE MemberCode='" & txtMemberCode.Text & "'"
102     Set rsMember = New ADODB.Recordset
104     rsMember.Open sSQL, Conn, 1, 3
        
106     MemberCreditLimit = 0
108     txtCardNumber.Text = ""
110     txtMemberName.Text = ""
112     txtBalanceAmount.Text = 0
114     PicMemberPhoto.Picture = LoadPicture("")

116     If rsMember.EOF = False Then
118         txtCardNumber.Text = rsMember("CardNumber") & ""
120         txtMemberName.Text = rsMember("MemberName") & ""
122         PicMemberPhoto.DataField = "Picture"
124         Set PicMemberPhoto.DataSource = rsMember
126         MemberCreditLimit = rsMember("CreditLimit")
128         txtBalanceAmount.Text = GetBalancePayments(txtMemberCode.Text) - MemberCreditLimit
        End If

        '<EhFooter>
        Exit Sub

txtMemberCode_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.txtMemberCode_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtPayableAmount_Change()
        '<EhHeader>
        On Error GoTo txtPayableAmount_Change_Err
        '</EhHeader>
    
100     Call txtCustomerAmount_Change
    
        '<EhFooter>
        Exit Sub

txtPayableAmount_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.txtPayableAmount_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtTransactionNo_LostFocus()
        '<EhHeader>
        On Error GoTo txtTransactionNo_LostFocus_Err
        '</EhHeader>
        Dim MemberCode As String
        Dim CardNumber As String
        
100     If Len(txtTransactionNo.Text) = 0 Then
102         MsgBox "Invalid Transaction Number ", vbOKOnly + vbCritical, "Error!"
            Exit Sub
        End If

104     txtTransactionNo.Text = Format(txtTransactionNo.Text, "0###")
106     TransactionNo = Format(txtTransactionNo.Text, "0###")
108     TransactionDate = Format(txtTransactionDate.Text, "dd/mmm/yyyy")

110     sSQL = "SELECT Transactions.*, ItemSetup.Price AS Price FROM Transactions INNER JOIN ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode " & _
           "WHERE Transactions.TransactionDate='" & TransactionDate & "' AND Transactions.TransactionNo='" & TransactionNo & "'"
        
112     Debug.Print sSQL

114     Set rs = New ADODB.Recordset
116     rs.Open sSQL, Conn, 1, 3
        
118     vaSpread2.MaxRows = 0
120     vaSpread2.MaxRows = 20
122     vaSpread2.Row = 1

124     Do Until rs.EOF
        
126         txtCustomers.Text = rs("Customers")
128         cmbPaymentType.Text = rs("PaymentType")
130         cmbTables.Text = rs("TableNumber")
132         vaSpread2.Col = 1
134         vaSpread2.Text = rs("ItemCode")
136         vaSpread2.Col = 2
138         vaSpread2.Text = rs("ItemName")
140         vaSpread2.Col = 3
142         vaSpread2.Value = rs("Price")
144         vaSpread2.Col = 4
146         vaSpread2.Value = rs("ItemQuantity")
148         vaSpread2.Row = vaSpread2.Row + 1
150         MemberCode = rs("MemberCode") & ""
152         CardNumber = rs("CardNumber") & ""
154         rs.MoveNext
        Loop
        
156     txtMemberCode.Text = MemberCode
158     txtCardNumber.Text = CardNumber
            
160     txtMemberCode_Change
162     Call CalculateBill

        '<EhFooter>
        Exit Sub

txtTransactionNo_LostFocus_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.txtTransactionNo_LostFocus " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread1_ButtonClicked(ByVal Col As Long, _
                                    ByVal Row As Long, _
                                    ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread1_ButtonClicked_Err
        '</EhHeader>
        
100     vaSpread1.Row = vaSpread1.ActiveRow
102     vaSpread2.Row = vaSpread2.DataRowCnt + 1
104     vaSpread1.Col = 0
106     vaSpread2.Col = 1
108     vaSpread2.Text = vaSpread1.Text
110     vaSpread1.Col = 1
112     vaSpread2.Col = 2
114     vaSpread2.Text = vaSpread1.Text
116     vaSpread1.Col = 2
118     vaSpread2.Col = 3
120     vaSpread2.Text = vaSpread1.Text
122     vaSpread2.Col = 4
124     vaSpread2.Text = 1
126     vaSpread2.Row = vaSpread2.Row + 1
128     vaSpread2.Col = 1
130     vaSpread2.Action = ActionActiveCell
    
132     Call CalculateBill
    
        '<EhFooter>
        Exit Sub

vaSpread1_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.vaSpread1_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread2_ButtonClicked(ByVal Col As Long, _
                                    ByVal Row As Long, _
                                    ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread2_ButtonClicked_Err
        '</EhHeader>
    
100     vaSpread2.Row = vaSpread2.ActiveRow
102     vaSpread2.Action = ActionDeleteRow
        
        '<EhFooter>
        Exit Sub

vaSpread2_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.vaSpread2_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub CalculateBill()
        '<EhHeader>
        On Error GoTo CalculateBill_Err
        '</EhHeader>
    
        Dim TotalAmount As String
        Dim TotalTaxAmount As String

100     For i = 1 To vaSpread2.MaxRows
102         vaSpread2.Row = i
            'vaSpread2.Col = 8
            'TotalTaxAmount = Val(TotalTaxAmount) + Val(vaSpread2.Value & "")
104         vaSpread2.Col = 5
106         TotalAmount = Val(TotalAmount) + Val(vaSpread2.Value & "")
        Next

108     txtTotalAmount.Text = TotalAmount
         
110     If IsPercentage = True Then
112         txtDiscount.Text = Round((txtTotalAmount.Text * DiscountAmount) / 100, 0)
        Else
114         txtDiscount.Text = DiscountAmount
        End If

116     If DiscountAmount = "100" Then
118         txtSalesTaxAmount.Text = 0
120         txtDiscount.Text = txtTotalAmount.Text
        Else
122         txtSalesTaxAmount.Text = Round((TotalAmount * SalesTaxRate) / 100)
        End If

124     txtPayableAmount.Text = Val(txtTotalAmount.Value) - Val(txtDiscount.Value) + Val(txtSalesTaxAmount.Value)
    
        '<EhFooter>
        Exit Sub

CalculateBill_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.CalculateBill " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub FillcmbDiscount()
        '<EhHeader>
        On Error GoTo FillcmbDiscount_Err
        '</EhHeader>
    
100     sSQL = "Select * From DiscountSetup"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     cmbDiscount.Clear

108     Do Until rs.EOF
110         cmbDiscount.AddItem rs("DiscountDescription")
112         cmbDiscount.ItemData(cmbDiscount.NewIndex) = rs("DiscountCode")
114         rs.MoveNext
        Loop
    
116     cmbDiscount.Text = "No Discount"

        '<EhFooter>
        Exit Sub

FillcmbDiscount_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.FillcmbDiscount " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub POS_Lock(strStatus As Boolean)
        '<EhHeader>
        On Error GoTo POS_Lock_Err
        '</EhHeader>

100     strStatus = Not strStatus
        
102     cmdStart.Enabled = Not strStatus
104     cmbTables.Enabled = strStatus
106     txtCustomers.Enabled = strStatus
        
108     cmdItems(0).Enabled = strStatus
110     cmdItems(1).Enabled = strStatus
112     cmdItems(2).Enabled = strStatus
114     cmdItems(3).Enabled = strStatus
116     cmdItems(4).Enabled = strStatus
118     cmdItems(5).Enabled = strStatus
120     cmdItems(6).Enabled = strStatus
122     cmdItems(7).Enabled = strStatus
        
124     vaSpread1.Enabled = strStatus
126     vaSpread2.Enabled = strStatus

128     If strStatus = True Then

130         vaSpread1.MaxRows = 0
132         vaSpread1.MaxRows = 25

134         vaSpread2.MaxRows = 0
136         vaSpread2.MaxRows = 20
        
        End If
        
138     cmdPayments.Enabled = strStatus
140     cmbDiscount.Enabled = strStatus
142     cmdSave.Enabled = strStatus
144     cmdPrintAgain.Enabled = Not strStatus
    
        '<EhFooter>
        Exit Sub

POS_Lock_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.POS_Lock " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread2_LeaveCell(ByVal Col As Long, _
                                ByVal Row As Long, _
                                ByVal NewCol As Long, _
                                ByVal NewRow As Long, _
                                Cancel As Boolean)
        '<EhHeader>
        On Error GoTo vaSpread2_LeaveCell_Err
        '</EhHeader>
        
100     CalculateBill

        '<EhFooter>
        Exit Sub

vaSpread2_LeaveCell_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.vaSpread2_LeaveCell " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub FillcmbEmployees()
        '<EhHeader>
        On Error GoTo FillcmbEmployees_Err
        '</EhHeader>

100     sSQL = "SELECT EmployeeCode, EmployeeName From EmployeeInformation"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     cmbEmployees.Clear

108     Do Until rs.EOF
110         cmbEmployees.AddItem rs("EmployeeName")
112         cmbEmployees.ItemData(cmbEmployees.NewIndex) = rs("EmployeeCode")
114         rs.MoveNext
        Loop

116     cmbEmployees.ListIndex = 0
    
        '<EhFooter>
        Exit Sub

FillcmbEmployees_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.FillcmbEmployees " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub FillcmbTables()
        '<EhHeader>
        On Error GoTo FillcmbTables_Err
        '</EhHeader>

100     sSQL = "Select * From TablesSetup Order by TableDescription"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     cmbTables.Clear

108     Do Until rs.EOF
    
110         cmbTables.AddItem rs("TableDescription")
112         cmbTables.ItemData(cmbTables.NewIndex) = rs("TableCode")
114         rs.MoveNext
        Loop
    
116     If cmbTables.ListCount > 0 Then cmbTables.ListIndex = 0
    
        '<EhFooter>
        Exit Sub

FillcmbTables_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.FillcmbTables " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub LoadOpenDockits()
        '<EhHeader>
        On Error GoTo LoadOpenDockits_Err
        '</EhHeader>
        
100     sSQL = "SELECT DISTINCT TableNumber, TransactionNo, SUM(ItemPrice * ItemQuantity) AS ItemAmount, TaxAmount From Transactions " & _
           "WHERE TransactionDate='" & TransactionDate & "' AND TransactionClose=0 GROUP BY TransactionNo, TaxAmount, TableNumber"
                
102     Debug.Print sSQL
104     Set rs = New ADODB.Recordset
106     rs.Open sSQL, Conn, 1, 3
        
108     vaSpread3.MaxRows = 0
110     vaSpread3.MaxRows = 1
112     vaSpread3.Row = 1

114     Do Until rs.EOF
116         vaSpread3.Col = 1
118         vaSpread3.Text = rs("TableNumber")
120         vaSpread3.Col = 2
122         vaSpread3.Text = Val(rs("ItemAmount"))
124         vaSpread3.Col = 3
126         vaSpread3.Text = rs("TransactionNo")
128         vaSpread3.MaxRows = vaSpread3.MaxRows + 1
130         vaSpread3.Row = vaSpread3.Row + 1
132         rs.MoveNext
        Loop

134     vaSpread3.MaxRows = vaSpread3.DataRowCnt

        '<EhFooter>
        Exit Sub

LoadOpenDockits_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.LoadOpenDockits " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread3_Click(ByVal Col As Long, _
                            ByVal Row As Long)
        '<EhHeader>
        On Error GoTo vaSpread3_Click_Err
        '</EhHeader>
    
        Dim strTransactionNo As String

100     vaSpread3.Col = 3
102     vaSpread3.Row = Row
104     strTransactionNo = vaSpread3.Text
106     sSQL = "SELECT Transactions.*, ItemSetup.Price AS Price FROM Transactions INNER JOIN ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode " & _
           "WHERE Transactions.TransactionDate='" & TransactionDate & "' AND Transactions.TransactionNo='" & strTransactionNo & "'"
        
108     Debug.Print sSQL

110     Set rs = New ADODB.Recordset
112     rs.Open sSQL, Conn, 1, 3
        
114     vaSpread2.MaxRows = 0
116     vaSpread2.MaxRows = 20
118     vaSpread2.Row = 1

120     Do Until rs.EOF
        
122         TransactionNo = strTransactionNo
124         txtCustomers.Text = rs("Customers")
126         cmbPaymentType.Text = rs("PaymentType")
128         cmbTables.Text = rs("TableNumber")
130         vaSpread2.Col = 1
132         vaSpread2.Text = rs("ItemCode")
134         vaSpread2.Col = 2
136         vaSpread2.Text = rs("ItemName")
138         vaSpread2.Col = 3
140         vaSpread2.Value = rs("Price")
142         vaSpread2.Col = 4
144         vaSpread2.Value = rs("ItemQuantity")
146         vaSpread2.Row = vaSpread2.Row + 1
148         PaymentType = rs("PaymentType")
150         MemberCode = rs("MemberCode") & ""
152         CardNumber = rs("CardNumber") & ""
154         rs.MoveNext
        Loop
        
156     cmbPaymentType.Text = PaymentType
158     txtMemberCode.Text = MemberCode
160     txtCardNumber.Text = CardNumber
        
162     txtMemberCode_Change
164     Call CalculateBill

166     cmdPayments.Enabled = True
168     cmdSave.Enabled = True

        '<EhFooter>
        Exit Sub

vaSpread3_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSPavilianEndClub.vaSpread3_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub


