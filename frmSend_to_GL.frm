VERSION 5.00
Begin VB.Form frmSend_to_GL 
   Caption         =   "Send to GL"
   ClientHeight    =   4800
   ClientLeft      =   3810
   ClientTop       =   4830
   ClientWidth     =   8550
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmSend_to_GL.frx":0000
   LinkTopic       =   "Form1"
   ScaleHeight     =   4800
   ScaleWidth      =   8550
End
Attribute VB_Name = "frmSend_to_GL"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

'''SELECT     MemberCode, TransactionNo, SUM(ChargesAmount) AS SumBillAmount
'''From MemberTransactions
'''WHERE     (ChargesCode <> N'003')
'''GROUP BY BillIssueDate, MemberCode, TransactionNo
'''HAVING      (BillIssueDate = '30/Apr/2011') AND (SUM(ChargesAmount) IS NOT NULL) AND (SUM(ChargesAmount) <> 0)
'''ORDER BY MemberCode
