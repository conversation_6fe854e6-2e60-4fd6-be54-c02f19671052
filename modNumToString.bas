Attribute VB_Name = "ModNumToString"
Option Explicit
'************ Code Start **********
'This code was originally written by <PERSON>.
'It is not to be altered or distributed,
'except as part of an application.
'You are free to use it in any application,
'provided the copyright notice is left unchanged.
'
'Code Courtesy of
'<PERSON>
'
'Convert a currency value into an (American) English string
Function English(ByVal N As Currency) As String
        '<EhHeader>
        On Error GoTo English_Err
        '</EhHeader>
        Const Thousand = 1000@
        Const Million = Thousand * Thousand
        Const Billion = Thousand * Million
        Const Trillion = Thousand * Billion

100     If (N = 0@) Then English = "zero": Exit Function

        Dim Buf As String: If (N < 0@) Then Buf = "negative " Else Buf = ""

        Dim Frac As Currency: Frac = Abs(N - Fix(N))

102     If (N < 0@ Or Frac <> 0@) Then N = Abs(Fix(N))

        Dim AtLeastOne As Integer: AtLeastOne = N >= 1

104     If (N >= Trillion) Then

106         Debug.Print N
108         Buf = Buf & EnglishDigitGroup(Int(N / Trillion)) & " trillion"
110         N = N - Int(N / Trillion) * Trillion ' Mod overflows

112         If (N >= 1@) Then Buf = Buf & " "

        End If
    
114     If (N >= Billion) Then

116         Debug.Print N
118         Buf = Buf & EnglishDigitGroup(Int(N / Billion)) & " billion"
120         N = N - Int(N / Billion) * Billion ' Mod still overflows

122         If (N >= 1@) Then Buf = Buf & " "

        End If

124     If (N >= Million) Then

126         Debug.Print N
128         Buf = Buf & EnglishDigitGroup(N \ Million) & " million"
130         N = N Mod Million

132         If (N >= 1@) Then Buf = Buf & " "

        End If

134     If (N >= Thousand) Then

136         Debug.Print N
138         Buf = Buf & EnglishDigitGroup(N \ Thousand) & " thousand"
140         N = N Mod Thousand

142         If (N >= 1@) Then Buf = Buf & " "

        End If

144     If (N >= 1@) Then

146         Debug.Print N
148         Buf = Buf & EnglishDigitGroup(N)

        End If

150     If (Frac = 0@) Then

152         Buf = Buf & " exactly"

154     ElseIf (Int(Frac * 100@) = Frac * 100@) Then

156         If AtLeastOne Then Buf = Buf & " and "

158         Buf = Buf & Format$(Frac * 100@, "00") & "/100"

        Else

160         If AtLeastOne Then Buf = Buf & " and "

162         Buf = Buf & Format$(Frac * 10000@, "0000") & "/10000"

        End If

164     English = Buf
        '<EhFooter>
        Exit Function

English_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Accounts.ModNumToString.English " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Function

' Support function to be used only by English()
Private Function EnglishDigitGroup(ByVal N As Integer) As String
        '<EhHeader>
        On Error GoTo EnglishDigitGroup_Err
        '</EhHeader>
        Const Hundred = " hundred"
        Const One = "one"
        Const Two = "two"
        Const Three = "three"
        Const Four = "four"
        Const Five = "five"
        Const Six = "six"
        Const Seven = "seven"
        Const Eight = "eight"
        Const Nine = "nine"
        Dim Buf As String: Buf = ""
        Dim Flag As Integer: Flag = False

        'Do hundreds

100     Select Case (N \ 100)

            Case 0: Buf = "":  Flag = False

102         Case 1: Buf = One & Hundred: Flag = True

104         Case 2: Buf = Two & Hundred: Flag = True

106         Case 3: Buf = Three & Hundred: Flag = True

108         Case 4: Buf = Four & Hundred: Flag = True

110         Case 5: Buf = Five & Hundred: Flag = True

112         Case 6: Buf = Six & Hundred: Flag = True

114         Case 7: Buf = Seven & Hundred: Flag = True

116         Case 8: Buf = Eight & Hundred: Flag = True

118         Case 9: Buf = Nine & Hundred: Flag = True

        End Select
   
120     If (Flag <> False) Then N = N Mod 100

122     If (N > 0) Then

124         If (Flag <> False) Then Buf = Buf & " "

        Else

126         EnglishDigitGroup = Buf
            Exit Function

        End If
      
        'Do tens (except teens)

128     Select Case (N \ 10)

            Case 0, 1: Flag = False

130         Case 2: Buf = Buf & "twenty": Flag = True

132         Case 3: Buf = Buf & "thirty": Flag = True

134         Case 4: Buf = Buf & "forty": Flag = True

136         Case 5: Buf = Buf & "fifty": Flag = True

138         Case 6: Buf = Buf & "sixty": Flag = True

140         Case 7: Buf = Buf & "seventy": Flag = True

142         Case 8: Buf = Buf & "eighty": Flag = True

144         Case 9: Buf = Buf & "ninety": Flag = True

        End Select
   
146     If (Flag <> False) Then N = N Mod 10

148     If (N > 0) Then

150         If (Flag <> False) Then Buf = Buf & "-"

        Else

152         EnglishDigitGroup = Buf
            Exit Function

        End If
    
        'Do ones and teens

154     Select Case (N)

            Case 0: ' do nothing

156         Case 1: Buf = Buf & One

158         Case 2: Buf = Buf & Two

160         Case 3: Buf = Buf & Three

162         Case 4: Buf = Buf & Four

164         Case 5: Buf = Buf & Five

166         Case 6: Buf = Buf & Six

168         Case 7: Buf = Buf & Seven

170         Case 8: Buf = Buf & Eight

172         Case 9: Buf = Buf & Nine

174         Case 10: Buf = Buf & "ten"

176         Case 11: Buf = Buf & "eleven"

178         Case 12: Buf = Buf & "twelve"

180         Case 13: Buf = Buf & "thirteen"

182         Case 14: Buf = Buf & "fourteen"

184         Case 15: Buf = Buf & "fifteen"

186         Case 16: Buf = Buf & "sixteen"

188         Case 17: Buf = Buf & "seventeen"

190         Case 18: Buf = Buf & "eighteen"

192         Case 19: Buf = Buf & "nineteen"

        End Select

194     EnglishDigitGroup = Buf
        '<EhFooter>
        Exit Function

EnglishDigitGroup_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Accounts.ModNumToString.EnglishDigitGroup " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Function
'************ Code End **********



