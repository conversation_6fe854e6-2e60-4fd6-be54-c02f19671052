VERSION 5.00
Object = "{BDC217C8-ED16-11CD-956C-0000C04E4C0A}#1.1#0"; "TABCTL32.OCX"
Object = "{7888C00A-4808-4D27-9AAE-BD36EC13D16F}#1.0#0"; "LVbutton.ocx"
Begin VB.Form frmSettings 
   AutoRedraw      =   -1  'True
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "POS Settings"
   ClientHeight    =   4170
   ClientLeft      =   3180
   ClientTop       =   5475
   ClientWidth     =   6390
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmSettings.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   4170
   ScaleWidth      =   6390
   ShowInTaskbar   =   0   'False
   Begin LVbuttons.LaVolpeButton cmdCancel 
      Height          =   375
      Left            =   5130
      TabIndex        =   3
      Top             =   3735
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   661
      BTYPE           =   3
      TX              =   "Cancel"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   1
      BCOL            =   14215660
      FCOL            =   0
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmSettings.frx":000C
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
   Begin TabDlg.SSTab SSTTab0 
      Height          =   3240
      Left            =   45
      TabIndex        =   0
      Top             =   420
      Width           =   6300
      _ExtentX        =   11113
      _ExtentY        =   5715
      _Version        =   393216
      Style           =   1
      TabHeight       =   520
      TabCaption(0)   =   "Dockit"
      TabPicture(0)   =   "frmSettings.frx":0028
      Tab(0).ControlEnabled=   -1  'True
      Tab(0).ControlCount=   0
      TabCaption(1)   =   "Printer"
      TabPicture(1)   =   "frmSettings.frx":0044
      Tab(1).ControlEnabled=   0   'False
      Tab(1).Control(0)=   "cmbPrinterType"
      Tab(1).Control(1)=   "lblPrinterType"
      Tab(1).ControlCount=   2
      TabCaption(2)   =   "Others"
      TabPicture(2)   =   "frmSettings.frx":0060
      Tab(2).ControlEnabled=   0   'False
      Tab(2).ControlCount=   0
      Begin VB.ComboBox cmbPrinterType 
         Height          =   315
         ItemData        =   "frmSettings.frx":007C
         Left            =   -73365
         List            =   "frmSettings.frx":0089
         Style           =   2  'Dropdown List
         TabIndex        =   2
         Top             =   675
         Width           =   3150
      End
      Begin VB.Label lblPrinterType 
         AutoSize        =   -1  'True
         Caption         =   "Printer Type:"
         Height          =   195
         Left            =   -74700
         TabIndex        =   1
         Top             =   720
         Width           =   1125
      End
   End
   Begin LVbuttons.LaVolpeButton cmdSave 
      Cancel          =   -1  'True
      Height          =   375
      Left            =   3855
      TabIndex        =   4
      Top             =   3735
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   661
      BTYPE           =   3
      TX              =   "Save"
      ENAB            =   -1  'True
      BeginProperty FONT {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      COLTYPE         =   1
      BCOL            =   14215660
      FCOL            =   0
      FCOLO           =   0
      EMBOSSM         =   12632256
      EMBOSSS         =   16777215
      MPTR            =   0
      MICON           =   "frmSettings.frx":00C4
      ALIGN           =   1
      IMGLST          =   "(None)"
      IMGICON         =   "(None)"
      ICONAlign       =   0
      ORIENT          =   0
      STYLE           =   0
      IconSize        =   2
      SHOWF           =   -1  'True
      BSTYLE          =   0
   End
End
Attribute VB_Name = "frmSettings"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub cmdSave_Click()
    Dim PrinterType As String
    
    Dim DockitsPrintCount As String
    Dim DockitsPrintCompanyInfo As String
    
    SaveSetting App.EXEName, "Settings", "Printer Type", "Thermal Printer"
    
End Sub
