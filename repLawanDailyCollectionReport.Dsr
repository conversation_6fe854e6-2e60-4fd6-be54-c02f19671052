VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} repLawanDailyCollectionReport 
   Caption         =   "Point_of_Sale_System - repLawanDailyCollectionReport (ActiveReport)"
   ClientHeight    =   8670
   ClientLeft      =   -2070
   ClientTop       =   1995
   ClientWidth     =   15420
   _ExtentX        =   27199
   _ExtentY        =   15293
   SectionData     =   "repLawanDailyCollectionReport.dsx":0000
End
Attribute VB_Name = "repLawanDailyCollectionReport"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub BookingTypeHeader_Format()
    If txtTransactionType.Text = "LP" Then
        txtTransactionType3 = "Lawn Payments"
    ElseIf txtTransactionType.Text = "DP" Then
        txtTransactionType3 = "Decoration Payments"
    ElseIf txtTransactionType.Text = "CP" Then
        txtTransactionType3 = "Catering Payments"
    End If
End Sub

Private Sub TransactionTypeHeader_Format()
    If txtIsCheque = -1 Then
        txtTransactionType2.Text = "Bank"
    Else
        txtTransactionType2.Text = "Cash"
    End If
    
End Sub
