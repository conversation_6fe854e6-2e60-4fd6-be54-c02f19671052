VERSION 5.00
Object = "{B02F3647-766B-11CE-AF28-C3A2FBE76A13}#3.0#0"; "SPR32X30.ocx"
Begin VB.Form frmUserRights 
   Appearance      =   0  'Flat
   Caption         =   "Make new user"
   ClientHeight    =   5460
   ClientLeft      =   2100
   ClientTop       =   4515
   ClientWidth     =   10755
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmUserRights.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   ScaleHeight     =   5460
   ScaleWidth      =   10755
   Begin VB.Frame Frame1 
      BackColor       =   &H00EAD08E&
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   525
      Left            =   15
      TabIndex        =   0
      Top             =   -90
      Width           =   10740
      Begin VB.Label Label4 
         Alignment       =   2  'Center
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "USERS RIGHTS (Grant && Revoke)"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   15.75
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Height          =   375
         Left            =   3300
         TabIndex        =   1
         Top             =   135
         Width           =   5325
      End
   End
   Begin VB.Frame Frame2 
      BackColor       =   &H00E0E0E0&
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   5070
      Left            =   15
      TabIndex        =   2
      Top             =   375
      Width           =   10740
      Begin VB.Frame Frame4 
         BackColor       =   &H00E0E0E0&
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   120
         Left            =   1230
         TabIndex        =   21
         Top             =   2025
         Width           =   8205
      End
      Begin VB.CommandButton cmdNew 
         Caption         =   "&New"
         Height          =   375
         Left            =   3000
         TabIndex        =   20
         Top             =   1650
         Width           =   870
      End
      Begin VB.CommandButton cmdSave 
         Caption         =   "&Save"
         Height          =   375
         Left            =   4230
         TabIndex        =   19
         Top             =   1650
         Width           =   870
      End
      Begin VB.CommandButton cmdDelete 
         Caption         =   "D&elete"
         Height          =   375
         Left            =   5460
         TabIndex        =   18
         Top             =   1650
         Width           =   870
      End
      Begin VB.CommandButton cmdExit 
         Cancel          =   -1  'True
         Caption         =   "E&xit"
         Height          =   375
         Left            =   6690
         TabIndex        =   17
         Top             =   1650
         Width           =   870
      End
      Begin VB.CheckBox chkChangePassword 
         BackColor       =   &H00E0E0E0&
         Caption         =   "Add New User or Change password "
         Height          =   240
         Left            =   1260
         TabIndex        =   16
         Top             =   2190
         Width           =   3945
      End
      Begin VB.TextBox txtUserName 
         Appearance      =   0  'Flat
         Height          =   285
         Left            =   3090
         MaxLength       =   50
         TabIndex        =   6
         Text            =   "txtUserName"
         Top             =   555
         Width           =   5250
      End
      Begin VB.ComboBox cmbUserName 
         Height          =   315
         Left            =   5145
         Style           =   2  'Dropdown List
         TabIndex        =   13
         TabStop         =   0   'False
         Top             =   180
         Width           =   3195
      End
      Begin VB.Frame Frame3 
         BackColor       =   &H00E0E0E0&
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   120
         Left            =   1230
         TabIndex        =   12
         Top             =   1470
         Width           =   8205
      End
      Begin FPSpread.vaSpread vaSpread1 
         Height          =   2535
         Left            =   45
         TabIndex        =   14
         Top             =   2475
         Width           =   10635
         _Version        =   196608
         _ExtentX        =   18759
         _ExtentY        =   4471
         _StockProps     =   64
         ButtonDrawMode  =   4
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         MaxCols         =   12
         MaxRows         =   8
         ScrollBars      =   2
         SpreadDesigner  =   "frmUserRights.frx":030A
         UserResize      =   2
      End
      Begin VB.TextBox txtConfPassword 
         Appearance      =   0  'Flat
         Height          =   285
         IMEMode         =   3  'DISABLE
         Left            =   6390
         MaxLength       =   15
         PasswordChar    =   "*"
         TabIndex        =   10
         Text            =   "txtConfPassword"
         Top             =   930
         Width           =   1965
      End
      Begin VB.TextBox txtPassword 
         Appearance      =   0  'Flat
         Height          =   285
         IMEMode         =   3  'DISABLE
         Left            =   3090
         MaxLength       =   15
         PasswordChar    =   "*"
         TabIndex        =   8
         Text            =   "txtPassword"
         Top             =   930
         Width           =   1965
      End
      Begin VB.TextBox txtUserID 
         Appearance      =   0  'Flat
         Height          =   285
         Left            =   3090
         MaxLength       =   15
         TabIndex        =   4
         Text            =   "txtUserID"
         Top             =   180
         Width           =   1965
      End
      Begin VB.Label Label8 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "UserName:"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   195
         Left            =   1905
         TabIndex        =   5
         Top             =   600
         Width           =   1065
      End
      Begin VB.Label lblFpMessage 
         Alignment       =   1  'Right Justify
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Height          =   195
         Left            =   10815
         TabIndex        =   15
         Top             =   2490
         Width           =   60
      End
      Begin VB.Label Label7 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "(Password length min 8 max 15 character alpha numeric)"
         Height          =   195
         Left            =   3105
         TabIndex        =   11
         Top             =   1230
         Width           =   4935
      End
      Begin VB.Label Label3 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "CONFIRM:"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   195
         Left            =   5415
         TabIndex        =   9
         Top             =   975
         Width           =   960
         WordWrap        =   -1  'True
      End
      Begin VB.Label Label2 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "PASSWORD:"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   195
         Left            =   1905
         TabIndex        =   7
         Top             =   975
         Width           =   1125
      End
      Begin VB.Label Label1 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "USER ID:"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   195
         Left            =   1905
         TabIndex        =   3
         Top             =   225
         Width           =   840
      End
   End
End
Attribute VB_Name = "frmUserRights"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub cmbUserName_Click()
        '<EhHeader>
        On Error GoTo cmbUserName_Click_Err
        '</EhHeader>

100     txtUserID.Text = cmbUserName.Text
102     txtUserID_LostFocus

        '<EhFooter>
        Exit Sub

cmbUserName_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmUserRights.cmbUserName_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub


Private Sub cmdExit_Click()
        '<EhHeader>
        On Error GoTo cmdExit_Click_Err
        '</EhHeader>

100     Unload Me

        '<EhFooter>
        Exit Sub

cmdExit_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmUserRights.cmdExit_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdNew_Click()
        '<EhHeader>
        On Error GoTo cmdNew_Click_Err
        '</EhHeader>

100     txtUserID.Text = ""
102     txtUserName.Text = ""
104     txtPassword.Text = ""
106     txtConfPassword.Text = ""
108     chkChangePassword.Value = vbUnchecked
110     cmdSave.Enabled = True
112     FillGrid

        '<EhFooter>
        Exit Sub

cmdNew_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmUserRights.cmdNew_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdSave_Click()
        '<EhHeader>
        On Error GoTo cmdSave_Click_Err
        '</EhHeader>
        Dim strHashString As New CSHA256
100     If chkChangePassword.Value = vbChecked Then
    
102         If Len(txtUserID.Text) = 0 Then

104             MsgBox "Please enter the User ID", vbCritical + vbOKOnly
106             txtUserID.SetFocus
                Exit Sub

            End If

108         If Len(txtUserName.Text) = 0 Then

110             MsgBox "Please enter the User Name", vbCritical + vbOKOnly
112             txtUserName.SetFocus
                Exit Sub

            End If

114         If Trim$(txtPassword.Text) <> Trim$(txtConfPassword.Text) Then

116             MsgBox "Password and Confirm Password is not equal please retype your passwords", vbCritical + vbOKOnly, "Error!"
118             txtPassword.SetFocus
                Exit Sub

            End If
        
120         If Len(txtPassword.Text) < 8 Then

122             MsgBox "Password must be eight characters", vbCritical + vbOKOnly, "Error!"
124             txtPassword.SetFocus
                Exit Sub

            End If
        
126         Set rs = New ADODB.Recordset
128         sSQL = "Select * From UserMaster Where UserID='" & txtUserID.Text & "'"
130         rs.Open sSQL, Conn, 1, 3

132         If rs.EOF Then

134             rs.AddNew
136             rs("UserID") = txtUserID.Text

            End If

138         rs("UserName") = txtUserName.Text
140         rs("Password") = strHashString.SHA256("\" & txtPassword.Text & "/")
142         rs.Update
144         rs.Close

        Else

146         For i = 1 To vaSpread1.DataRowCnt

148             vaSpread1.Col = 1
150             vaSpread1.Row = i
152             Set rs = New ADODB.Recordset
154             sSQL = "Select * From UserMaster Where UserID='" & vaSpread1.Text & "'"
156             rs.Open sSQL, Conn, 1, 3
158             vaSpread1.Col = 7
160             rs("AllowEdit") = vaSpread1.Value
162             vaSpread1.Col = 8
164             rs("AllowDelete") = vaSpread1.Value
166             vaSpread1.Col = 9
168             rs("AllowPrint") = vaSpread1.Value
170             vaSpread1.Col = 10
172             rs("DisableUser") = vaSpread1.Value
174             vaSpread1.Col = 11
176             rs("ChangePassword") = vaSpread1.Value
178             rs.Update
180             rs.Close
            
            Next

        End If

182     FillCmbUserName
184     cmdNew_Click

        '<EhFooter>
        Exit Sub

cmdSave_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmUserRights.cmdSave_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>

100     CenterForm Me
102     txtUserID.Text = ""
104     txtUserName.Text = ""
106     txtPassword.Text = ""
108     txtConfPassword.Text = ""
110     Call FillCmbUserName
112     cmdNew_Click

        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmUserRights.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtUserID_LostFocus()
        '<EhHeader>
        On Error GoTo txtUserID_LostFocus_Err
        '</EhHeader>

100     If Len(txtUserID.Text) = 0 Then Exit Sub

102     Set rs = New ADODB.Recordset
104     sSQL = "Select * From UserMaster Where UserID='" & txtUserID.Text & "'"
106     rs.Open sSQL, Conn, 1, 3

108     If rs.EOF Then

110         txtUserName.Text = ""
112         txtPassword.Text = ""
114         txtConfPassword.Text = ""
            Exit Sub

        End If

116     cmdSave.Enabled = UserRights.AllowEdit
118     txtUserName.Text = rs("UserName")
120     txtPassword.Text = rs("Password")
122     txtConfPassword.Text = rs("Password")
124     rs.Close

        '<EhFooter>
        Exit Sub

txtUserID_LostFocus_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmUserRights.txtUserID_LostFocus " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread1_ButtonClicked(ByVal Col As Long, ByVal Row As Long, ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread1_ButtonClicked_Err
        '</EhHeader>
        
        On Error Resume Next
100     If Col = 3 Or Col = 4 Or Col = 5 Or Col = 6 Then
        vaSpread1.Col = 1
        vaSpread1.Row = vaSpread1.ActiveRow
        UserRightsUser = vaSpread1.Text

102         Select Case Col

                Case Is = 3
                
104                 BrowserType = TransactionFrom
106                 frmRightsBrowser.lblHeading.Caption = "Transaction Froms Rights"
                
108             Case Is = 4
110                 BrowserType = Settings
112                 frmRightsBrowser.lblHeading.Caption = "Settings Rights"

114             Case Is = 5
116                 BrowserType = SetupReports
118                 frmRightsBrowser.lblHeading.Caption = "Setup Reports Rights"

120             Case Is = 6
122                 BrowserType = SummaryReports
124                 frmRightsBrowser.lblHeading.Caption = "Summary Reports Rights"

            End Select
            
126         frmRightsBrowser.Show 1

        End If

        '<EhFooter>
        Exit Sub

vaSpread1_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmUserRights.vaSpread1_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub FillCmbUserName()
        '<EhHeader>
        On Error GoTo FillCmbUserName_Err
        '</EhHeader>

100     Set rs = New ADODB.Recordset
102     sSQL = "Select * From UserMaster"
104     rs.Open sSQL, Conn, 1, 3
106     cmbUserName.Clear

108     Do Until rs.EOF

110         cmbUserName.AddItem rs("UserID")
112         rs.MoveNext

        Loop
    
114     If cmbUserName.ListCount > 0 Then

116         cmbUserName.ListIndex = 0

        End If

        '<EhFooter>
        Exit Sub

FillCmbUserName_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmUserRights.FillCmbUserName " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub FillGrid()
        '<EhHeader>
        On Error GoTo FillGrid_Err
        '</EhHeader>

100     sSQL = "Select * from UserMaster Order by UserID"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
106     vaSpread1.MaxRows = 0
108     vaSpread1.MaxRows = 1
110     vaSpread1.Row = 1

112     Do Until rs.EOF

114         vaSpread1.Col = 1
116         vaSpread1.Text = rs("UserID")
118         vaSpread1.Col = 2
120         vaSpread1.Text = rs("UserName")
122         vaSpread1.Col = 7
124         vaSpread1.Value = rs("AllowEdit")
126         vaSpread1.Col = 8
128         vaSpread1.Value = rs("AllowDelete")
130         vaSpread1.Col = 9
132         vaSpread1.Value = rs("AllowPrint")
134         vaSpread1.Col = 10
136         vaSpread1.Value = rs("DisableUser")
138         vaSpread1.Col = 11
140         vaSpread1.Value = rs("ChangePassword")
142         vaSpread1.MaxRows = vaSpread1.MaxRows + 1
144         vaSpread1.Row = vaSpread1.Row + 1
146         rs.MoveNext

        Loop

148     vaSpread1.MaxRows = vaSpread1.DataRowCnt

        '<EhFooter>
        Exit Sub

FillGrid_Err:
        MsgBox Err.Description & vbCrLf & _
               "in SalesTaxInvoice.frmUserRights.FillGrid " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

