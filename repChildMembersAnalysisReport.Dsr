VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} repChildMembersAnalysisReport 
   Caption         =   "Point_of_Sale_System - repChildMembersAnalysisReport (ActiveReport)"
   ClientHeight    =   10680
   ClientLeft      =   0
   ClientTop       =   390
   ClientWidth     =   15360
   _ExtentX        =   27093
   _ExtentY        =   18838
   SectionData     =   "repChildMembersAnalysisReport.dsx":0000
End
Attribute VB_Name = "repChildMembersAnalysisReport"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub ActiveReport_ReportStart()
        '<EhHeader>
        On Error GoTo ActiveReport_ReportStart_Err
        '</EhHeader>

100     Set SubReport1.object = New repDetailOfMembersAnalysisReport

        '<EhFooter>
        Exit Sub

ActiveReport_ReportStart_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Project1.repChildMembersAnalysisReport.ActiveReport_ReportStart " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub GroupFooter1_Format()
        '<EhHeader>
        On Error GoTo GroupFooter1_Format_Err
        '</EhHeader>
        
        SubReport1.object.DataControl1.ConnectionString = Conn.ConnectionString
100     SubReport1.object.DataControl1.Source = "SELECT dbo.YearlyMembersAnalysis.MonthName, " & _
           "dbo.YearlyMembersAnalysis.YearNo, dbo.YearlyMembersAnalysis.Sub, dbo.YearlyMembersAnalysis.Food, " & _
           "dbo.YearlyMembersAnalysis.Others, dbo.YearlyMembersAnalysis.Total, dbo.YearlyMembersAnalysis.Cash, " & _
           "dbo.YearlyMembersAnalysis.Surcharge, dbo.YearlyMembersAnalysis.Cheque, " & _
           "dbo.YearlyMembersAnalysis.CreditCard, dbo.YearlyMembersAnalysis.RSPNo, " & _
           "dbo.YearlyMembersAnalysis.Remarks,dbo.YearlyMembersAnalysis.MonthNo FROM dbo.YearlyMembersAnalysis INNER JOIN " & _
           "dbo.MemberInformation ON dbo.YearlyMembersAnalysis.MembersCode = dbo.MemberInformation.MemberCode " & _
           "ORDER BY dbo.YearlyMembersAnalysis.MonthNo"
        
        
        '<EhFooter>
        Exit Sub

GroupFooter1_Format_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Project1.repChildMembersAnalysisReport.GroupFooter1_Format " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
