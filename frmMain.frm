VERSION 5.00
Begin VB.Form frmMain 
   BackColor       =   &H00EEEEEE&
   Caption         =   "Billing System"
   ClientHeight    =   4110
   ClientLeft      =   3315
   ClientTop       =   6405
   ClientWidth     =   12225
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmMain.frx":0000
   LinkTopic       =   "Form1"
   LockControls    =   -1  'True
   ScaleHeight     =   4110
   ScaleWidth      =   12225
   WindowState     =   2  'Maximized
   Begin VB.CommandButton cmdInsertMenu 
      Caption         =   "Command1"
      Height          =   345
      Left            =   465
      TabIndex        =   2
      Top             =   1245
      Visible         =   0   'False
      Width           =   1830
   End
   Begin VB.Frame Frame1 
      BackColor       =   &H00EAD08E&
      Height          =   690
      Left            =   0
      TabIndex        =   0
      Top             =   -90
      Width           =   15375
      Begin VB.Label lblMemberType 
         Alignment       =   2  'Center
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Pavilion end Club"
         BeginProperty Font 
            Name            =   "Arial"
            Size            =   20.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Height          =   480
         Left            =   6000
         TabIndex        =   1
         Top             =   150
         Width           =   3345
      End
   End
   Begin VB.Image ImageLogo 
      Height          =   7965
      Left            =   3450
      Picture         =   "frmMain.frx":000C
      Stretch         =   -1  'True
      Top             =   1500
      Width           =   8865
   End
   Begin VB.Menu mnuTransactions 
      Caption         =   "Transactions"
      Begin VB.Menu mnuTransactionsMembersInformation 
         Caption         =   "Members Infomation"
      End
      Begin VB.Menu mnuTransactionsMonthlyCharges 
         Caption         =   "Monthly Charges"
      End
      Begin VB.Menu mnuTransactionsMemberChargesSetup 
         Caption         =   "Member Charges Setup"
      End
      Begin VB.Menu mnuTransactionsPointofSales 
         Caption         =   "Point of Sales"
      End
      Begin VB.Menu mnuTransactionsGenerateBills 
         Caption         =   "Generate Bills"
      End
      Begin VB.Menu mnuTransactionsPrepareReminderLetters 
         Caption         =   "Prepare Reminder Letters"
      End
      Begin VB.Menu mnuTransactionsPaymentRecieved 
         Caption         =   "Payment Recieved"
      End
      Begin VB.Menu mnuTransactionsSpace1 
         Caption         =   "-"
      End
      Begin VB.Menu mnuTransactionsBookingInformation 
         Caption         =   "Lawn Booking Information"
      End
      Begin VB.Menu mnuTransactionsLawnBookingPayments 
         Caption         =   "Lawn Booking Payments"
      End
      Begin VB.Menu mnuTransactionsSpace2 
         Caption         =   "-"
      End
      Begin VB.Menu mnuTransactionsCatersBookingInformation 
         Caption         =   "Caters Booking Information"
      End
      Begin VB.Menu mnuTransactionsCatersBookingPayments 
         Caption         =   "Caters Booking Payments"
      End
      Begin VB.Menu mnuTransactionsSpace3 
         Caption         =   "-"
      End
      Begin VB.Menu mnuTransactionsDecoratersBookingInformation 
         Caption         =   "Decoraters Booking Information"
      End
      Begin VB.Menu mnuTransactionsDecoratersBookingPayments 
         Caption         =   "Decoraters Booking Payments"
      End
      Begin VB.Menu mnuTransactionsSpace4 
         Caption         =   "-"
      End
      Begin VB.Menu mnuTransactionsLawnsBookingInquiry 
         Caption         =   "Lawns Booking Inquiry"
      End
      Begin VB.Menu mnuTransactionsLawnInquiryDateWise 
         Caption         =   "Lawn Inquiry Date Wise"
      End
      Begin VB.Menu mnuTransactionsSpace5 
         Caption         =   "-"
      End
      Begin VB.Menu mnuTransactionsCateringPurchaseOrder 
         Caption         =   "Catering Purchase Order"
      End
      Begin VB.Menu mnuTransactionsDecorationPurchaseOrder 
         Caption         =   "Decoration Purchase Order"
      End
      Begin VB.Menu mnuTransactionsSpace6 
         Caption         =   "-"
      End
      Begin VB.Menu mnuTransactionsPOSGL 
         Caption         =   "POS to GL Data Export"
      End
      Begin VB.Menu mnuTransactionsBillingGL 
         Caption         =   "Billing to GL Export"
      End
      Begin VB.Menu mnuTransactionsLawnGL 
         Caption         =   "Lawn to GL Export"
      End
   End
   Begin VB.Menu mnuSettings 
      Caption         =   "Settings"
      Begin VB.Menu mnuSettingsUserRights 
         Caption         =   "UserRights"
      End
      Begin VB.Menu mnuSettingsChangePassword 
         Caption         =   "Change Password"
      End
      Begin VB.Menu mnuSettingsSpace1 
         Caption         =   "-"
      End
      Begin VB.Menu mnuSettingsFamilySetup 
         Caption         =   "Family Setup"
      End
      Begin VB.Menu mnuSettingsMemberType 
         Caption         =   "Member Type"
      End
      Begin VB.Menu mnuSettingItemCatagory 
         Caption         =   "Item Catagory"
      End
      Begin VB.Menu mnuSettingsItemSetup 
         Caption         =   "Item Setup"
      End
      Begin VB.Menu mnuSettingsDiscountSetup 
         Caption         =   "Discount Setup"
      End
      Begin VB.Menu mnuSettingsDealsSetup 
         Caption         =   "Deals Setup"
         Visible         =   0   'False
      End
      Begin VB.Menu mnuSettingsTaxesSetup 
         Caption         =   "Taxes Setup"
      End
      Begin VB.Menu mnuSettingsChargesSetup 
         Caption         =   "Charges Setup"
      End
      Begin VB.Menu mnuSettingsTablesSetup 
         Caption         =   "Tables Setup"
      End
      Begin VB.Menu mnuSettingsSpace2 
         Caption         =   "-"
      End
      Begin VB.Menu mnuSettingsLawnSetup 
         Caption         =   "Lawn Setup"
      End
      Begin VB.Menu mnuSettingsEventSetup 
         Caption         =   "Event Setup"
      End
      Begin VB.Menu mnuSettingsMenuCategorySetup 
         Caption         =   "Menu Category Setup"
      End
      Begin VB.Menu mnuSettingsMenuSetup 
         Caption         =   "Menu Setup"
      End
      Begin VB.Menu mnuSettingsCatersAndDecorators 
         Caption         =   "Caters and Decorators"
      End
      Begin VB.Menu mnuSettingsDecorationSetup 
         Caption         =   "Decoration Setup"
      End
   End
   Begin VB.Menu mnuReports 
      Caption         =   "Reports"
      Visible         =   0   'False
      Begin VB.Menu mnuReportsMembersList 
         Caption         =   "Members List"
      End
      Begin VB.Menu mnuReportsItemCatagory 
         Caption         =   "Item Catagory"
      End
      Begin VB.Menu mnuReportsItemList 
         Caption         =   "Items List"
      End
   End
   Begin VB.Menu mnuSummaryReports 
      Caption         =   "SummaryReports"
      Begin VB.Menu mnuSummaryReportsMembersListPrint 
         Caption         =   "Members List Print"
      End
      Begin VB.Menu mnuSummaryReportsPerodicNewMembersList 
         Caption         =   "Periodic New Members List"
      End
      Begin VB.Menu mnuSummaryReportsMembersBillPrint 
         Caption         =   "Members Bill Print"
      End
      Begin VB.Menu mnuSummaryReminderLetterPrint 
         Caption         =   "Reminder Letter Print"
      End
      Begin VB.Menu mnuSummaryReportsMonthlyBillingSummaryReport 
         Caption         =   "Monthly Billing Summary Report"
      End
      Begin VB.Menu mnuSummaryReportsPerodicCollectionReport 
         Caption         =   "Periodic Collection Report"
      End
      Begin VB.Menu mnuSummaryReportsSpace1 
         Caption         =   "-"
      End
      Begin VB.Menu mnuSummaryReportsMemberFoodReport 
         Caption         =   "Member Food Report"
      End
      Begin VB.Menu mnuSummaryReportsDailyFoodReport 
         Caption         =   "Daily Food Report"
      End
      Begin VB.Menu mnuSummaryReportsDailyTransactionReport 
         Caption         =   "Daily Transaction Report"
      End
      Begin VB.Menu mnuSummaryReportsSpace2 
         Caption         =   "-"
      End
      Begin VB.Menu mnuSummaryReportsPaymentRecievedReport 
         Caption         =   "Payment Recieved Report"
      End
      Begin VB.Menu mnuSummaryReportsMemberLedger 
         Caption         =   "Member Ledger"
      End
      Begin VB.Menu mnuSummaryReportsPrintLabels 
         Caption         =   "Print Labels"
      End
      Begin VB.Menu mnuSummaryReportsCardsDeliveryReport 
         Caption         =   "Cards Delivery Report"
      End
      Begin VB.Menu mnuSummaryReportsLawnBookingSummary 
         Caption         =   "Lawn Booking Summary"
      End
      Begin VB.Menu mnuSummaryReportsSpace3 
         Caption         =   "-"
      End
      Begin VB.Menu mnuSummaryReportsMembersYearlyAnalysis 
         Caption         =   "Members Yearly Analysis"
      End
      Begin VB.Menu mnuSummaryReportsMembersBillingDetailSummary 
         Caption         =   "Members Billing Detail Summary"
      End
      Begin VB.Menu mnuSummaryReportsMembersOutstandingAgingSummary 
         Caption         =   "Members Outstanding Aging Summary"
      End
      Begin VB.Menu mnuSummaryReportsSMSBillingDataExport 
         Caption         =   "SMS Billing Data Export"
      End
      Begin VB.Menu mnuSummaryReportsLawnSummaryReport 
         Caption         =   "Lawn Summary Report"
      End
   End
End
Attribute VB_Name = "frmMain"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub cmdInsertMenu_Click()
        '<EhHeader>
        On Error GoTo cmdInsertMenu_Click_Err
        '</EhHeader>
    
        Dim iCounter As String
        
        '100     sSQL = "DELETE FROM UserMenu WHERE MenuID='020'"
        '102     Conn.Execute sSQL
        
100     sSQL = "SELECT MAX(MenuID) AS LastNumber From UserMenu"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     iCounter = Format(rs("LastNumber") + 1, "0##")
     
108     sSQL = "SELECT * From UserMenu WHERE MenuName='mnuTransactionsPOSGL'"
110     Set rs = New ADODB.Recordset
112     rs.Open sSQL, Conn, 1, 3
        
114     If rs.EOF = True Then
116         rs.AddNew
118         rs("MenuID") = iCounter
        End If

120     rs("MenuName") = "mnuTransactionsPOSGL"
122     rs("MenuCaption") = "POS to GL Data Export"
124     rs("MenuType") = "TransactionFrom"
126     rs.Update

        '~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        '~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        '~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

128     sSQL = "SELECT MAX(MenuID) AS LastNumber From UserMenu"
130     Set rs = New ADODB.Recordset
132     rs.Open sSQL, Conn, 1, 3
    
134     iCounter = Format(rs("LastNumber") + 1, "0##")
     
136     sSQL = "SELECT * From UserMenu WHERE MenuName='mnuTransactionsLawnGL'"
138     Set rs = New ADODB.Recordset
140     rs.Open sSQL, Conn, 1, 3
        
142     If rs.EOF = True Then
144         rs.AddNew
146         rs("MenuID") = iCounter
        End If

148     rs("MenuName") = "mnuTransactionsLawnGL"
150     rs("MenuCaption") = "Lawn to GL Export"
152     rs("MenuType") = "TransactionFrom"
154     rs.Update

        '<EhFooter>
        Exit Sub

cmdInsertMenu_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.cmdInsertMenu_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>
    
100     MenuRightsImplement
102     Call cmdInsertMenu_Click


        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_QueryUnload(Cancel As Integer, _
                             UnloadMode As Integer)
        '<EhHeader>
        On Error GoTo Form_QueryUnload_Err
        '</EhHeader>
    
100     End
    
        '<EhFooter>
        Exit Sub

Form_QueryUnload_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.Form_QueryUnload " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Resize()
    '<EhHeader>
    On Error Resume Next
    '</EhHeader>
    
    Frame1.Width = Me.ScaleWidth
    Frame1.Left = (Me.ScaleWidth - Frame1.Width) / 2
    ImageLogo.Left = (Me.ScaleWidth - ImageLogo.Width) / 2

End Sub

Private Sub mnuSettingItemCatagory_Click()
        '<EhHeader>
        On Error GoTo mnuSettingItemCatagory_Click_Err
        '</EhHeader>

100     frmItemCatagory.Show 1

        '<EhFooter>
        Exit Sub

mnuSettingItemCatagory_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingItemCatagory_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsCatersAndDecorators_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsCatersAndDecorators_Click_Err
        '</EhHeader>
    
100     frmCatersAndDecoraters.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSettingsCatersAndDecorators_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsCatersAndDecorators_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsChangePassword_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsChangePassword_Click_Err
        '</EhHeader>

100     frmChangePassword.Show 1

        '<EhFooter>
        Exit Sub

mnuSettingsChangePassword_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsChangePassword_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsChargesSetup_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsChargesSetup_Click_Err
        '</EhHeader>

100     frmChargesSetup.Show 1

        '<EhFooter>
        Exit Sub

mnuSettingsChargesSetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsChargesSetup_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsDecorationSetup_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsDecorationSetup_Click_Err
        '</EhHeader>
    
100     frmDecorations.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSettingsDecorationSetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsDecorationSetup_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsDiscountSetup_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsDiscountSetup_Click_Err
        '</EhHeader>

100     frmDiscountSetup.Show 1

        '<EhFooter>
        Exit Sub

mnuSettingsDiscountSetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsDiscountSetup_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsEventSetup_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsEventSetup_Click_Err
        '</EhHeader>
    
100     frmEventSetup.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSettingsEventSetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsEventSetup_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsFamilySetup_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsFamilySetup_Click_Err
        '</EhHeader>
    
100     frmFamilySetup.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSettingsFamilySetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsFamilySetup_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsItemSetup_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsItemSetup_Click_Err
        '</EhHeader>

100     frmItemSetup.Show 1

        '<EhFooter>
        Exit Sub

mnuSettingsItemSetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsItemSetup_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsLawnSetup_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsLawnSetup_Click_Err
        '</EhHeader>
    
100     frmLawnSetup.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSettingsLawnSetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsLawnSetup_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsMemberType_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsMemberType_Click_Err
        '</EhHeader>

100     frmMemberType.Show 1

        '<EhFooter>
        Exit Sub

mnuSettingsMemberType_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsMemberType_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsMenuCategorySetup_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsMenuCategorySetup_Click_Err
        '</EhHeader>
    
100     frmMenuCatagorySetup.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSettingsMenuCategorySetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsMenuCategorySetup_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsMenuSetup_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsMenuSetup_Click_Err
        '</EhHeader>

100     frmMenuSetup.Show 1

        '<EhFooter>
        Exit Sub

mnuSettingsMenuSetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsMenuSetup_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsTablesSetup_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsTablesSetup_Click_Err
        '</EhHeader>

100     frmTablesSetup.Show 1

        '<EhFooter>
        Exit Sub

mnuSettingsTablesSetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsTablesSetup_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsTaxesSetup_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsTaxesSetup_Click_Err
        '</EhHeader>

100     frmTaxesSetup.Show 1

        '<EhFooter>
        Exit Sub

mnuSettingsTaxesSetup_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsTaxesSetup_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSettingsUserRights_Click()
        '<EhHeader>
        On Error GoTo mnuSettingsUserRights_Click_Err
        '</EhHeader>
    
100     frmUserRights.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSettingsUserRights_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSettingsUserRights_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReminderLetterPrint_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReminderLetterPrint_Click_Err
        '</EhHeader>
    
100     frmPrintReminderLetters.Show 1

        '<EhFooter>
        Exit Sub

mnuSummaryReminderLetterPrint_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReminderLetterPrint_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsCardsDeliveryReport_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsCardsDeliveryReport_Click_Err
        '</EhHeader>
        
100     frmMembersCardsDeliveryReport.Show 1

        '<EhFooter>
        Exit Sub

mnuSummaryReportsCardsDeliveryReport_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsCardsDeliveryReport_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsDailyFoodReport_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsDailyFoodReport_Click_Err
        '</EhHeader>
    
100     frmDailyMealReport.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSummaryReportsDailyFoodReport_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsDailyFoodReport_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsDailyTransactionReport_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsDailyTransactionReport_Click_Err
        '</EhHeader>
    
100     frmDailyTransactionDetail.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSummaryReportsDailyTransactionReport_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsDailyTransactionReport_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsLawnSummaryReport_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsLawnSummaryReport_Click_Err
        '</EhHeader>
    
100     frmLDCOutStandingAmountReportPrint.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSummaryReportsLawnSummaryReport_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsLawnSummaryReport_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsMemberFoodReport_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsMemberFoodReport_Click_Err
        '</EhHeader>

100     frmMembersFoodReport.Show 1

        '<EhFooter>
        Exit Sub

mnuSummaryReportsMemberFoodReport_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsMemberFoodReport_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsMemberLedger_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsMemberLedger_Click_Err
        '</EhHeader>
    
100     frmMemberLedger.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSummaryReportsMemberLedger_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsMemberLedger_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsMembersBillingDetailSummary_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsMembersBillingDetailSummary_Click_Err
        '</EhHeader>
    
100     frmDetailOfMemebersBillingPrint.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSummaryReportsMembersBillingDetailSummary_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsMembersBillingDetailSummary_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsMembersBillPrint_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsMembersBillPrint_Click_Err
        '</EhHeader>
    
100     frmMemberBillsPrint.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSummaryReportsMembersBillPrint_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsMembersBillPrint_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsMembersListPrint_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsMembersListPrint_Click_Err
        '</EhHeader>
    
100     frmMembersListPrint.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSummaryReportsMembersListPrint_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsMembersListPrint_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsMembersOutstandingAgingSummary_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsMembersOutstandingAgingSummary_Click_Err
        '</EhHeader>

100     frmMembersOutStandingAgingReportPrint.Show 1

        '<EhFooter>
        Exit Sub

mnuSummaryReportsMembersOutstandingAgingSummary_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsMembersOutstandingAgingSummary_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsMonthlyBillingSummaryReport_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsMonthlyBillingSummaryReport_Click_Err
        '</EhHeader>

100     frmMonthlyBillingSummaryReport.Show 1

        '<EhFooter>
        Exit Sub

mnuSummaryReportsMonthlyBillingSummaryReport_Click_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.frmMain.mnuSummaryReportsMonthlyBillingSummaryReport_Click " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsPaymentRecievedReport_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsPaymentRecievedReport_Click_Err
        '</EhHeader>

100     frmMemberPaymentRecievedReport.Show 1

        '<EhFooter>
        Exit Sub

mnuSummaryReportsPaymentRecievedReport_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsPaymentRecievedReport_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsPerodicCollectionReport_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsPerodicCollectionReport_Click_Err
        '</EhHeader>
    
100     frmPerodicCollectionReport.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSummaryReportsPerodicCollectionReport_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsPerodicCollectionReport_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsPerodicNewMembersList_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsPerodicNewMembersList_Click_Err
        '</EhHeader>
    
100     frmPerodicNewMembersReport.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSummaryReportsPerodicNewMembersList_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsPerodicNewMembersList_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsPrintLabels_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsPrintLabels_Click_Err
        '</EhHeader>
    
100     sSQL = "SELECT MemberCode, MemberName, BillingAddress, MobileNumber From MemberInformation " & _
           "WHERE (RIGHT(MemberCode, 3) = '001') AND (LEFT(MemberCode, 2) <> '99') AND (LEN(BillingAddress) <> 0) " & _
           "ORDER BY MemberName, MemberCode"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     With repAddressLabelsPrint
108         .DataControl1.Recordset = rs
110         .documentName = "Print Labels"
112         .Caption = .documentName
114         .WindowState = vbMaximized
116         .Show 1
        End With
    
        '<EhFooter>
        Exit Sub

mnuSummaryReportsPrintLabels_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsPrintLabels_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuSummaryReportsSMSBillingDataExport_Click()
        '<EhHeader>
        On Error GoTo mnuSummaryReportsSMSBillingDataExport_Click_Err
        '</EhHeader>
    
100     frmSmsBillingSummaryPrint.Show 1
    
        '<EhFooter>
        Exit Sub

mnuSummaryReportsSMSBillingDataExport_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuSummaryReportsSMSBillingDataExport_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsBookingInformation_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsBookingInformation_Click_Err
        '</EhHeader>
        
100     TransactionType = "LB"
102     frmBookers.Show 1
    
        '<EhFooter>
        Exit Sub

mnuTransactionsBookingInformation_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsBookingInformation_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsCateringPurchaseOrder_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsCateringPurchaseOrder_Click_Err
        '</EhHeader>
        
100     TransactionType = "CB"
102     frmCatersPurchaseOrder.lblHeading.Caption = "Catering Purchase Order"
104     frmCatersPurchaseOrder.Show 1
    
        '<EhFooter>
        Exit Sub

mnuTransactionsCateringPurchaseOrder_Click_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.frmMain.mnuTransactionsCateringPurchaseOrder_Click " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsCatersBookingInformation_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsCatersBookingInformation_Click_Err
        '</EhHeader>
        
100     TransactionType = "CB"
102     frmCatersBooking.Show 1
    
        '<EhFooter>
        Exit Sub

mnuTransactionsCatersBookingInformation_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsCatersBookingInformation_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsCatersBookingPayments_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsCatersBookingPayments_Click_Err
        '</EhHeader>

100     TransactionType = "CP"
102     frmLawnPayments.lblLawnPayments.Caption = " Caters Booking Payments"
104     frmLawnPayments.Show 1

        '<EhFooter>
        Exit Sub

mnuTransactionsCatersBookingPayments_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsCatersBookingPayments_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsDecoratersBookingInformation_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsDecoratersBookingInformation_Click_Err
        '</EhHeader>
            
100     TransactionType = "DB"
102     frmDecoratersBooking.Show 1
    
        '<EhFooter>
        Exit Sub

mnuTransactionsDecoratersBookingInformation_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsDecoratersBookingInformation_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsDecoratersBookingPayments_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsDecoratersBookingPayments_Click_Err
        '</EhHeader>
        
100     TransactionType = "DP"
102     frmLawnPayments.lblLawnPayments.Caption = " Decorators Booking Payments"
104     frmLawnPayments.Show 1

        '<EhFooter>
        Exit Sub

mnuTransactionsDecoratersBookingPayments_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsDecoratersBookingPayments_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsDecorationPurchaseOrder_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsDecorationPurchaseOrder_Click_Err
        '</EhHeader>
        
100     TransactionType = "DB"
102     frmCatersPurchaseOrder.lblHeading.Caption = "Decoration Purchase Order"
104     frmCatersPurchaseOrder.Show 1

        '<EhFooter>
        Exit Sub

mnuTransactionsDecorationPurchaseOrder_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsDecorationPurchaseOrder_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsLawnBookingPayments_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsLawnBookingPayments_Click_Err
        '</EhHeader>
    
100     TransactionType = "LP"
102     frmLawnPayments.lblLawnPayments.Caption = " Lawn Booking Payments"
104     frmLawnPayments.Show 1
    
        '<EhFooter>
        Exit Sub

mnuTransactionsLawnBookingPayments_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsLawnBookingPayments_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsLawnInquiryDateWise_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsLawnInquiryDateWise_Click_Err
        '</EhHeader>
    
100     frmLawnInquirybyDate.Show 1

        '<EhFooter>
        Exit Sub

mnuTransactionsLawnInquiryDateWise_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsLawnInquiryDateWise_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub


Private Sub mnuTransactionsMembersInformation_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsMembersInformation_Click_Err
        '</EhHeader>

100     frmMemberInformation.Show 1

        '<EhFooter>
        Exit Sub

mnuTransactionsMembersInformation_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsMembersInformation_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsMonthlyCharges_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsMonthlyCharges_Click_Err
        '</EhHeader>

100     frmMonthlyCharges.Show 1

        '<EhFooter>
        Exit Sub

mnuTransactionsMonthlyCharges_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsMonthlyCharges_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsPaymentRecieved_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsPaymentRecieved_Click_Err
        '</EhHeader>

100     frmPaymentReceived.Show 1

        '<EhFooter>
        Exit Sub

mnuTransactionsPaymentRecieved_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsPaymentRecieved_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsPointofSales_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsPointofSales_Click_Err
        '</EhHeader>
    
100     frmPOSPavilianEndClub.Show 1
    
        '<EhFooter>
        Exit Sub

mnuTransactionsPointofSales_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsPointofSales_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdInsertMenu1_Click()
        '<EhHeader>
        On Error GoTo cmdInsertMenu1_Click_Err
        '</EhHeader>
        
        Dim obj As Object
        Dim iCounter As String
100     iCounter = 1

102     For Each obj In Me

104         If TypeOf obj Is Menu Then

106             If obj.Caption <> "-" Then

108                 Debug.Print obj.Name & "        " & obj.Caption
                
110                 iCounter = Format$(iCounter, "0##")
112                 sSQL = "INSERT INTO UserMenu (MenuID, MenuName, MenuCaption, MenuType) " & _
                       "VALUES ('" & iCounter & "', '" & obj.Name & "', '" & obj.Caption & "', '')"
114                 Conn.Execute sSQL
116                 iCounter = Val(iCounter) + 1

                End If

            End If

        Next

        '<EhFooter>
        Exit Sub

cmdInsertMenu1_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.cmdInsertMenu1_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub MenuRightsImplement()
        '<EhHeader>
        On Error GoTo MenuRightsImplement_Err
        '</EhHeader>

        'On Error Resume Next

        Dim ObjMenu As Object

100     If UserID = "Shahid" Then

            Exit Sub

        End If
    
102     For Each ObjMenu In Me

104         If TypeOf ObjMenu Is Menu Then
            
106             If ObjMenu.Caption <> "-" Then

108                 ObjMenu.Enabled = False

                End If

            End If

        Next
   
110     For i = 1 To 4

112         sSQL = "SELECT UserMenu.MenuName, UserMenu.MenuCaption  FROM MenuRights INNER JOIN UserMenu ON MenuRights.MenuID = UserMenu.MenuID " & _
               "WHERE MenuRights.MenuTypeID=" & i & " And MenuRights.UserID='" & UserID & "'"
114         Set rs = New ADODB.Recordset
116         rs.Open sSQL, Conn, 1, 3

118         Do Until rs.EOF

120             For Each ObjMenu In Me

122                 If TypeOf ObjMenu Is Menu Then

124                     If ObjMenu.Name = Trim(rs("MenuName")) Then
                            
126                         ObjMenu.Enabled = True

                        End If

                    End If

                Next

128             rs.MoveNext

            Loop

        Next

130     mnuTransactions.Enabled = True
        '132     mnuExit.Enabled = True
132     mnuSettings.Enabled = True
134     mnuReports.Enabled = True
136     mnuSummaryReports.Enabled = True

        '<EhFooter>
        Exit Sub

MenuRightsImplement_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.MenuRightsImplement " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub mnuTransactionsPrepareReminderLetters_Click()
        '<EhHeader>
        On Error GoTo mnuTransactionsPrepareReminderLetters_Click_Err
        '</EhHeader>
    
100     frmPrepareReminderLetter.Show 1
    
        '<EhFooter>
        Exit Sub

mnuTransactionsPrepareReminderLetters_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmMain.mnuTransactionsPrepareReminderLetters_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
