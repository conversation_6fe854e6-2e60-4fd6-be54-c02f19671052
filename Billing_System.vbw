frmMain = 0, 0, 0, 0, C, 182, 182, 1141, 694, C
modMain = 169, 170, 1501, 850, 
frmItemSetup = 185, 131, 1419, 810, C, 208, 208, 1167, 720, C
frmItemCatagory = 0, 0, 0, 0, C, 234, 234, 1193, 746, C
ucProgressBar = 0, 0, 0, 0, C, 0, 0, 959, 512, C
ucStatusBar = 0, 0, 0, 0, C, 26, 26, 985, 538, C
frmFamilySetup = 0, 0, 0, 0, C, 52, 52, 1011, 564, C
frmPointOfSale = 274, 120, 1464, 942, C, 78, 78, 1037, 590, C
modLvTimer = 220, 183, 667, 895, C
lvButtons_H = 174, 346, 1142, 657, C, 104, 104, 1063, 616, C
frmChangePassword = 0, 0, 0, 0, C, 130, 130, 1089, 642, C
frmLogin = 183, 183, 1142, 695, C, 156, 156, 1115, 668, C
frmRightsBrowser = 0, 0, 0, 0, C, 182, 182, 1141, 694, C
frmUserRights = 0, 0, 0, 0, C, 208, 208, 1167, 720, C
frmMemberType = 0, 0, 0, 0, C, 234, 234, 1193, 746, C
CSHA256 = 104, 104, 1156, 555, C
frmEmployees = 0, 0, 0, 0, C, 0, 0, 959, 512, C
frmDiscountSetup = 0, 0, 0, 0, C, 26, 26, 985, 538, C
frmDealsSetup = 0, 0, 0, 0, C, 52, 52, 1011, 564, C
repPrintDockit = 0, 0, 0, 0, C, 52, 52, 1084, 799, C
frmTaxesSetup = 0, 0, 0, 0, C, 104, 104, 1063, 616, C
frmMainSubWay = 185, 304, 1342, 755, , 182, 182, 1141, 694, C
repDailyMealReport = 220, 129, 667, 895, C, 78, 78, 1054, 907, C
repZTapeReport = 0, 0, 0, 0, C, 104, 104, 1400, 921, C
frmDockitPrint = 222, 131, 1759, 815, C, 208, 208, 1167, 720, C
frmDailyMealReport = 220, 129, 1354, 784, CZ, 234, 234, 1193, 746, C
frmZTapePrint = 0, 0, 0, 0, C, 0, 0, 959, 512, C
frmEndOfDay = 0, 0, 0, 0, C, 26, 26, 985, 538, C
frmChargesSetup = 0, 0, 0, 0, C, 52, 52, 1011, 564, C
frmMonthlyCharges = 0, 0, 0, 0, C, 78, 78, 1037, 590, C
repItemCatagory = 0, 0, 0, 0, C, 130, 130, 1012, 641, C
repItemsList = 0, 0, 0, 0, C, 156, 156, 611, 702, C
repPeriodicBillingReport = 0, 0, 0, 0, C, 182, 182, 1214, 929, C
repPeriodicCollectionReport = 0, 0, 0, 0, C, 208, 208, 1240, 955, C
repDailyTransactions = 220, 129, 667, 895, C, 234, 234, 1530, 1118, C
frmDailyTransactionDetail = 220, 129, 1630, 758, C, 234, 234, 1193, 746, C
repCatagoryWiseMealReport = 220, 129, 667, 895, C, 0, 0, 1032, 760, C
frmTaxesCollectionReport = 220, 129, 1891, 869, C, 26, 26, 985, 538, C
frmPOSPavilianEndClub = 0, 0, 0, 0, C, 52, 52, 1011, 564, C
repDockitPavilianEndClub = 0, 0, 0, 0, C, 26, 26, 1066, 777, C
repPrintDockitStar = 0, 0, 0, 0, C, 52, 52, 1084, 799, C
repPrinterTest = 0, 0, 0, 0, C, 78, 78, 1118, 825, C
repDiscountReport = 0, 0, 0, 0, C, 104, 104, 1144, 851, C
WinXp = 156, 156, 1280, 607, C
win98 = 0, 0, 0, 0, C
frmSettings = 0, 0, 0, 0, C, 182, 182, 1141, 694, C
repZTapeReportStar = 0, 0, 0, 0, C, 130, 130, 1170, 881, C
frmMemberInformation = 0, 0, 0, 0, C, 234, 234, 1193, 746, C
frmPOSDeliverySystem = 0, 0, 0, 0, C, 0, 0, 959, 512, C
frmDateWiseTransactionSummary = 220, 129, 1157, 739, C, 26, 26, 985, 538, C
repDateWiseTransactionSummary = 220, 129, 667, 895, C, 156, 156, 1196, 907, C
frmTablesSetup = 0, 0, 0, 0, C, 78, 78, 1037, 590, C
frmDiscountReport = 0, 0, 0, 0, C, 104, 104, 1063, 616, C
repDeliveryReport = 0, 0, 0, 0, C, 182, 182, 1222, 933, C
frmDeliveryReport = 0, 0, 0, 0, C, 156, 156, 1115, 668, C
frmGenerateBills = 0, 0, 0, 0, C, 182, 182, 1141, 694, C
repMemberBills = 0, 0, 0, 0, C, 208, 208, 1248, 959, C
frmDataBrowser = 0, 0, 0, 0, C, 234, 234, 1193, 746, C
frmMemberBillsPrint = 0, 0, 0, 0, C, 0, 0, 959, 512, C
repMemberFoodReport = 0, 0, 0, 0, C, 234, 234, 1274, 994, C
frmMembersFoodReport = 0, 0, 0, 0, C, 52, 52, 1011, 564, C
frmPaymentReceived = 0, 0, 0, 0, C, 78, 78, 1037, 590, C
repPaymentRecieved = 0, 0, 0, 0, C, 0, 0, 1032, 747, C
frmMemberPaymentRecievedReport = 0, 0, 0, 0, C, 130, 130, 1089, 642, C
repMemberLedger = 0, 0, 0, 0, C, 26, 26, 1066, 773, C
frmMemberLedger = 0, 0, 0, 0, C, 182, 182, 1141, 694, C
frmReorderBills = 0, 0, 0, 0, C, 208, 208, 1167, 720, C
frmRFID_Device = 0, 0, 0, 0, C, 234, 234, 1193, 746, C
modRegionalSettings = 182, 182, 1306, 633, C
frmLawnSetup = 0, 0, 0, 0, C, 0, 0, 959, 512, C
frmMenuSetup = 0, 0, 0, 0, C, 26, 26, 985, 538, C
frmBookers = 0, 0, 0, 0, C, 52, 52, 1011, 564, C
frmMenuCatagorySetup = 0, 0, 0, 0, C, 78, 78, 1037, 590, C
frmCatersAndDecoraters = 0, 0, 0, 0, C, 104, 104, 1063, 616, C
frmDecorations = 0, 0, 0, 0, C, 130, 130, 1089, 642, C
frmEventSetup = 0, 0, 0, 0, C, 156, 156, 1115, 668, C
frmMemberChargesSetup = 0, 0, 0, 0, C, 182, 182, 1141, 694, C
repAddressLabelsPrint = 0, 0, 0, 0, C, 52, 52, 1092, 799, C
repMembersList = 0, 0, 0, 0, C, 78, 78, 1118, 825, C
frmMembersListPrint = 0, 0, 0, 0, C, 0, 0, 959, 512, C
frmCatersBooking = 0, 0, 0, 0, C, 26, 26, 985, 538, C
frmDecoratersBooking = 0, 0, 0, 0, C, 52, 52, 1011, 564, C
repLawnBookingRecepit = 0, 0, 0, 0, C, 104, 104, 1144, 851, C
ModNumToString = 183, 129, 630, 895, C
repLawnBookingFromPrint = 0, 0, 0, 0, C, 130, 130, 1170, 877, C
frmLawnPayments = 0, 0, 0, 0, C, 130, 130, 1089, 642, C
repCatersandDecoratorsBooking = 0, 0, 0, 0, C, 156, 156, 1196, 903, C
frmLawnInquirybyDate = 0, 0, 0, 0, C, 182, 182, 1141, 694, C
frmCatersPurchaseOrder = 0, 0, 0, 0, C, 208, 208, 1167, 720, C
repCardsIssueReport = 0, 0, 0, 0, C, 182, 182, 1222, 933, C
frmMembersCardsDeliveryReport = 0, 0, 0, 0, C, 0, 0, 959, 512, C
frmMainLittleItly = 0, 0, 0, 0, C, 26, 26, 985, 538, C
repPrintDockitLittleItaly = 0, 0, 0, 0, C, 208, 208, 1240, 955, C
frmPointOfSaleLittleItaly = 0, 0, 0, 0, C, 78, 78, 1037, 590, C
repPurchaseOrder = 0, 0, 0, 0, C, 234, 234, 1274, 981, C
repCostSheet = 0, 0, 0, 0, C, 0, 0, 1040, 747, C
repPeriodicNewMembersReport = 0, 0, 0, 0, C, 26, 26, 1066, 773, C
frmPerodicNewMembersReport = 0, 0, 0, 0, C, 182, 182, 1141, 694, C
frmPerodicCollectionReport = 0, 0, 0, 0, C, 208, 208, 1167, 720, C
repMonthlyBillingSummaryReport = 0, 0, 0, 0, C, 52, 52, 1092, 799, C
frmMonthlyBillingSummaryReport = 0, 0, 0, 0, C, 0, 0, 959, 512, C
repReceiptPrint = 0, 0, 0, 0, C, 78, 78, 1118, 825, C
repMembersBillingSummary = 0, 0, 0, 0, C, 104, 104, 1144, 851, C
repPaymentReminderLetter = 0, 0, 0, 0, C, 130, 130, 1174, 915, C
frmPrepareReminderLetter = 0, 0, 0, 0, C, 104, 104, 1063, 616, C
frmPrintReminderLetters = 0, 0, 0, 0, C, 130, 130, 1089, 642, C
frmSendPOSDataToGL = 0, 0, 0, 0, C, 156, 156, 1115, 668, C
frmSendBillingDataToGL = 0, 0, 0, 0, C, 182, 182, 1141, 694, C
repDetailOfMembersAnalysisSubReport = 0, 0, 0, 0, C, 156, 156, 1140, 635, C
repChildMembersAnalysisReport = 0, 0, 0, 0, C, 182, 182, 1222, 933, C
repDetailOfMembersAnalysisReport = 0, 0, 0, 0, C, 208, 208, 1217, 687, C
repDetailOfMemebersBill = 0, 0, 0, 0, C, 234, 234, 1278, 780, C
repMembersOutstandingAgingReport = 0, 0, 0, 0, C, 0, 0, 1296, 817, C
frmDetailOfMemebersAnalysisPrint = 0, 0, 0, 0, C, 78, 78, 1037, 590, C
frmDetailOfMemebersBillingPrint = 0, 0, 0, 0, C, 104, 104, 1063, 616, C
frmMembersOutStandingAgingReportPrint = 0, 0, 0, 0, C, 130, 130, 1089, 642, C
frmSmsBillingSummaryPrint = 0, 0, 0, 0, C, 156, 156, 1115, 668, C
frmLDCOutStandingAmountReportPrint = 0, 0, 0, 0, C, 182, 182, 1141, 694, C
repLDCOutStandingAmountReport = 0, 0, 0, 0, C, 26, 26, 1070, 555, C
repHourlyMealSalesReport = 0, 0, 0, 0, C, 52, 52, 1092, 803, C
frmSendLawnDetaiToGL = 0, 0, 0, 0, C, 0, 0, 959, 512, C
repLawanDailyCollectionReport = 0, 0, 0, 0, C, 78, 78, 1122, 695, C
repDOTPrinterFontTest = 0, 0, 0, 0, C, 104, 104, 1144, 855, C
repHourlyMealSalesReportStar = 0, 0, 0, 0, C, 130, 130, 1170, 881, C
frmSalesPercentage = 0, 0, 0, 0, C, 104, 104, 1063, 616, C
repSalesPercentage = 0, 0, 0, 0, C, 156, 156, 938, 808, C
frmMainPOSUK = 0, 0, 0, 0, C, 156, 156, 1115, 668, C
frmCustomersSetup = 0, 0, 0, 0, C, 182, 182, 1141, 694, C
repItemBarcodePrint = 0, 0, 0, 0, C, 182, 182, 1478, 999, C
frmPOSUK = 0, 0, 0, 0, C, 234, 234, 1193, 746, C
frmItemBarcodePrint = 0, 0, 0, 0, C, 0, 0, 959, 512, C
repSalesInvoiceUK = 0, 0, 0, 0, C, 208, 208, 1504, 1025, C
frmDailySalesInvoice = 0, 0, 0, 0, C, 52, 52, 1011, 564, C
repItemSalesSummary = 0, 0, 0, 0, C, 234, 234, 1312, 883, C
frmItemSalesSummery = 0, 0, 0, 0, C, 104, 104, 1063, 616, C
frmPOSTouch = 0, 0, 0, 0, C, 130, 130, 1089, 642, C
repSalesInvoiceFBR = 220, 129, 1795, 580, C, 0, 0, 1160, 876, C
frmFBRSalesReverse = 26, 26, 1078, 477, C, 156, 156, 1115, 668, C
