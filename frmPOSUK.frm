VERSION 5.00
Object = "{CDF3B183-D408-11CE-AE2C-0080C786E37D}#3.0#0"; "Edt32x30.ocx"
Object = "{B02F3647-766B-11CE-AF28-C3A2FBE76A13}#3.0#0"; "SPR32X30.ocx"
Begin VB.Form frmPOSUK 
   BackColor       =   &H00EEEEEE&
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "Billing System"
   ClientHeight    =   10905
   ClientLeft      =   1935
   ClientTop       =   4005
   ClientWidth     =   16185
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmPOSUK.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   10905
   ScaleWidth      =   16185
   ShowInTaskbar   =   0   'False
   Begin VB.PictureBox picSearch 
      Appearance      =   0  'Flat
      BackColor       =   &H008FCFD8&
      ForeColor       =   &H80000008&
      Height          =   4785
      Left            =   2430
      ScaleHeight     =   4755
      ScaleWidth      =   11475
      TabIndex        =   29
      Top             =   3375
      Visible         =   0   'False
      Width           =   11505
      Begin VB.TextBox txtPrice 
         Appearance      =   0  'Flat
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   12
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   405
         Left            =   10140
         TabIndex        =   36
         Top             =   360
         Width           =   1305
      End
      Begin VB.TextBox txtItemDesc 
         Appearance      =   0  'Flat
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   12
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   405
         Left            =   4500
         TabIndex        =   34
         Top             =   360
         Width           =   5415
      End
      Begin VB.TextBox txtItemSearch 
         Appearance      =   0  'Flat
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   12
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   405
         Left            =   180
         TabIndex        =   31
         Top             =   360
         Width           =   4050
      End
      Begin FPSpread.vaSpread vaSpreadSearch 
         Height          =   3840
         Left            =   30
         TabIndex        =   30
         Top             =   870
         Width           =   11415
         _Version        =   196608
         _ExtentX        =   20135
         _ExtentY        =   6773
         _StockProps     =   64
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         MaxCols         =   4
         MaxRows         =   15
         ScrollBars      =   2
         SpreadDesigner  =   "frmPOSUK.frx":000C
      End
      Begin VB.Label Label5 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Price:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   12
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   285
         Left            =   10140
         TabIndex        =   37
         Top             =   90
         Width           =   810
      End
      Begin VB.Label Label4 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Item Desc:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   12
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   285
         Left            =   4500
         TabIndex        =   35
         Top             =   90
         Width           =   1350
      End
      Begin VB.Label Label1 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Item Name:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   12
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   285
         Left            =   180
         TabIndex        =   32
         Top             =   90
         Width           =   1350
      End
   End
   Begin VB.ComboBox cmbInvNo 
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   405
      Left            =   3165
      Style           =   2  'Dropdown List
      TabIndex        =   26
      Top             =   945
      Width           =   2985
   End
   Begin VB.TextBox txtInvID 
      Appearance      =   0  'Flat
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   405
      Left            =   1620
      TabIndex        =   19
      Text            =   "Text1"
      Top             =   945
      Width           =   1500
   End
   Begin VB.ComboBox cmbCustomerName 
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   405
      Left            =   8475
      Style           =   2  'Dropdown List
      TabIndex        =   12
      Top             =   855
      Width           =   6675
   End
   Begin VB.TextBox txtItemCode 
      Appearance      =   0  'Flat
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   405
      Left            =   1620
      TabIndex        =   10
      Top             =   1590
      Width           =   3495
   End
   Begin VB.Frame Frame3 
      Appearance      =   0  'Flat
      BackColor       =   &H00EAD08E&
      ForeColor       =   &H80000008&
      Height          =   825
      Left            =   15
      TabIndex        =   8
      Top             =   -75
      Width           =   16170
      Begin VB.Label lblTransactionDate 
         Alignment       =   1  'Right Justify
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Date:"
         BeginProperty Font 
            Name            =   "Consolas"
            Size            =   15.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   360
         Left            =   15120
         TabIndex        =   27
         Top             =   285
         Width           =   900
      End
      Begin VB.Label Label6 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Point Of Sales  [Cash Sales]"
         BeginProperty Font 
            Name            =   "Corbel"
            Size            =   21.75
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Height          =   540
         Left            =   120
         TabIndex        =   9
         Top             =   195
         Width           =   5055
      End
   End
   Begin VB.Frame Frame2 
      BackColor       =   &H00EEEEEE&
      BeginProperty Font 
         Name            =   "Corbel"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   120
      Left            =   -255
      TabIndex        =   7
      Top             =   2205
      Width           =   16470
   End
   Begin Point_of_Sale_System.lvButtons_H cmdNew 
      Height          =   840
      Left            =   75
      TabIndex        =   1
      Top             =   9630
      Width           =   2415
      _ExtentX        =   4260
      _ExtentY        =   1482
      Caption         =   "New Sales"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Corbel"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.ucStatusBar UcSStatText 
      Height          =   330
      Left            =   -15
      TabIndex        =   2
      Top             =   10590
      Width           =   16215
      _ExtentX        =   28601
      _ExtentY        =   582
      Text            =   " Point of Sale-={by www.intelysol.com"
   End
   Begin FPSpread.vaSpread vaSpread 
      Height          =   5400
      Left            =   60
      TabIndex        =   3
      Top             =   2430
      Width           =   16095
      _Version        =   196608
      _ExtentX        =   28390
      _ExtentY        =   9525
      _StockProps     =   64
      EditEnterAction =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   11.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   8
      MaxRows         =   20
      ScrollBars      =   2
      SpreadDesigner  =   "frmPOSUK.frx":03AA
   End
   Begin Point_of_Sale_System.lvButtons_H cmdClose 
      Cancel          =   -1  'True
      Height          =   840
      Left            =   9510
      TabIndex        =   4
      Top             =   9630
      Width           =   1860
      _ExtentX        =   3281
      _ExtentY        =   1482
      Caption         =   "Close"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Corbel"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      cGradient       =   0
      Mode            =   0
      Value           =   0   'False
      cBack           =   -2147483633
   End
   Begin Point_of_Sale_System.lvButtons_H cmdPayments 
      Height          =   840
      Left            =   2860
      TabIndex        =   6
      Top             =   9630
      Width           =   3270
      _ExtentX        =   5768
      _ExtentY        =   1482
      Caption         =   "Save && Print"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Corbel"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      Image           =   "frmPOSUK.frx":0AD6
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin EditLib.fpCurrency txtTotalAmount 
      Height          =   450
      Left            =   13275
      TabIndex        =   25
      Top             =   7935
      Width           =   2565
      _Version        =   196608
      _ExtentX        =   4524
      _ExtentY        =   794
      Enabled         =   -1  'True
      MousePointer    =   0
      Object.TabStop         =   -1  'True
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   14.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      BackColor       =   11856344
      ForeColor       =   -2147483640
      ThreeDInsideStyle=   0
      ThreeDInsideHighlightColor=   -2147483633
      ThreeDInsideShadowColor=   -2147483642
      ThreeDInsideWidth=   1
      ThreeDOutsideStyle=   0
      ThreeDOutsideHighlightColor=   -2147483628
      ThreeDOutsideShadowColor=   -2147483632
      ThreeDOutsideWidth=   1
      ThreeDFrameWidth=   0
      BorderStyle     =   1
      BorderColor     =   -2147483642
      BorderWidth     =   1
      ButtonDisable   =   0   'False
      ButtonHide      =   0   'False
      ButtonIncrement =   1
      ButtonMin       =   0
      ButtonMax       =   100
      ButtonStyle     =   0
      ButtonWidth     =   0
      ButtonWrap      =   -1  'True
      ButtonDefaultAction=   -1  'True
      ThreeDText      =   0
      ThreeDTextHighlightColor=   -2147483633
      ThreeDTextShadowColor=   -2147483632
      ThreeDTextOffset=   1
      AlignTextH      =   2
      AlignTextV      =   0
      AllowNull       =   0   'False
      NoSpecialKeys   =   0
      AutoAdvance     =   0   'False
      AutoBeep        =   0   'False
      CaretInsert     =   0
      CaretOverWrite  =   3
      UserEntry       =   0
      HideSelection   =   -1  'True
      InvalidColor    =   -2147483637
      InvalidOption   =   0
      MarginLeft      =   3
      MarginTop       =   3
      MarginRight     =   3
      MarginBottom    =   3
      NullColor       =   -2147483637
      OnFocusAlignH   =   0
      OnFocusAlignV   =   0
      OnFocusNoSelect =   0   'False
      OnFocusPosition =   0
      ControlType     =   0
      Text            =   "Rs:0.00"
      CurrencyDecimalPlaces=   -1
      CurrencyNegFormat=   0
      CurrencyPlacement=   0
      CurrencySymbol  =   ""
      DecimalPoint    =   ""
      FixedPoint      =   -1  'True
      LeadZero        =   0
      MaxValue        =   "9000000000"
      MinValue        =   "-9000000000"
      NegToggle       =   0   'False
      Separator       =   ""
      UseSeparator    =   0   'False
      IncInt          =   1
      IncDec          =   1
      BorderGrayAreaColor=   -2147483637
      ThreeDOnFocusInvert=   0   'False
      ThreeDFrameColor=   -2147483633
      Appearance      =   1
      BorderDropShadow=   0
      BorderDropShadowColor=   -2147483632
      BorderDropShadowWidth=   3
      ButtonColor     =   -2147483633
      AutoMenu        =   0   'False
      ButtonAlign     =   0
      OLEDropMode     =   0
      OLEDragMode     =   0
   End
   Begin Point_of_Sale_System.lvButtons_H cmdCancelOrder 
      Height          =   840
      Left            =   6500
      TabIndex        =   28
      Top             =   9630
      Width           =   2640
      _ExtentX        =   4657
      _ExtentY        =   1482
      Caption         =   "Cancel Order"
      CapAlign        =   2
      BackStyle       =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Corbel"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   -1  'True
         Strikethrough   =   0   'False
      EndProperty
      Mode            =   0
      Value           =   0   'False
      ImgAlign        =   2
      ImgSize         =   32
      cBack           =   -2147483633
   End
   Begin VB.PictureBox Picture2 
      Appearance      =   0  'Flat
      AutoSize        =   -1  'True
      BackColor       =   &H80000005&
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H80000008&
      Height          =   1065
      Left            =   12660
      Picture         =   "frmPOSUK.frx":66F8
      ScaleHeight     =   1035
      ScaleWidth      =   3465
      TabIndex        =   0
      Top             =   9525
      Width           =   3495
   End
   Begin VB.Frame Frame1 
      BackColor       =   &H00EEEEEE&
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   120
      Left            =   -90
      TabIndex        =   5
      Top             =   9405
      Width           =   16335
   End
   Begin VB.Label Label2 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Press [F5] key for search Items"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H00FF00FF&
      Height          =   285
      Left            =   45
      TabIndex        =   33
      Top             =   8055
      Width           =   4185
   End
   Begin VB.Label Label3 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Total Amount:"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   14.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   330
      Left            =   11265
      TabIndex        =   24
      Top             =   8025
      Width           =   1950
   End
   Begin VB.Label Label7 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Barcode:"
      BeginProperty Font 
         Name            =   "Corbel"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   285
      Left            =   135
      TabIndex        =   23
      Top             =   1650
      Width           =   930
   End
   Begin VB.Label Label12 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Invoice ID:"
      BeginProperty Font 
         Name            =   "Corbel"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   285
      Left            =   135
      TabIndex        =   22
      Top             =   1005
      Width           =   1080
   End
   Begin VB.Label Label15 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Email Addr:"
      BeginProperty Font 
         Name            =   "Corbel"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   285
      Left            =   6330
      TabIndex        =   21
      Top             =   1830
      Width           =   1230
   End
   Begin VB.Label lblEmailAddress 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Intelysol"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H00FF00FF&
      Height          =   285
      Left            =   8550
      TabIndex        =   20
      Top             =   1830
      Width           =   1215
   End
   Begin VB.Label lblContactMobile 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "03218287590"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H00FF00FF&
      Height          =   285
      Left            =   13335
      TabIndex        =   18
      Top             =   1380
      Width           =   1485
   End
   Begin VB.Label Label11 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Mobile:"
      BeginProperty Font 
         Name            =   "Corbel"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   285
      Left            =   12195
      TabIndex        =   17
      Top             =   1395
      Width           =   780
   End
   Begin VB.Label lblContactHome 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "02135426362"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H00FF00FF&
      Height          =   285
      Left            =   13320
      TabIndex        =   16
      Top             =   1830
      Width           =   1485
   End
   Begin VB.Label Label10 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Home:"
      BeginProperty Font 
         Name            =   "Corbel"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   285
      Left            =   12195
      TabIndex        =   15
      Top             =   1830
      Width           =   705
   End
   Begin VB.Label lblCompany 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Intelysol"
      BeginProperty Font 
         Name            =   "Consolas"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H00FF00FF&
      Height          =   285
      Left            =   8550
      TabIndex        =   14
      Top             =   1395
      Width           =   1215
   End
   Begin VB.Label Label9 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Company Name:"
      BeginProperty Font 
         Name            =   "Corbel"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   285
      Left            =   6330
      TabIndex        =   13
      Top             =   1395
      Width           =   1740
   End
   Begin VB.Label Label8 
      AutoSize        =   -1  'True
      BackStyle       =   0  'Transparent
      Caption         =   "Customer Name:"
      BeginProperty Font 
         Name            =   "Corbel"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   285
      Left            =   6330
      TabIndex        =   11
      Top             =   915
      Width           =   1770
   End
End
Attribute VB_Name = "frmPOSUK"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit
Dim MemberCode As String
Dim CardNumber As String
Dim DiscountCode As String
Dim DiscountAmount As String
Dim IsPercentage As Boolean
Dim TransactionNo As String
Dim DockItPrints As Integer
Dim EmployeeCode As String
Dim CustomerCode As String
Dim ItemCode As String
Dim ItemName As String
Dim ItemDesc As String
Dim ItemPrice As String

Private Sub cmbCustomerName_Click()
        '<EhHeader>
        On Error GoTo cmbCustomerName_Click_Err
        '</EhHeader>

100     CustomerCode = Format(cmbCustomerName.ItemData(cmbCustomerName.ListIndex), "0###")

102     sSQL = "SELECT * FROM CustomerInformation Where CustomerCode='" & CustomerCode & "'"
104     Set rs = New ADODB.Recordset
106     rs.Open sSQL, Conn, 1, 3

108     If rs.EOF <> True Then
110         lblCompany.Caption = rs("CompanyName")
112         lblContactHome.Caption = rs("ContactHome")
114         lblContactMobile.Caption = rs("ContactMobile")
116         lblEmailAddress.Caption = rs("EmailAddress")
        End If

        '<EhFooter>
        Exit Sub

cmbCustomerName_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.cmbCustomerName_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmbInvNo_Click()
        '<EhHeader>
        On Error GoTo cmbInvNo_Click_Err
        '</EhHeader>
    
100     txtInvID.Text = cmbInvNo.Text
102     Call txtInvID_LostFocus
    
        '<EhFooter>
        Exit Sub

cmbInvNo_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.cmbInvNo_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdClose_Click()
        '<EhHeader>
        On Error GoTo cmdClose_Click_Err
        '</EhHeader>
    
100     Unload Me
    
        '<EhFooter>
        Exit Sub

cmdClose_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.cmdClose_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdNew_Click()
        '<EhHeader>
        On Error GoTo cmdNew_Click_Err
        '</EhHeader>
                   
100     txtInvID.Text = ""
102     txtItemCode.Text = ""
        
104     vaSpread.MaxRows = 0
106     vaSpread.MaxRows = 100
                   
108     cmbCustomerName.Text = "General Customer"
110     lblCompany.Caption = ""
112     lblContactHome.Caption = ""
114     lblContactMobile.Caption = ""
116     lblEmailAddress.Caption = ""
118     lblTransactionDate.Caption = TransactionDate
                   
120     sSQL = "Select Max(TransactionNo) as LastNumber  From Transactions Where TransactionDate='" & TransactionDate & "'"
122     Set rs = New ADODB.Recordset
124     rs.Open sSQL, Conn, 1, 3
        
126     If rs.EOF = True Then
128         txtInvID.Text = Format(1, "0###")
        Else
130         txtInvID.Text = Format(Val(rs("LastNumber") & "") + 1, "0###")
        End If
        
132     IsPercentage = True
134     DiscountAmount = 0
        
136     Call FillcmbInvNo
                
        '<EhFooter>
        Exit Sub

cmdNew_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.cmdNew_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdPayments_Click()
        '<EhHeader>
        On Error GoTo cmdPayments_Click_Err
        '</EhHeader>
                      
100     TransactionNo = txtInvID.Text
                      
102     sSQL = "Delete From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
104     Conn.Execute sSQL

106     sSQL = "Select * From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
108     Set rs = New ADODB.Recordset
110     rs.Open sSQL, Conn, 1, 3
        
112     For i = 1 To vaSpread.DataRowCnt
114         rs.AddNew
116         rs("TransactionNo") = TransactionNo
118         rs("TransactionDate") = TransactionDate
120         rs("PaymentType") = "Cash Sales"
122         rs("TransactionStartTime") = Time
124         rs("TransactionEndTime") = Time
126         rs("UserID") = UserID
128         rs("CustomerCode") = CustomerCode
130         vaSpread.Row = i
132         vaSpread.Col = 1
134         rs("ItemCode") = vaSpread.Text
136         vaSpread.Col = 2
138         rs("ItemName") = vaSpread.Text
140         vaSpread.Col = 4
142         rs("ItemPrice") = vaSpread.Value
144         vaSpread.Col = 5
146         rs("ItemQuantity") = vaSpread.Value
148         rs("DiscountCode") = 0
150         rs("DiscountAmount") = 0
152         rs("TransactionType") = "Cash"
154         rs("TaxRate") = 0
156         rs("TaxAmount") = 0
158         rs("TransactionClose") = True
160         rs("TransactionsVoid") = False
162         rs("VoidDescription") = ""
164         rs.Update
        Next

166     sSQL = "SELECT     Transactions.*, ItemSetup.ItemDesc, CustomerInformation.CustomerName, CustomerInformation.EmailAddress, " & _
           "Transactions.ItemQuantity * Transactions.ItemPrice AS TotalAmount FROM Transactions INNER JOIN ItemSetup " & _
           "ON Transactions.ItemCode = ItemSetup.ItemCode INNER JOIN CustomerInformation ON Transactions.CustomerCode = CustomerInformation.CustomerCode " & _
           "WHERE (Transactions.TransactionNo='" & txtInvID.Text & "') AND (Transactions.TransactionDate='" & TransactionDate & "')"
           
168     sSQL = "SELECT Transactions.*, ItemSetup.ItemDesc, CustomerInformation.CustomerName, CustomerInformation.EmailAddress, CustomerInformation.CompanyName, " & _
           "CustomerInformation.ContactHome, CustomerInformation.ContactMobile, CustomerInformation.Address, Transactions.ItemQuantity * Transactions.ItemPrice AS TotalAmount " & _
           "FROM Transactions INNER JOIN ItemSetup ON Transactions.ItemCode = ItemSetup.ItemCode INNER JOIN CustomerInformation ON Transactions.CustomerCode = CustomerInformation.CustomerCode " & _
           "WHERE (Transactions.TransactionNo='" & txtInvID.Text & "') AND (Transactions.TransactionDate='" & TransactionDate & "')"
170     Set rs = New ADODB.Recordset
172     rs.Open sSQL, Conn, 1, 3
        
174     With repSalesInvoiceUK
176         .Image1.Visible = False
178         .lblCompanyAddress1.Visible = False
180         .lblCompanyAddress2.Visible = False
182         .lblCompanyAddress3.Visible = False
184         .lblCompanyName.Caption = "CASH INOVICE"
186         .lblVATTaxes.Caption = "VAT TAX @ " & SalesTaxRate & " %:"
188         .lblVATTaxes.Visible = False
190         .txtVATAmount.Visible = False
192         .documentName = "Sales Invoice"
194         .DataControl1.Recordset = rs
196         .WindowState = vbMaximized
198         .Show 1
        End With

        '<EhFooter>
        Exit Sub

cmdPayments_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.cmdPayments_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_KeyDown(KeyCode As Integer, _
                         Shift As Integer)
        '<EhHeader>
        On Error GoTo Form_KeyDown_Err
        '</EhHeader>

100     If KeyCode = vbKeyF5 Then
        
102         picSearch.Visible = Not picSearch.Visible
        
104         If picSearch.Visible = True Then
106             txtItemSearch.Text = ""
108             txtItemSearch.SetFocus
            End If
        End If
    
        '<EhFooter>
        Exit Sub

Form_KeyDown_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.Form_KeyDown " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>
    
100     CenterForm Me
                
102     Me.KeyPreview = True
                
104     vaSpread.MaxRows = 0
106     vaSpread.MaxRows = 150

        '108     vaSpread.Formula = "C#*100/" & 100 + Val(SalesTaxRate)
108     FontControl = GetSetting(ApplicationName, "Drawer Settings", "FontName", "FontControl")
110     DrawerPort = GetSetting(ApplicationName, "Drawer Settings", "DrawerPort", "1")
                
112     TransactionDate = Format(TransactionDate, "dd/mmm/yyyy")
114     lblTransactionDate.Caption = "Date: " & TransactionDate
        
116     Call FillcmbCustomerName
        
118     Call cmdNew_Click
        
        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdCancelOrder_Click()
        '<EhHeader>
        On Error GoTo cmdCancelOrder_Click_Err
        '</EhHeader>
100     If Len(Trim$(txtInvID.Text)) = 0 Then Exit Sub
    
102     sSQL = "Select * From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
104     Set rs = New ADODB.Recordset
106     rs.Open sSQL, Conn, 1, 3

108     If rs.EOF <> True Then
            
110         If MsgBox("Are you sure to Cancel this Order ?", vbYesNo + vbQuestion, "Confirm") = vbYes Then
112             sSQL = "Delete From Transactions Where TransactionNo='" & TransactionNo & "' And TransactionDate='" & TransactionDate & "'"
114             Conn.Execute sSQL
            End If
        End If
    
116     Call cmdNew_Click

        '<EhFooter>
        Exit Sub

cmdCancelOrder_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.cmdCancelOrder_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtInvID_LostFocus()
        '<EhHeader>
        On Error GoTo txtInvID_LostFocus_Err
        '</EhHeader>

100     txtInvID.Text = Format(txtInvID.Text, "0###")

102     Call Update_Sparead
    
104     Call CalculateBill

        '<EhFooter>
        Exit Sub

txtInvID_LostFocus_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.txtInvID_LostFocus " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtItemCode_LostFocus()
        '<EhHeader>
        On Error GoTo txtItemCode_LostFocus_Err
        '</EhHeader>
    
100     ItemCode = ""
102     ItemName = ""
104     ItemDesc = ""
106     ItemPrice = ""
    
108     If Len(Trim(txtInvID.Text)) = 0 Then Exit Sub
    
110     If Len(Trim(txtItemCode.Text)) = 0 Then Exit Sub
        
112     sSQL = "SELECT * From ItemSetup WHERE ItemCode='" & txtItemCode.Text & "'"

114     If Conn.State = adStateClosed Then Conn.Open
116     Set rs = New ADODB.Recordset
118     rs.Open sSQL, Conn, 1, 3
    
120     If rs.EOF <> True Then
122         ItemCode = rs("ItemCode") & ""
124         ItemName = rs("ItemName") & ""
126         ItemDesc = rs("ItemDesc") & ""
128         ItemPrice = Val(rs("Price") & "")
        Else
            Exit Sub
        End If
    
        'Check the existing transaction for the itemcode
130     sSQL = "SELECT * From Transactions WHERE (TransactionDate='" & TransactionDate & "') AND (TransactionNo = '" & txtInvID.Text & "') AND (ItemCode='" & txtItemCode.Text & "')"
132     Set rs = New ADODB.Recordset
134     rs.Open sSQL, Conn, 1, 3
    
136     If rs.EOF = True Then
138         rs.AddNew
140         rs("TransactionNo") = txtInvID.Text
142         rs("TransactionDate") = TransactionDate
144         rs("UserID") = UserID
146         rs("Customers") = 1
148         rs("CustomerCode") = CustomerCode
150         rs("TransactionType") = "Sales"
152         rs("ItemCode") = ItemCode
154         rs("ItemName") = ItemName
156         rs("ItemPrice") = ItemPrice
158         rs("ItemQuantity") = 1
160         rs("DiscountCode") = 1
162         rs("DiscountAmount") = 0
164         rs("TaxRate") = SalesTaxRate
166         rs("TaxAmount") = 0
168         rs("PaymentType") = "Cash Sales"
170         rs("TransactionClose") = 1
172         rs("TransactionsVoid") = 0
174         rs("TransactionStartTime") = Now
176         rs("TransactionEndTime") = Now
178         rs("DiscountName") = "No Discount"
        Else
180         rs("CustomerCode") = CustomerCode
182         rs("ItemQuantity") = Val(rs("ItemQuantity")) + 1
        End If

184     rs.Update
186     rs.Close
    
188     Call Update_Sparead
    
        '<EhFooter>
        Exit Sub

txtItemCode_LostFocus_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.txtItemCode_LostFocus " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Update_Sparead()
        '<EhHeader>
        On Error GoTo Update_Sparead_Err
        '</EhHeader>

100     sSQL = "SELECT Transactions.*, CustomerName FROM Transactions INNER JOIN CustomerInformation ON Transactions.CustomerCode = CustomerInformation.CustomerCode " & _
           " WHERE (TransactionNo='" & txtInvID.Text & "') AND (TransactionDate='" & TransactionDate & "')"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     vaSpread.MaxRows = 0
108     vaSpread.MaxRows = 100
110     vaSpread.Row = 1

112     Do Until rs.EOF
114         CustomerCode = rs("CustomerName")
116         vaSpread.Col = 1
118         vaSpread.Text = rs("ItemCode")
120         vaSpread.Col = 2
122         vaSpread.Text = rs("ItemName")
124         vaSpread.Col = 3
126         vaSpread.Text = GetItemDesc(rs("ItemCode"))
128         vaSpread.Col = 4
130         vaSpread.Text = rs("ItemPrice")
132         vaSpread.Col = 5
134         vaSpread.Text = rs("ItemQuantity")
136         vaSpread.Col = 6
138         vaSpread.Row = vaSpread.Row + 1
140         rs.MoveNext
        Loop
        
142     If Len(Trim(CustomerCode)) > 4 Then
144         cmbCustomerName.Text = Trim(CustomerCode)
146         cmbCustomerName_Click
        End If

        '<EhFooter>
        Exit Sub

Update_Sparead_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.Update_Sparead " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Function GetItemDesc(strItemCode As String)
        '<EhHeader>
        On Error GoTo GetItemDesc_Err
        '</EhHeader>

        Dim rsItemDesc As New ADODB.Recordset
100     sSQL = "Select * From ItemSetup Where ItemCode='" & strItemCode & "'"
102     rsItemDesc.Open sSQL, Conn, 1, 3
    
104     If Not rsItemDesc.EOF = True Then
106         GetItemDesc = rsItemDesc("ItemDesc") & ""
        End If

        '<EhFooter>
        Exit Function

GetItemDesc_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.GetItemDesc " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Function

Private Sub txtItemDesc_Change()
        '<EhHeader>
        On Error GoTo txtItemDesc_Change_Err
        '</EhHeader>
    
100     Call SearchItem
    
        '<EhFooter>
        Exit Sub

txtItemDesc_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.txtItemDesc_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtItemSearch_Change()
        '<EhHeader>
        On Error GoTo txtItemSearch_Change_Err
        '</EhHeader>
    
100     Call SearchItem
    
        '<EhFooter>
        Exit Sub

txtItemSearch_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.txtItemSearch_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub SearchItem()
        '<EhHeader>
        On Error GoTo SearchItem_Err
        '</EhHeader>

100     If Len(Trim(txtItemSearch.Text)) = 0 And Len(Trim(txtItemDesc.Text)) = 0 And Len(Trim(txtPrice.Text)) = 0 Then
102         sSQL = "Select * From ItemSetup "
        Else
104         sSQL = "Select * From ItemSetup Where (ItemName Like '%" & txtItemSearch.Text & "%')"
        End If
    
106     If Len(Trim(txtItemDesc.Text)) <> 0 Then
108         sSQL = sSQL & " AND (ItemDesc Like '%" & txtItemDesc.Text & "%')"
        End If

110     If Len(Trim(txtPrice.Text)) <> 0 Then
112         sSQL = sSQL & " AND (CONVERT(varchar(20), Price) LIKE '%" & txtPrice.Text & "%')"
        End If
    
114     Set rs = New ADODB.Recordset
116     rs.Open sSQL, Conn, 1, 3
    
118     vaSpreadSearch.MaxRows = 0
120     vaSpreadSearch.MaxRows = 1
122     vaSpreadSearch.Row = 1

124     Do Until rs.EOF
126         vaSpreadSearch.Col = 1
128         vaSpreadSearch.Text = rs("ItemCode")
130         vaSpreadSearch.Col = 2
132         vaSpreadSearch.Text = rs("ItemName") & ""
134         vaSpreadSearch.Col = 3
136         vaSpreadSearch.Text = rs("ItemDesc") & ""
138         vaSpreadSearch.Col = 4
140         vaSpreadSearch.Text = Val(rs("Price") & "")
142         vaSpreadSearch.MaxRows = vaSpreadSearch.MaxRows + 1
144         vaSpreadSearch.Row = vaSpreadSearch.Row + 1
146         rs.MoveNext
        Loop

148     vaSpreadSearch.MaxRows = vaSpreadSearch.DataRowCnt
    
        '<EhFooter>
        Exit Sub

SearchItem_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.SearchItem " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtItemSearch_KeyPress(KeyAscii As Integer)
        '<EhHeader>
        On Error GoTo txtItemSearch_KeyPress_Err
        '</EhHeader>

100     If KeyAscii = 13 Then
102         vaSpreadSearch.Col = 1

104         If vaSpreadSearch.Row > 0 Then
106             vaSpreadSearch.Row = vaSpreadSearch.ActiveRow
            Else
108             vaSpreadSearch.Row = 1
            End If

110         txtItemCode.Text = vaSpreadSearch.Text
112         txtItemCode_LostFocus
114         picSearch.Visible = False
116         txtItemCode.Text = ""
        End If

        '<EhFooter>
        Exit Sub

txtItemSearch_KeyPress_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.txtItemSearch_KeyPress " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtPrice_Change()
        '<EhHeader>
        On Error GoTo txtPrice_Change_Err
        '</EhHeader>

100     Call SearchItem

        '<EhFooter>
        Exit Sub

txtPrice_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.txtPrice_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread_ButtonClicked(ByVal Col As Long, _
                                   ByVal Row As Long, _
                                   ByVal ButtonDown As Integer)
        '<EhHeader>
        On Error GoTo vaSpread_ButtonClicked_Err
        '</EhHeader>
    
100     vaSpread.Row = vaSpread.ActiveRow
102     vaSpread.Action = ActionDeleteRow
        
        '<EhFooter>
        Exit Sub

vaSpread_ButtonClicked_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.vaSpread_ButtonClicked " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub CalculateBill()
        '<EhHeader>
        On Error GoTo CalculateBill_Err
        '</EhHeader>
    
        Dim TotalAmount As String
        Dim TotalTaxAmount As String
        
100     TotalAmount = 0

102     For i = 1 To vaSpread.DataRowCnt
104         vaSpread.Row = i
106         vaSpread.Col = 6
108         TotalAmount = Val(TotalAmount) + Val(vaSpread.Value & "")
        Next

110     txtTotalAmount.Text = TotalAmount
         
        '126     txtSalesTaxAmount.Text = (TotalAmount * SalesTaxRate) / 100

        '128     txtGrandTotal.Text = Val(txtTotalAmount.Value) + Val(txtSalesTaxAmount.Value)
    
        '<EhFooter>
        Exit Sub

CalculateBill_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.CalculateBill " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpread_LeaveCell(ByVal Col As Long, _
                               ByVal Row As Long, _
                               ByVal NewCol As Long, _
                               ByVal NewRow As Long, _
                               Cancel As Boolean)
        '<EhHeader>
        On Error GoTo vaSpread_LeaveCell_Err
        '</EhHeader>
        
100     CalculateBill

        '<EhFooter>
        Exit Sub

vaSpread_LeaveCell_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.vaSpread_LeaveCell " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Sub FillcmbCustomerName()
        '<EhHeader>
        On Error GoTo FillcmbCustomerName_Err
        '</EhHeader>
    
100     sSQL = "SELECT * FROM CustomerInformation"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     cmbCustomerName.Clear

108     Do Until rs.EOF
            
110         cmbCustomerName.AddItem rs("CustomerName")
112         cmbCustomerName.ItemData(cmbCustomerName.NewIndex) = rs("CustomerCode")
114         rs.MoveNext
            
        Loop
    
116     If cmbCustomerName.ListCount > 0 Then cmbCustomerName.Text = "General Customer"

        '<EhFooter>
        Exit Sub

FillcmbCustomerName_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.FillcmbCustomerName " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub FillcmbInvNo()
        '<EhHeader>
        On Error GoTo FillcmbInvNo_Err
        '</EhHeader>
    
100     sSQL = "SELECT DISTINCT TransactionNo From Transactions WHERE (TransactionDate='" & TransactionDate & "')  And PaymentType='Cash Sales'"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
    
106     cmbInvNo.Clear

108     Do Until rs.EOF
110         cmbInvNo.AddItem rs("TransactionNo")
112         rs.MoveNext
        Loop

        '<EhFooter>
        Exit Sub

FillcmbInvNo_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.FillcmbInvNo " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub vaSpreadSearch_DblClick(ByVal Col As Long, _
                                    ByVal Row As Long)
        '<EhHeader>
        On Error GoTo vaSpreadSearch_DblClick_Err
        '</EhHeader>

100     vaSpreadSearch.Col = 1

102     If vaSpreadSearch.Row > 0 Then
104         vaSpreadSearch.Row = Row
        Else
106         vaSpreadSearch.Row = 1
        End If

108     txtItemCode.Text = vaSpreadSearch.Text
110     txtItemCode_LostFocus
112     picSearch.Visible = False
114     txtItemCode.Text = ""

        '<EhFooter>
        Exit Sub

vaSpreadSearch_DblClick_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.frmPOSUK.vaSpreadSearch_DblClick " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

