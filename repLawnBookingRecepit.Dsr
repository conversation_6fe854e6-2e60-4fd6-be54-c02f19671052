VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} repLawnBookingRecepit 
   Caption         =   "repLawnBookingRecepit (ActiveReport)"
   ClientHeight    =   10620
   ClientLeft      =   0
   ClientTop       =   390
   ClientWidth     =   15360
   _ExtentX        =   27093
   _ExtentY        =   18733
   SectionData     =   "repLawnBookingRecepit.dsx":0000
End
Attribute VB_Name = "repLawnBookingRecepit"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub Detail_Format()
        '<EhHeader>
        On Error GoTo Detail_Format_Err
        '</EhHeader>
        
        Dim BookingAmount As String
        
100     sSQL = "SELECT BookingAmount From BookingInformation WHERE (BookingID='" & txtBookingID.Text & "') AND (TransactionType='" & Left(TransactionType, 1) & "B')"
102     Set rs = New ADODB.Recordset
104     rs.Open sSQL, Conn, 1, 3
106     BookingAmount = Val(rs("BookingAmount"))

108     sSQL = "SELECT SUM(RecievedAmount) AS RecievedAmount From BookingInformation WHERE (BookingID='" & txtBookingID.Text & "') AND (TransactionType='" & Left(TransactionType, 1) & "P')"
110     Set rs = New ADODB.Recordset
112     rs.Open sSQL, Conn, 1, 3

114     txtBalanceAmount.DataValue = Val(BookingAmount) - rs("RecievedAmount")

116     txtInWords.Text = StrConv(English(txtRecievedAmount.DataValue), vbProperCase)
118     txtDay.Text = WeekdayName(Weekday(txtEventDate.Text))
    
        '<EhFooter>
        Exit Sub

Detail_Format_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.repLawnBookingRecepit.Detail_Format " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
