VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} repLawnBookingFromPrint 
   Caption         =   "Point_of_Sale_System - repLawnBookingFromPrint (ActiveReport)"
   ClientHeight    =   10620
   ClientLeft      =   0
   ClientTop       =   390
   ClientWidth     =   15360
   _ExtentX        =   27093
   _ExtentY        =   18733
   SectionData     =   "repLawnBookingFromPrint.dsx":0000
End
Attribute VB_Name = "repLawnBookingFromPrint"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub Detail_Format()
        '<EhHeader>
        On Error GoTo Detail_Format_Err
        '</EhHeader>


112     txtDay.Text = WeekdayName(Weekday(txtEventDate.Text))
        
        '<EhFooter>
        Exit Sub

Detail_Format_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Point_of_Sale_System.repLawnBookingFromPrint.Detail_Format " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
