VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} repCostSheet 
   Caption         =   "repCostSheet (ActiveReport)"
   ClientHeight    =   10620
   ClientLeft      =   0
   ClientTop       =   390
   ClientWidth     =   15360
   _ExtentX        =   27093
   _ExtentY        =   18733
   SectionData     =   "repCostSheet.dsx":0000
End
Attribute VB_Name = "repCostSheet"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub CostSheetFooter_BeforePrint()
    
    'On Error Resume Next
    Dim rsBookingInfo As New ADODB.Recordset
    Dim BookingAmount As String
    Dim RecievedAmount As String
    
    txtAvgPrice.DataValue = txtTotalSellingAmount.DataValue / txtNoofPersons.DataValue
    
    txtAvgCost.DataValue = txtTotalCostingAmount.DataValue / txtNoofPersons.DataValue
    
    txtAvgProfitPH.DataValue = txtTotalProfit.DataValue / txtNoofPersons.DataValue
    
    txtProfitPercentage.DataValue = txtAvgPrice.DataValue - txtAvgCost.DataValue / txtAvgPrice.DataValue * 100
    
    'Booking Amount Calcultion
    sSQL = "SELECT SUM(BookingAmount) AS BookingAmount From BookingInformation " & _
       "WHERE (TransactionType = N'CB' OR TransactionType = N'DB') AND (BookingID='" & txtBookingID.Text & "')"
    Set rsBookingInfo = New ADODB.Recordset
    rsBookingInfo.Open sSQL, Conn, 1, 3
    
    BookingAmount = Val(rsBookingInfo("BookingAmount") & "")
    
    'Recieved Amount Calculation
    sSQL = "SELECT SUM(RecievedAmount) AS RecievedAmount From BookingInformation " & _
       "WHERE (TransactionType = N'CP' OR TransactionType = N'DP') AND (BookingID='" & txtBookingID.Text & "')"
    Set rsBookingInfo = New ADODB.Recordset
    rsBookingInfo.Open sSQL, Conn, 1, 3
    
    RecievedAmount = Val(rsBookingInfo("RecievedAmount") & "")
    
    txtBookingAmount.DataValue = BookingAmount
    txtRecievedAmount.DataValue = RecievedAmount
    
    'Remaining Amount Calculation
    txtRemainingAmount.DataValue = Val(BookingAmount) - Val(RecievedAmount)
    
End Sub

Private Sub CostSheetHeader_Format()
    On Error Resume Next
    
    Dim rsBookingInfo As New ADODB.Recordset
    sSQL = "SELECT BookingInformation.CustomerName, BookingInformation.ContactPerson, LawnSetup.LawnName, BookingInformation.TotalPersons FROM BookingInformation " & _
       "INNER JOIN LawnSetup ON BookingInformation.LawnID = LawnSetup.LawnID WHERE (BookingInformation.BookingID='" & txtBookingID.Text & "') AND (BookingInformation.PaymentID IS NULL)"
    Set rsBookingInfo = New ADODB.Recordset
    rsBookingInfo.Open sSQL, Conn, 1, 3
    
    txtCustomerName.Text = rsBookingInfo("CustomerName")
    txtLawnName.Text = rsBookingInfo("LawnName")
    txtNoofPersons.DataValue = Val(rsBookingInfo("TotalPersons"))
    
End Sub
