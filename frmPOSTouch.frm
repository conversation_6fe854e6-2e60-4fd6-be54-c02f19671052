VERSION 5.00
Object = "{B02F3647-766B-11CE-AF28-C3A2FBE76A13}#3.0#0"; "SPR32X30.ocx"
Begin VB.Form frmPOSTouch 
   Caption         =   "Form1"
   ClientHeight    =   11670
   ClientLeft      =   255
   ClientTop       =   1500
   ClientWidth     =   18900
   LinkTopic       =   "Form1"
   ScaleHeight     =   11670
   ScaleWidth      =   18900
   Begin FPSpread.vaSpread vaSpread1 
      Height          =   10020
      Left            =   150
      TabIndex        =   0
      Top             =   1050
      Width           =   2925
      _Version        =   196608
      _ExtentX        =   5159
      _ExtentY        =   17674
      _StockProps     =   64
      ColHeaderDisplay=   0
      DisplayColHeaders=   0   'False
      DisplayRowHeaders=   0   'False
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   11.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   2
      MaxRows         =   10
      ScrollBars      =   0
      SelectBlockOptions=   0
      SpreadDesigner  =   "frmPOSTouch.frx":0000
      VisibleCols     =   1
   End
   Begin FPSpread.vaSpread vaSpread2 
      Height          =   10020
      Left            =   3180
      TabIndex        =   1
      Top             =   1080
      Width           =   8235
      _Version        =   196608
      _ExtentX        =   14526
      _ExtentY        =   17674
      _StockProps     =   64
      ColHeaderDisplay=   0
      DisplayColHeaders=   0   'False
      DisplayRowHeaders=   0   'False
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Consolas"
         Size            =   11.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   5
      MaxRows         =   7
      ScrollBars      =   0
      SelectBlockOptions=   0
      SpreadDesigner  =   "frmPOSTouch.frx":0493
      UserResize      =   0
      VisibleCols     =   1
   End
   Begin FPSpread.vaSpread vaSpread3 
      Height          =   5190
      Left            =   11505
      TabIndex        =   2
      Top             =   1065
      Width           =   7125
      _Version        =   196608
      _ExtentX        =   12568
      _ExtentY        =   9155
      _StockProps     =   64
      EditEnterAction =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      MaxCols         =   9
      MaxRows         =   20
      ScrollBars      =   2
      SpreadDesigner  =   "frmPOSTouch.frx":0D30
   End
End
Attribute VB_Name = "frmPOSTouch"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Private Sub Form_Load()

    'Item Category Grid
    
    'Select a single cell
    vaSpread1.Col = 1
    vaSpread1.Row = 1

    'Define cells as type BUTTON
    vaSpread1.CellType = CellTypeButton
    vaSpread1.TypeButtonColor = RGB(192, 192, 192)
    vaSpread1.TypeButtonTextColor = RGB(0, 0, 0)
    vaSpread1.TypeButtonDarkColor = RGB(128, 128, 128)
    vaSpread1.TypeButtonLightColor = RGB(255, 255, 255)
    vaSpread1.TypeButtonBorderColor = RGB(0, 0, 0)
    vaSpread1.TypeButtonPicture = LoadPicture("")
    vaSpread1.TypeButtonPictureDown = LoadPicture("")
    vaSpread1.TypeButtonText = "Button1"
    vaSpread1.TypeButtonShadowSize = 1
    vaSpread1.TypeButtonType = TypeButtonTypeNormal
    vaSpread1.TypeButtonAlign = TypeButtonAlignBottom



    'Item's Buttons
    
    'Select column(s)
    vaSpread2.Col = 1
    vaSpread2.Row = 1

    'Define cells as type BUTTON
    vaSpread2.CellType = CellTypeButton
    vaSpread2.TypeButtonColor = RGB(192, 192, 192)
    vaSpread2.TypeButtonTextColor = RGB(0, 0, 0)
    vaSpread2.TypeButtonDarkColor = RGB(128, 128, 128)
    vaSpread2.TypeButtonLightColor = RGB(255, 255, 255)
    vaSpread2.TypeButtonBorderColor = RGB(0, 0, 0)
    vaSpread2.TypeButtonPicture = LoadPicture("C:\DOCUMENTS AND SETTINGS\COMPUTER\DESKTOP\PROJECTS\POS 03-OCT-2014\CROSS.BMP")
    vaSpread2.TypeButtonPictureDown = LoadPicture("")
    vaSpread2.TypeButtonText = "Button"
    vaSpread2.TypeButtonShadowSize = 1
    vaSpread1.TypeButtonType = TypeButtonTypeNormal
    vaSpread1.TypeButtonAlign = TypeButtonAlignBottom
    

End Sub
