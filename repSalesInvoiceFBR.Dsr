VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} repSalesInvoiceFBR 
   Caption         =   "Point_of_Sale_System - repSalesInvoiceFBR (ActiveReport)"
   ClientHeight    =   12555
   ClientLeft      =   0
   ClientTop       =   285
   ClientWidth     =   19575
   _ExtentX        =   34528
   _ExtentY        =   22146
   SectionData     =   "repSalesInvoiceFBR.dsx":0000
End
Attribute VB_Name = "repSalesInvoiceFBR"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False

Private Sub GroupFooter1_BeforePrint()
    
    'txtTaxableAmount.DataValue = txtInvTotalAmount.DataValue + txtTaxAmount.DataValue
    
    'txtGrandTotal.DataValue = Round(txtInvTotalAmount.DataValue + txtTaxAmount.DataValue)
    
    lblInWords.Caption = StrConv(English(txtGrandTotal.DataValue), vbProperCase)
    
        '<EhHeader>
    
End Sub

Private Sub GroupHeader1_Format()
        On Error GoTo GroupHeader1_Format_Err
        '</EhHeader>

        Dim strQRCode As String
100     strQRCode = "C:\QRCodes\" & txtFBRInvoiceNumber.DataValue & ".jpg"
    
102     If Len(Dir(strQRCode)) > 0 Then
104         imgQRCode.Picture = LoadPicture("C:\QRCodes\" & txtFBRInvoiceNumber.DataValue & ".jpg")
        Else
106         imgQRCode.Picture = LoadPicture("")
        End If

        '<EhFooter>
        Exit Sub

GroupHeader1_Format_Err:
        MsgBox Err.Description & vbCrLf & _
           "in Point_of_Sale_System.repSalesInvoiceFBR.GroupHeader1_Format " & _
           "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub
