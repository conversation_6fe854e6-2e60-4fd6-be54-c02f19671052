VERSION 5.00
Begin VB.Form frmChangePassword 
   BackColor       =   &H00E0E0E0&
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "Change Password"
   ClientHeight    =   2295
   ClientLeft      =   6045
   ClientTop       =   4890
   ClientWidth     =   4575
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "frmChangePassword.frx":0000
   LinkTopic       =   "Form1"
   LockControls    =   -1  'True
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   2295
   ScaleWidth      =   4575
   ShowInTaskbar   =   0   'False
   Begin VB.Frame Frame3 
      BackColor       =   &H00EAD08E&
      Height          =   525
      Left            =   0
      TabIndex        =   10
      Top             =   -90
      Width           =   4560
      Begin VB.Label Label4 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Change Password"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   15.75
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   -1  'True
            Strikethrough   =   0   'False
         EndProperty
         Height          =   375
         Left            =   45
         TabIndex        =   11
         Top             =   105
         Width           =   2805
      End
   End
   Begin VB.Frame Frame1 
      BackColor       =   &H00E0E0E0&
      Height          =   1350
      Left            =   0
      TabIndex        =   6
      Top             =   375
      Width           =   4560
      Begin VB.TextBox txtOldPassword 
         Appearance      =   0  'Flat
         Height          =   285
         IMEMode         =   3  'DISABLE
         Left            =   1755
         MaxLength       =   15
         PasswordChar    =   "*"
         TabIndex        =   0
         Text            =   "Password"
         Top             =   225
         Width           =   2715
      End
      Begin VB.TextBox txtNewPassword1 
         Appearance      =   0  'Flat
         Height          =   285
         IMEMode         =   3  'DISABLE
         Left            =   1755
         MaxLength       =   15
         PasswordChar    =   "*"
         TabIndex        =   1
         Top             =   555
         Width           =   2715
      End
      Begin VB.TextBox txtConfirmPassword 
         Appearance      =   0  'Flat
         Height          =   285
         IMEMode         =   3  'DISABLE
         Left            =   1755
         MaxLength       =   15
         PasswordChar    =   "*"
         TabIndex        =   2
         Top             =   900
         Width           =   2715
      End
      Begin VB.Label Label1 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Old &Password:"
         Height          =   195
         Left            =   75
         TabIndex        =   9
         Top             =   270
         Width           =   1230
      End
      Begin VB.Label Label2 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "&New Password:"
         Height          =   195
         Left            =   75
         TabIndex        =   8
         Top             =   600
         Width           =   1305
      End
      Begin VB.Label Label3 
         AutoSize        =   -1  'True
         BackStyle       =   0  'Transparent
         Caption         =   "Confir&m Password:"
         Height          =   195
         Left            =   75
         TabIndex        =   7
         Top             =   945
         Width           =   1635
      End
   End
   Begin VB.Frame Frame2 
      BackColor       =   &H00E0E0E0&
      Height          =   630
      Left            =   0
      TabIndex        =   5
      Top             =   1665
      Width           =   4560
      Begin VB.CommandButton cmdCancel 
         Cancel          =   -1  'True
         Caption         =   "&Cancel"
         Height          =   375
         Left            =   2490
         TabIndex        =   4
         Top             =   165
         Width           =   870
      End
      Begin VB.CommandButton cmdOk 
         Caption         =   "&OK"
         Height          =   375
         Left            =   1275
         TabIndex        =   3
         Top             =   165
         Width           =   870
      End
   End
End
Attribute VB_Name = "frmChangePassword"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub cmdCancel_Click()
        '<EhHeader>
        On Error GoTo cmdCancel_Click_Err
        '</EhHeader>

100     Unload Me

        '<EhFooter>
        Exit Sub

cmdCancel_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Payroll_System.frmChangePassword.cmdCancel_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub cmdOk_Click()
        '<EhHeader>
        On Error GoTo cmdOk_Click_Err
        '</EhHeader>

        Dim strHashString As New CSHA256

100     If Len(Trim$(txtNewPassword1.Text)) = 0 Or Len(Trim$(txtConfirmPassword.Text)) = 0 Then

102         MsgBox "Please enter the both password correctly...", vbOKOnly + vbCritical, "Error!"
104         txtNewPassword1.SetFocus
            Exit Sub

        End If

106     If txtNewPassword1.Text <> txtConfirmPassword.Text Then

108         MsgBox "New Password and Confirm Password are not same please re enter the new password", vbOKOnly + vbCritical, "Error!"
110         txtNewPassword1.SetFocus
            Exit Sub

        End If
        
112     Set rs = New ADODB.Recordset
114     sSQL = "Select * From UserMaster Where UserID='" & UserID & "'"
116     rs.Open sSQL, Conn, 1, 3

118     If rs.EOF = False Then

120         If rs("Password") = strHashString.SHA256("\" & txtOldPassword.Text & "/") Then

122             rs("Password") = strHashString.SHA256("\" & txtNewPassword1.Text & "/")
124             rs.Update
126             MsgBox "Your Password is sucessfully changed...", vbOKOnly + vbInformation, "Password Change"

            Else

128             MsgBox "Your Old Password is not correct Please try again ...", vbOKOnly + vbCritical, "Error!"
130             txtOldPassword.SetFocus
                Exit Sub

            End If

        Else

132         MsgBox "Your UserID is not found in Database...", vbOKOnly + vbCritical, "Error!"
134         txtOldPassword.SetFocus

        End If

136     rs.Close
138     Unload Me

        '<EhFooter>
        Exit Sub

cmdOk_Click_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Payroll_System.frmChangePassword.cmdOk_Click " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub Form_Load()
        '<EhHeader>
        On Error GoTo Form_Load_Err
        '</EhHeader>

100     CenterForm Me
102     txtOldPassword.Text = ""
104     txtNewPassword1.Text = ""
106     txtConfirmPassword.Text = ""

        '<EhFooter>
        Exit Sub

Form_Load_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Payroll_System.frmChangePassword.Form_Load " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtConfirmPassword_Change()
        '<EhHeader>
        On Error GoTo txtConfirmPassword_Change_Err
        '</EhHeader>

100     Call chkTextBoxes

        '<EhFooter>
        Exit Sub

txtConfirmPassword_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Payroll_System.frmChangePassword.txtConfirmPassword_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtNewPassword1_Change()
        '<EhHeader>
        On Error GoTo txtNewPassword1_Change_Err
        '</EhHeader>

100     Call chkTextBoxes

        '<EhFooter>
        Exit Sub

txtNewPassword1_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Payroll_System.frmChangePassword.txtNewPassword1_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub txtOldPassword_Change()
        '<EhHeader>
        On Error GoTo txtOldPassword_Change_Err
        '</EhHeader>

100     Call chkTextBoxes

        '<EhFooter>
        Exit Sub

txtOldPassword_Change_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Payroll_System.frmChangePassword.txtOldPassword_Change " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

Private Sub chkTextBoxes()
        '<EhHeader>
        On Error GoTo chkTextBoxes_Err
        '</EhHeader>

100     If Len(txtOldPassword.Text) = 0 Or Len(txtNewPassword1.Text) = 0 Or Len(txtConfirmPassword.Text) = 0 Then

102         cmdOk.Enabled = False

        Else

104         cmdOk.Enabled = True

        End If

        '<EhFooter>
        Exit Sub

chkTextBoxes_Err:
        MsgBox Err.Description & vbCrLf & _
               "in Payroll_System.frmChangePassword.chkTextBoxes " & _
               "at line " & Erl
        Resume Next
        '</EhFooter>
End Sub

