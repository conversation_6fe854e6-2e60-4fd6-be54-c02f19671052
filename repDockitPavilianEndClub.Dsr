VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} repDockitPavilianEndClub 
   Caption         =   "Point_of_Sale_System - repDockitPavilianEndClub (ActiveReport)"
   ClientHeight    =   10680
   ClientLeft      =   0
   ClientTop       =   390
   ClientWidth     =   15360
   _ExtentX        =   27093
   _ExtentY        =   18838
   SectionData     =   "repDockitPavilianEndClub.dsx":0000
End
Attribute VB_Name = "repDockitPavilianEndClub"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub Detail_BeforePrint()
    
    Line6.Y2 = Detail.Height
    Line7.Y2 = Detail.Height
    Line8.Y2 = Detail.Height
    
End Sub

Private Sub GroupFooter1_Format()
    
    txtGrandTotal.DataValue = Val(txtSumOfAmount.DataValue) + Val(txtTaxAmount.DataValue) - Val(txtDiscountAmount.DataValue)

End Sub
